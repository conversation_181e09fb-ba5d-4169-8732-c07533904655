#include "flutter_window.h"
#include <optional>
#include "flutter/generated_plugin_registrant.h"
#include <windows.h>

// Глобальная переменная для хранения мьютекса
HANDLE g_hMutex = nullptr;

FlutterWindow::FlutterWindow(const flutter::DartProject& project)
    : project_(project) {}

FlutterWindow::~FlutterWindow() {}

bool FlutterWindow::OnCreate() {
  // Проверка на наличие мьютекса
  g_hMutex = CreateMutex(nullptr, TRUE, L"ProlesferoSphereMutex");
  if (GetLastError() == ERROR_ALREADY_EXISTS) {
    // Если мьютекс уже существует, активируем существующее окно и завершаем текущий экземпляр
    HWND hwnd = FindWindow(nullptr, L"sphere.exe");
    if (hwnd) {
      ShowWindow(hwnd, SW_RESTORE); 
      SetForegroundWindow(hwnd);    
    }
    return false;
  }

  if (!Win32Window::OnCreate()) {
    return false;
  }

  RECT frame = GetClientArea();

  flutter_controller_ = std::make_unique<flutter::FlutterViewController>(
      frame.right - frame.left, frame.bottom - frame.top, project_);
  if (!flutter_controller_->engine() || !flutter_controller_->view()) {
    return false;
  }
  RegisterPlugins(flutter_controller_->engine());
  SetChildContent(flutter_controller_->view()->GetNativeWindow());

  this->Show();
  flutter_controller_->ForceRedraw();

  return true;
}

void FlutterWindow::OnDestroy() {
  if (flutter_controller_) {
    flutter_controller_ = nullptr;
  }

  // Освобождаем мьютекс при завершении приложения
  if (g_hMutex) {
    ReleaseMutex(g_hMutex);
    CloseHandle(g_hMutex);
  }

  Win32Window::OnDestroy();
}

LRESULT
FlutterWindow::MessageHandler(HWND hwnd, UINT const message,
                              WPARAM const wparam,
                              LPARAM const lparam) noexcept {
  if (flutter_controller_) {
    std::optional<LRESULT> result =
        flutter_controller_->HandleTopLevelWindowProc(hwnd, message, wparam,
                                                      lparam);
    if (result) {
      return *result;
    }
  }

  switch (message) {
    case WM_FONTCHANGE:
      flutter_controller_->engine()->ReloadSystemFonts();
      break;
  }

  return Win32Window::MessageHandler(hwnd, message, wparam, lparam);
}