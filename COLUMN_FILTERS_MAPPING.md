# Соответствие колонок и фильтров

## Структура колонок (из column.dart)

| Индекс | Название колонки | Тип фильтра | Поле фильтра | Статус |
|--------|------------------|-------------|--------------|--------|
| 0 | Дата ввода | Дата | dateFrom/dateTo | ✅ |
| 1 | Номер чертежа | Текст/Список | drawingNumber/drawingNumbers | ✅ |
| 2 | Название | Текст/Список | name/names | ✅ |
| 3 | Материал | Текст/Список | material/materials | ✅ |
| 4 | Признак | Список | features | ✅ |
| 5 | Масса ед., кг | Диапазон | massFrom/massTo | ✅ |
| 6 | Количество | Диапазон | quantityFrom/quantityTo | ✅ |
| 7 | Масса, кг | Диапазон | totalMassFrom/totalMassTo | ✅ |
| 8 | Примечание | Текст/Список | requirement/requirements | ✅ |
| 9 | Доп. тр. к материалу | Текст/Список | materialRequirement/materialRequirements | ✅ |
| 10 | Узел | Текст/Список | parentName/parentNames | ✅ |
| 11 | Номер сборки | Текст/Список | drawingNumber/drawingNumbers | ✅ |
| 12 | Приоритет | Текст/Список | priority/priorities | ✅ |
| 13 | Ответственный | Текст/Список | responsiblePerson/responsiblePersons | ✅ |
| 14 | Дата задачи | Дата | taskDateFrom/taskDateTo | ✅ |
| 15 | Статус задачи | Список | - | ❌ (нет на бэкенде) |
| 16 | Дедлайн задачи | Дата | taskDateFrom/taskDateTo | ✅ |
| 17 | Статус закупа | Список | supplyStatus | ❌ (нет на бэкенде) |
| 18 | Плановая дата | Дата | plannedContractDateFrom/plannedContractDateTo | ✅ |
| 19 | Номер лота | Текст/Число | lotNumbers | ✅ |
| 20 | Название лота | Текст/Список | lotNames | ✅ |
| 21 | Номер заказа | Текст/Список | contractNumber/contractNumbers | ✅ |
| 22 | Поставщик | Текст/Список | supplier/suppliers | ✅ |
| 23 | Дата поставки | Дата | deliveryDateFrom/deliveryDateTo | ✅ |
| 24 | № Договора\|Спецификации\|счета | Текст/Список | contractNumber/contractNumbers | ✅ |
| 25 | Стоимость | Диапазон | costFrom/costTo | ✅ |
| 26 | Примечание | Текст/Список | requirement/requirements | ✅ |
| 27 | ФИО Кладовщика | Текст/Список | responsiblePerson/responsiblePersons | ✅ |
| 28 | Количество | Диапазон | quantityFrom/quantityTo | ✅ |
| 29 | Дата поступления | Дата | deliveryDateFrom/deliveryDateTo | ✅ |
| 30 | Сертификаты | - | - | ❌ (нет на бэкенде) |
| 31 | Акт на брак | - | - | ❌ (нет на бэкенде) |

## Примечания

- ✅ - Фильтр реализован и работает
- ❌ - Фильтр не реализован (нет функционала на бэкенде)
- Фильтры работают до колонки "№ Договора|Спецификации|счета" (индекс 24) включительно
- Колонки 25-29 имеют ограниченную поддержку на бэкенде
- Колонки 30-31 не поддерживаются на бэкенде

## Типы фильтров

1. **Текст** - простой текстовый поиск
2. **Список** - выбор из предопределенных значений
3. **Диапазон** - числовой диапазон (от/до)
4. **Дата** - диапазон дат (от/до)
5. **Число** - числовое значение
