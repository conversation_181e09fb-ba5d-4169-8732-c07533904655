import 'package:flutter_test/flutter_test.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';

void main() {
  group('PurchaseList Task Selection Tests', () {
    late BlocPurchaseList bloc;

    setUp(() {
      bloc = BlocPurchaseList();
    });

    tearDown(() {
      bloc.close();
    });

    test('should select task when product with task is selected', () async {
      // Arrange
      final task = TaskProgressModel(
        id: 'task1',
        status: TaskStatus.inProgress,
      );

      final product = ProvisionProductModel(
        uniqueId: 'product1',
        product: ProductModel(id: 'product1'),
        task: task,
      );

      // Act
      bloc.add(ToggleProvisionProductSelection(product));

      // Assert
      await expectLater(
        bloc.stream,
        emits(predicate<BlocPurchaseListState>((state) =>
            state.selectedProvisionProductsForTask.length == 1 &&
            state.selectedProgressTasks.length == 1 &&
            state.selectedProgressTasks.first.id == 'task1')),
      );
    });

    test('should deselect task when product with task is deselected', () async {
      // Arrange
      final task = TaskProgressModel(
        id: 'task1',
        status: TaskStatus.inProgress,
      );

      final product = ProvisionProductModel(
        uniqueId: 'product1',
        product: ProductModel(id: 'product1'),
        task: task,
      );

      // First select the product
      bloc.add(ToggleProvisionProductSelection(product));
      await bloc.stream.first;

      // Act - deselect the product
      bloc.add(ToggleProvisionProductSelection(product));

      // Assert
      await expectLater(
        bloc.stream,
        emits(predicate<BlocPurchaseListState>((state) =>
            state.selectedProvisionProductsForTask.isEmpty &&
            state.selectedProgressTasks.isEmpty)),
      );
    });

    test('should select product when task is selected', () async {
      // Arrange
      final task = TaskProgressModel(
        id: 'task1',
        status: TaskStatus.inProgress,
      );

      final product = ProvisionProductModel(
        uniqueId: 'product1',
        product: ProductModel(id: 'product1'),
        task: task,
      );

      // Set up the products in state
      bloc.add(SetProvisionProducts([product]));
      await bloc.stream.first;

      // Act
      bloc.add(ToggleSelectedProgressTaskInPurchaseList(task));

      // Assert
      await expectLater(
        bloc.stream,
        emits(predicate<BlocPurchaseListState>((state) =>
            state.selectedProgressTasks.length == 1 &&
            state.selectedProvisionProductsForTask.length == 1 &&
            state.selectedProvisionProductsForTask.first.uniqueId ==
                'product1')),
      );
    });

    test('should clear both products and tasks when clearing product selection',
        () async {
      // Arrange
      final task = TaskProgressModel(
        id: 'task1',
        status: TaskStatus.inProgress,
      );

      final product = ProvisionProductModel(
        uniqueId: 'product1',
        product: ProductModel(id: 'product1'),
        task: task,
      );

      // First select the product
      bloc.add(ToggleProvisionProductSelection(product));
      await bloc.stream.first;

      // Act
      bloc.add(ClearProvisionProductSelections());

      // Assert
      await expectLater(
        bloc.stream,
        emits(predicate<BlocPurchaseListState>((state) =>
            state.selectedProvisionProductsForTask.isEmpty &&
            state.selectedProgressTasks.isEmpty)),
      );
    });

    test('should clear both products and tasks when clearing task selection',
        () async {
      // Arrange
      final task = TaskProgressModel(
        id: 'task1',
        status: TaskStatus.inProgress,
      );

      final product = ProvisionProductModel(
        uniqueId: 'product1',
        product: ProductModel(id: 'product1'),
        task: task,
      );

      // First select the product
      bloc.add(ToggleProvisionProductSelection(product));
      await bloc.stream.first;

      // Act
      bloc.add(ClearProgressTasksSelectionsInPurchaseList());

      // Assert
      await expectLater(
        bloc.stream,
        emits(predicate<BlocPurchaseListState>((state) =>
            state.selectedProvisionProductsForTask.isEmpty &&
            state.selectedProgressTasks.isEmpty)),
      );
    });
  });
}
