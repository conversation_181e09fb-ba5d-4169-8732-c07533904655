# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
# Install dependencies
flutter pub get

# Code generation (required after model/route changes)
flutter pub run build_runner build --delete-conflicting-outputs

# Watch mode for code generation
flutter pub run build_runner watch --delete-conflicting-outputs

# Run the app
flutter run

# Linting
flutter analyze

# Run tests
flutter test

# Build for production
flutter build apk          # Android
flutter build ios         # iOS
flutter build web         # Web
```

## Architecture

This Flutter application follows a **Feature-First Clean Architecture** pattern:

### Directory Structure
- `lib/features/` - Feature modules, each containing:
  - `data/` - Models (with `.g.dart` and `.freezed.dart` generated files) and repositories
  - `domain/` - Business logic entities (when needed)
  - `presentation/` - UI screens, widgets, and BLoCs
- `lib/core/` - Core functionality including navigation (AutoRoute)
- `lib/shared/` - Shared components, API client, themes, and reusable widgets

### Key Patterns
1. **State Management**: BLoC pattern with flutter_bloc
   - Global BLoCs are provided in `app.dart`
   - Feature-specific BLoCs are in their respective feature folders

2. **Routing**: AutoRoute for declarative navigation
   - Routes defined in `core/navigation/index.dart`
   - Generated with `.gr.dart` files

3. **Data Models**: 
   - Use Freezed for complex models requiring immutability
   - Use JsonSerializable for simple serialization
   - Models generate `.g.dart` and `.freezed.dart` files

4. **API Communication**:
   - Centralized API client in `shared/data/datasources/api.dart`
   - Repository pattern for data access
   - Dio for HTTP requests

5. **Code Generation**:
   - Always run `build_runner` after modifying models or routes
   - Generated files should not be edited manually

### Important Notes
- The app supports multiple platforms (iOS, Android, Web, Windows, macOS, Linux)
- Environment configuration is in `.env` file (not committed)
- Use Russian language for UI text as the app is localized for Russian users
- Follow existing patterns when adding new features - create the same folder structure within `lib/features/`