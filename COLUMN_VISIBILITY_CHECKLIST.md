# Чеклист реализации функции скрытия столбцов в списке закупок

## ✅ Задачи

### 1. Модель для хранения видимости столбцов
- [x] Добавить поле `columnVisibility: Map<String, bool>` в `StatePurchaseList`
- [x] Установить значения по умолчанию (все столбцы видимы)
- [x] Обновить методы `copyWith` и конструкторы

### 2. Обновление BLoC
- [x] Создать событие `UpdateColumnVisibility` в `events.dart`
- [x] Добавить обработчик события в `bloc.dart`
- [x] Обновить инициализацию состояния с дефолтными значениями

### 3. UI компонент попапа
- [x] Создать файл `column_visibility_dialog.dart`
- [x] Реализовать диалог с использованием `showBaseDialog`
- [x] Добавить список столбцов с чекбоксами
- [x] Добавить кнопки "Показать все" и "Скрыть все"
- [x] Добавить кнопку "Применить"
- [x] Стилизовать согласно дизайн-системе приложения

### 4. Интеграция с кнопкой настроек
- [x] Добавить обработчик `onPressed` для кнопки настроек
- [x] Открывать диалог при нажатии
- [x] Передавать текущее состояние видимости в диалог
- [x] Обрабатывать результат диалога

### 5. Обновление рендеринга таблицы
- [x] Фильтровать столбцы на основе `columnVisibility`
- [x] Обновить генерацию `DataColumn2`
- [x] Обновить генерацию ячеек данных
- [x] Пересчитывать ширину таблицы динамически
- [x] Обновить минимальную ширину контейнера

### 6. Персистентность настроек
- [x] Добавить методы для сохранения в `SharedPreferences`
- [x] Добавить методы для загрузки из `SharedPreferences`
- [x] Загружать настройки при инициализации BLoC
- [x] Сохранять настройки при изменении

### 7. Тестирование
- [ ] Проверить корректность скрытия/показа столбцов
- [ ] Проверить сохранение настроек после перезапуска
- [ ] Проверить работу кнопок "Показать все"/"Скрыть все"
- [ ] Проверить адаптивность таблицы при изменении видимости

## 📝 Дополнительные улучшения (опционально)
- [ ] Минимальное количество видимых столбцов
- [ ] Анимация при скрытии/показе столбцов
- [ ] Индикатор количества скрытых столбцов
- [ ] Возможность сброса к настройкам по умолчанию