/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Inter-VariableFont_opsz,wght.ttf
  String get interVariableFontOpszWght =>
      'assets/fonts/Inter-VariableFont_opsz,wght.ttf';

  /// File path: assets/fonts/JetBrainsMono.ttf
  String get jetBrainsMono => 'assets/fonts/JetBrainsMono.ttf';

  /// List of all assets
  List<String> get values => [interVariableFontOpszWght, jetBrainsMono];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/add.svg
  String get add => 'assets/icons/add.svg';

  /// File path: assets/icons/arrow_back.svg
  String get arrowBack => 'assets/icons/arrow_back.svg';

  /// File path: assets/icons/arrow_back_ios_new.svg
  String get arrowBackIosNew => 'assets/icons/arrow_back_ios_new.svg';

  /// File path: assets/icons/arrow_downward.svg
  String get arrowDownward => 'assets/icons/arrow_downward.svg';

  /// File path: assets/icons/arrow_forward_ios.svg
  String get arrowForwardIos => 'assets/icons/arrow_forward_ios.svg';

  /// File path: assets/icons/check.svg
  String get check => 'assets/icons/check.svg';

  /// File path: assets/icons/close.svg
  String get close => 'assets/icons/close.svg';

  /// File path: assets/icons/copy.svg
  String get copy => 'assets/icons/copy.svg';

  /// File path: assets/icons/create_folder.svg
  String get createFolder => 'assets/icons/create_folder.svg';

  /// File path: assets/icons/delete.svg
  String get delete => 'assets/icons/delete.svg';

  /// File path: assets/icons/description.svg
  String get description => 'assets/icons/description.svg';

  /// File path: assets/icons/developer_guide.svg
  String get developerGuide => 'assets/icons/developer_guide.svg';

  /// File path: assets/icons/edit.svg
  String get edit => 'assets/icons/edit.svg';

  /// File path: assets/icons/event.svg
  String get event => 'assets/icons/event.svg';

  /// File path: assets/icons/fit_heigth.svg
  String get fitHeigth => 'assets/icons/fit_heigth.svg';

  /// File path: assets/icons/fit_width.svg
  String get fitWidth => 'assets/icons/fit_width.svg';

  /// File path: assets/icons/functions.svg
  String get functions => 'assets/icons/functions.svg';

  /// File path: assets/icons/garage_door.svg
  String get garageDoor => 'assets/icons/garage_door.svg';

  /// File path: assets/icons/help.svg
  String get help => 'assets/icons/help.svg';

  /// File path: assets/icons/hide.svg
  String get hide => 'assets/icons/hide.svg';

  /// File path: assets/icons/home.svg
  String get home => 'assets/icons/home.svg';

  /// File path: assets/icons/import.svg
  String get import => 'assets/icons/import.svg';

  /// File path: assets/icons/keyboard_arrow_down.svg
  String get keyboardArrowDown => 'assets/icons/keyboard_arrow_down.svg';

  /// File path: assets/icons/keyboard_arrow_up.svg
  String get keyboardArrowUp => 'assets/icons/keyboard_arrow_up.svg';

  /// File path: assets/icons/more_vert.svg
  String get moreVert => 'assets/icons/more_vert.svg';

  /// File path: assets/icons/motion.svg
  String get motion => 'assets/icons/motion.svg';

  /// File path: assets/icons/notifications.svg
  String get notifications => 'assets/icons/notifications.svg';

  /// File path: assets/icons/number.svg
  String get number => 'assets/icons/number.svg';

  /// File path: assets/icons/open_folder.svg
  String get openFolder => 'assets/icons/open_folder.svg';

  /// File path: assets/icons/page_info.svg
  String get pageInfo => 'assets/icons/page_info.svg';

  /// File path: assets/icons/pallet.svg
  String get pallet => 'assets/icons/pallet.svg';

  /// File path: assets/icons/paste.svg
  String get paste => 'assets/icons/paste.svg';

  /// File path: assets/icons/person_add.svg
  String get personAdd => 'assets/icons/person_add.svg';

  /// File path: assets/icons/person_edit.svg
  String get personEdit => 'assets/icons/person_edit.svg';

  /// File path: assets/icons/person_remove.svg
  String get personRemove => 'assets/icons/person_remove.svg';

  /// File path: assets/icons/repeat.svg
  String get repeat => 'assets/icons/repeat.svg';

  /// File path: assets/icons/search.svg
  String get search => 'assets/icons/search.svg';

  /// File path: assets/icons/sell.svg
  String get sell => 'assets/icons/sell.svg';

  /// File path: assets/icons/settings.svg
  String get settings => 'assets/icons/settings.svg';

  /// File path: assets/icons/square.svg
  String get square => 'assets/icons/square.svg';

  /// File path: assets/icons/tab_hide.svg
  String get tabHide => 'assets/icons/tab_hide.svg';

  /// File path: assets/icons/tab_show.svg
  String get tabShow => 'assets/icons/tab_show.svg';

  /// File path: assets/icons/underline.svg
  String get underline => 'assets/icons/underline.svg';

  /// File path: assets/icons/upload_file.svg
  String get uploadFile => 'assets/icons/upload_file.svg';

  /// File path: assets/icons/visibility.svg
  String get visibility => 'assets/icons/visibility.svg';

  /// File path: assets/icons/visibility_off.svg
  String get visibilityOff => 'assets/icons/visibility_off.svg';

  /// File path: assets/icons/warehouse.svg
  String get warehouse => 'assets/icons/warehouse.svg';

  /// File path: assets/icons/weight.svg
  String get weight => 'assets/icons/weight.svg';

  /// File path: assets/icons/widgets.svg
  String get widgets => 'assets/icons/widgets.svg';

  /// List of all assets
  List<String> get values => [
        add,
        arrowBack,
        arrowBackIosNew,
        arrowDownward,
        arrowForwardIos,
        check,
        close,
        copy,
        createFolder,
        delete,
        description,
        developerGuide,
        edit,
        event,
        fitHeigth,
        fitWidth,
        functions,
        garageDoor,
        help,
        hide,
        home,
        import,
        keyboardArrowDown,
        keyboardArrowUp,
        moreVert,
        motion,
        notifications,
        number,
        openFolder,
        pageInfo,
        pallet,
        paste,
        personAdd,
        personEdit,
        personRemove,
        repeat,
        search,
        sell,
        settings,
        square,
        tabHide,
        tabShow,
        underline,
        uploadFile,
        visibility,
        visibilityOff,
        warehouse,
        weight,
        widgets
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/sphere_logo.png
  AssetGenImage get sphereLogo =>
      const AssetGenImage('assets/images/sphere_logo.png');

  /// File path: assets/images/start.ico
  String get startIco => 'assets/images/start.ico';

  /// File path: assets/images/start.png
  AssetGenImage get startPng => const AssetGenImage('assets/images/start.png');

  /// List of all assets
  List<dynamic> get values => [sphereLogo, startIco, startPng];
}

class Assets {
  const Assets._();

  static const String aEnv = '.env';
  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();

  /// List of all assets
  static List<String> get values => [aEnv];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
