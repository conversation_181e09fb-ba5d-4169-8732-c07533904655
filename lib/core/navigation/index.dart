import 'package:auto_route/auto_route.dart';
import 'package:sphere/core/navigation/index.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  // @override
  // RouteType get defaultRouteType => RouteType.custom(
  //       transitionsBuilder: (context, animation, secondaryAnimation, child) {
  //         // Анимация скольжения с затуханием
  //         const curve = Curves.easeInOut;

  //         final tween = Tween<Offset>(
  //           begin: const Offset(1.0, 0.0), // Начальная позиция (с правого края)
  //           end: Offset.zero, // Конечная позиция (на экране)
  //         ).chain(CurveTween(curve: curve));

  //         final fadeTween = Tween<double>(
  //           begin: 0.0, // Полная прозрачность в начале
  //           end: 1.0, // Полностью видно в конце
  //         ).chain(CurveTween(curve: curve));

  //         return SlideTransition(
  //           position: animation.drive(tween),
  //           child: FadeTransition(
  //             opacity: animation.drive(fadeTween),
  //             child: child,
  //           ),
  //         );
  //       },
  //     );

  @override
  RouteType get defaultRouteType => RouteType.cupertino();

  @override
  List<AutoRoute> get routes => [
        AutoRoute(page: SplashRoute.page, initial: true, path: '/splash'),
        AutoRoute(page: AuthRoute.page, path: '/auth'),
        AutoRoute(
          page: InitialRoute.page,
          path: '/home',
          allowSnapshotting: false,
          children: [
            RedirectRoute(path: '', redirectTo: 'branch'),
            // AutoRoute(
            //   page: BranchesRoute.page,
            //   path: 'branches',
            //   initial: true,
            // ),
            AutoRoute(
              page: NomenclaturesRoute.page,
              path: 'nomenclatures',
              allowSnapshotting: false,
            ),
            AutoRoute(
              page: StorageRoute.page,
              path: 'storage',
              allowSnapshotting: false,
            ),
            AutoRoute(
              page: BranchRoute.page,
              path: 'branch',
              allowSnapshotting: false,
            ),
            AutoRoute(
              page: NotificationRoute.page,
              path: 'notifications',
              allowSnapshotting: false,
            ),
            AutoRoute(
              page: SettingsRoute.page,
              path: 'settings',
              allowSnapshotting: false,
            ),
            AutoRoute(
              page: WarehousesRoute.page,
              path: 'warehouses',
              allowSnapshotting: false,
            ),
            AutoRoute(
              page: OtkDeliveriesRoute.page,
              path: 'otk',
              allowSnapshotting: false,
            ),
            AutoRoute(
              page: OtkDefectActsRoute.page,
              path: 'otk-defect-acts',
              allowSnapshotting: false,
            ),
          ],
        ),
        // Project
        AutoRoute(
          page: ProjectRoute.page,
          path: '/project/:id',
        ),
        // Project structure
        AutoRoute(
          page: StructureRoute.page,
          path: '/project/:id/structure',
        ),
        // Project storage
        AutoRoute(
          page: InnerStorageRoute.page,
          path: '/project/:id/storage',
        ),
        // Project purchase list
        AutoRoute(
          page: PurchaseListRoute.page,
          path: '/project/:id/purchaseList',
        ),
        // Product
        AutoRoute(
          page: ProductRoute.page,
          path: '/project/:id/product/:productId',
        ),
        // Files
        AutoRoute(
          page: FilesRoute.page,
          path: '/files/:id',
        ),
        AutoRoute(
          page: PdfViewerRoute.page,
          path: '/pdfViewer/:pdfUrl',
        ),
        // Admin
        AutoRoute(
          page: AdminRoute.page,
          path: '/admin',
        ),
        // Deliveries
        AutoRoute(
          page: DeliveriesRoute.page,
          path: '/deliveries',
        ),
        // Production
        AutoRoute(
          page: ProductionRoute.page,
          path: '/production',
        ),
        // Warehouse contents
        AutoRoute(
          page: WarehouseContentsRoute.page,
          path: '/warehouse/:warehouseId/contents',
        ),
      ];

  @override
  List<AutoRouteGuard> get guards => [];
}
