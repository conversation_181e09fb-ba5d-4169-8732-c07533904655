import 'dart:io';

import 'package:file_picker/file_picker.dart';

Future<String?> pickFolder() async {
  final result = await FilePicker.platform.getDirectoryPath(
    dialogTitle: 'Выберите папку',
  );

  return result;
}

Future<List<File>> scanFolder(String folderPath) async {
  final dir = Directory(folderPath);
  if (!await dir.exists()) {
    throw Exception('Folder does not exist: $folderPath');
  }

  final result = await dir
      .list(recursive: true)
      .where((entity) => entity is File)
      .cast<File>()
      .toList();

  return result;
}
