import 'dart:io';

import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;

Future<File> zipFolder(
  List<File> files, // Список объектов File
  String folderPath,
) async {
  final archive = Archive();

  // Добавление папки -
  // final folderName = path.basename(folderPath);

  for (final file in files) {
    final bytes = await file.readAsBytes();

    // Рассчитываем относительный путь от выбранной папки
    final relativePath = path.relative(file.path, from: folderPath);

    // Добавляем файл в архив с относительным путём
    archive.addFile(ArchiveFile(relativePath, bytes.length, bytes));
  }

  // Кодируем архив
  final zipEncoder = ZipEncoder();
  final zipData = zipEncoder.encode(archive);

  // Получаем домашнюю папку приложения
  final appDirectory = Directory.current.path;

  // Путь к архиву в домашней папке
  final zipFilePath = path.join(appDirectory, 'archive.zip');

  // Сохраняем архив в файл
  final zipFile = File(zipFilePath);
  await zipFile.writeAsBytes(zipData);
  return zipFile;
}
