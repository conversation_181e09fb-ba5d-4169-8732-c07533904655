import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Performance optimization utilities for the Sphere application
class PerformanceOptimizer {
  static final PerformanceOptimizer _instance =
      PerformanceOptimizer._internal();
  factory PerformanceOptimizer() => _instance;
  PerformanceOptimizer._internal();

  /// Cache for storing computed values
  final Map<String, _CachedValue> _cache = {};

  /// Debouncers for different operations
  final Map<String, Debouncer> _debouncers = {};

  /// Performance metrics
  final Map<String, List<int>> _metrics = {};

  /// Cache a value with expiration
  void cacheValue<T>(String key, T value, {Duration? expiration}) {
    _cache[key] = _CachedValue<T>(
      value: value,
      expiration: expiration ?? const Duration(minutes: 5),
    );
  }

  /// Get a cached value
  T? getCachedValue<T>(String key) {
    final cached = _cache[key];
    if (cached != null && cached.isValid) {
      return cached.value as T;
    }
    _cache.remove(key);
    return null;
  }

  /// Clear all cached values
  void clearCache() {
    _cache.clear();
  }

  /// Get or create a debouncer for a specific operation
  Debouncer getDebouncer(
    String key, {
    Duration latency = const Duration(milliseconds: 500),
  }) {
    return _debouncers.putIfAbsent(key, () => Debouncer(latency: latency));
  }

  /// Record a performance metric
  void recordMetric(String name, int value) {
    _metrics.putIfAbsent(name, () => []).add(value);

    // Keep only last 100 values to prevent memory growth
    if (_metrics[name]!.length > 100) {
      _metrics[name] = _metrics[name]!.sublist(_metrics[name]!.length - 100);
    }
  }

  /// Get average metric value
  double getAverageMetric(String name) {
    final values = _metrics[name];
    if (values == null || values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }

  /// Get all metrics
  Map<String, List<int>> getMetrics() => Map.unmodifiable(_metrics);

  /// Log memory usage
  void logMemoryUsage() {
    if (kDebugMode) {
      final memoryInfo = ProcessInfo.currentRss;
      final memoryMB = memoryInfo ~/ 1024 ~/ 1024;
      debugPrint('Memory usage: ${memoryMB}MB');
      recordMetric('memory_usage_mb', memoryMB);
    }
  }

  /// Log performance warning
  void logPerformanceWarning(String operation, int duration) {
    if (kDebugMode && duration > 16) {
      // 16ms = 60fps threshold
      debugPrint('Performance warning: $operation took ${duration}ms');
    }
  }
}

/// Cached value with expiration
class _CachedValue<T> {
  final T value;
  final DateTime _createdAt;
  final Duration expiration;

  _CachedValue({
    required this.value,
    required this.expiration,
  }) : _createdAt = DateTime.now();

  bool get isValid => DateTime.now().difference(_createdAt) < expiration;
}

/// Debouncer for throttling frequent operations
class Debouncer {
  final Duration latency;
  Timer? _timer;

  Debouncer({required this.latency});

  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(latency, action);
  }

  void cancel() {
    _timer?.cancel();
  }

  void dispose() {
    _timer?.cancel();
  }
}

/// Performance monitoring mixin for widgets
mixin PerformanceMonitorMixin<T extends StatefulWidget> on State<T> {
  final Stopwatch _buildStopwatch = Stopwatch();
  final Stopwatch _initStopwatch = Stopwatch();

  @override
  void initState() {
    _initStopwatch.start();
    super.initState();
    _initStopwatch.stop();

    if (kDebugMode) {
      PerformanceOptimizer()
          .recordMetric('widget_init_time', _initStopwatch.elapsedMilliseconds);
    }
  }

  @override
  Widget build(BuildContext context) {
    _buildStopwatch.start();
    final widget = buildWidget(context);
    _buildStopwatch.stop();

    if (kDebugMode) {
      PerformanceOptimizer().recordMetric(
          'widget_build_time', _buildStopwatch.elapsedMilliseconds);
      PerformanceOptimizer().logPerformanceWarning(
          '${widget.runtimeType} build', _buildStopwatch.elapsedMilliseconds);
    }

    return widget;
  }

  /// Override this method instead of build
  Widget buildWidget(BuildContext context);

  @override
  void dispose() {
    _buildStopwatch.stop();
    _initStopwatch.stop();
    super.dispose();
  }
}

/// Optimized list builder for better performance
class OptimizedListView<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext, T, int) itemBuilder;
  final Widget Function(BuildContext, int)? separatorBuilder;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.separatorBuilder,
    this.padding,
    this.controller,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    if (separatorBuilder != null) {
      return ListView.separated(
        itemCount: items.length,
        itemBuilder: (context, index) =>
            itemBuilder(context, items[index], index),
        separatorBuilder: separatorBuilder!,
        padding: padding,
        controller: controller,
        shrinkWrap: shrinkWrap,
        physics: physics,
      );
    } else {
      return ListView.builder(
        itemCount: items.length,
        itemBuilder: (context, index) =>
            itemBuilder(context, items[index], index),
        padding: padding,
        controller: controller,
        shrinkWrap: shrinkWrap,
        physics: physics,
      );
    }
  }
}

/// Extension for performance monitoring
extension PerformanceExtension on Widget {
  /// Wrap widget with performance monitoring
  Widget withPerformanceMonitoring(String operationName) {
    if (!kDebugMode) return this;

    return PerformanceMonitor(
      operationName: operationName,
      child: this,
    );
  }
}

/// Performance monitoring widget
class PerformanceMonitor extends StatefulWidget {
  final String operationName;
  final Widget child;

  const PerformanceMonitor({
    super.key,
    required this.operationName,
    required this.child,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  final Stopwatch _stopwatch = Stopwatch();

  @override
  void initState() {
    super.initState();
    _stopwatch.start();
  }

  @override
  void dispose() {
    _stopwatch.stop();
    PerformanceOptimizer().recordMetric(
        '${widget.operationName}_duration', _stopwatch.elapsedMilliseconds);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
