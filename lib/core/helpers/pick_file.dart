import 'dart:io';

import 'package:file_picker/file_picker.dart';

Future<File?> pickFile({List<String>? extensions}) async {
  final result = await FilePicker.platform.pickFiles(
    dialogTitle: 'Выберите файл',
    type: (extensions ?? []).isEmpty ? FileType.any : FileType.custom,
    allowedExtensions: extensions,
  );

  if (result == null) return null;
  return File(result.files.single.path!);
}
