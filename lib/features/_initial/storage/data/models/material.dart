// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';

part 'material.freezed.dart';
part 'material.g.dart';

@freezed
class MaterialModel with _$MaterialModel {
  const MaterialModel._();

  @JsonSerializable(includeIfNull: false)
  const factory MaterialModel({
    @JsonKey(name: '_id') String? id,
    @JsonKey(name: 'materialId') String? nomenclatureId,
    @JsonKey(name: 'material') NomenclatureModel? nomenclature,
    double? quantity,
    // TODO: kludge
    QuantitiesModel? quantities,
    String? status,
    UnitType? unit,
    StorageType? storageType,
    String? projectId,
    String? projectName,
    DateTime? updatedAt,
    DateTime? createdAt,
  }) = _MaterialModel;

  factory MaterialModel.fromJson(Map<String, dynamic> json) =>
      _$MaterialModelFromJson(json);
}

@freezed
class QuantitiesModel with _$QuantitiesModel {
  @JsonSerializable(includeIfNull: false)
  const factory QuantitiesModel({
    double? ready,
    @JsonKey(name: 'in_production') double? inProduction,
    @JsonKey(name: 'pending_qc') double? pendingQc,
    @JsonKey(name: 'qc_rejected') double? qcRejected,
  }) = _QuantitiesModel;

  factory QuantitiesModel.fromJson(Map<String, dynamic> json) =>
      _$QuantitiesModelFromJson(json);
}

enum StorageType {
  @JsonValue('common')
  common,
  @JsonValue('project')
  project;

  String getName() {
    switch (this) {
      case StorageType.common:
        return 'общий';
      case StorageType.project:
        return 'проекта';
    }
  }
}
