// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'material.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MaterialModelImpl _$$MaterialModelImplFromJson(Map<String, dynamic> json) =>
    _$MaterialModelImpl(
      id: json['_id'] as String?,
      nomenclatureId: json['materialId'] as String?,
      nomenclature: json['material'] == null
          ? null
          : NomenclatureModel.fromJson(
              json['material'] as Map<String, dynamic>),
      quantity: (json['quantity'] as num?)?.toDouble(),
      quantities: json['quantities'] == null
          ? null
          : QuantitiesModel.fromJson(
              json['quantities'] as Map<String, dynamic>),
      status: json['status'] as String?,
      unit: $enumDecodeNullable(_$UnitTypeEnumMap, json['unit']),
      storageType:
          $enumDecodeNullable(_$StorageTypeEnumMap, json['storageType']),
      projectId: json['projectId'] as String?,
      projectName: json['projectName'] as String?,
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$MaterialModelImplToJson(_$MaterialModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.nomenclatureId case final value?) 'materialId': value,
      if (instance.nomenclature case final value?) 'material': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.quantities case final value?) 'quantities': value,
      if (instance.status case final value?) 'status': value,
      if (_$UnitTypeEnumMap[instance.unit] case final value?) 'unit': value,
      if (_$StorageTypeEnumMap[instance.storageType] case final value?)
        'storageType': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.projectName case final value?) 'projectName': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
    };

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};

const _$StorageTypeEnumMap = {
  StorageType.common: 'common',
  StorageType.project: 'project',
};

_$QuantitiesModelImpl _$$QuantitiesModelImplFromJson(
        Map<String, dynamic> json) =>
    _$QuantitiesModelImpl(
      ready: (json['ready'] as num?)?.toDouble(),
      inProduction: (json['in_production'] as num?)?.toDouble(),
      pendingQc: (json['pending_qc'] as num?)?.toDouble(),
      qcRejected: (json['qc_rejected'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$QuantitiesModelImplToJson(
        _$QuantitiesModelImpl instance) =>
    <String, dynamic>{
      if (instance.ready case final value?) 'ready': value,
      if (instance.inProduction case final value?) 'in_production': value,
      if (instance.pendingQc case final value?) 'pending_qc': value,
      if (instance.qcRejected case final value?) 'qc_rejected': value,
    };
