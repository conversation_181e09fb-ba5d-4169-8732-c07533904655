// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'material.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MaterialModel _$MaterialModelFromJson(Map<String, dynamic> json) {
  return _MaterialModel.fromJson(json);
}

/// @nodoc
mixin _$MaterialModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'materialId')
  String? get nomenclatureId => throw _privateConstructorUsedError;
  @JsonKey(name: 'material')
  NomenclatureModel? get nomenclature => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError; // TODO: kludge
  QuantitiesModel? get quantities => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  UnitType? get unit => throw _privateConstructorUsedError;
  StorageType? get storageType => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  String? get projectName => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Serializes this MaterialModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MaterialModelCopyWith<MaterialModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MaterialModelCopyWith<$Res> {
  factory $MaterialModelCopyWith(
          MaterialModel value, $Res Function(MaterialModel) then) =
      _$MaterialModelCopyWithImpl<$Res, MaterialModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      @JsonKey(name: 'materialId') String? nomenclatureId,
      @JsonKey(name: 'material') NomenclatureModel? nomenclature,
      double? quantity,
      QuantitiesModel? quantities,
      String? status,
      UnitType? unit,
      StorageType? storageType,
      String? projectId,
      String? projectName,
      DateTime? updatedAt,
      DateTime? createdAt});

  $QuantitiesModelCopyWith<$Res>? get quantities;
}

/// @nodoc
class _$MaterialModelCopyWithImpl<$Res, $Val extends MaterialModel>
    implements $MaterialModelCopyWith<$Res> {
  _$MaterialModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nomenclatureId = freezed,
    Object? nomenclature = freezed,
    Object? quantity = freezed,
    Object? quantities = freezed,
    Object? status = freezed,
    Object? unit = freezed,
    Object? storageType = freezed,
    Object? projectId = freezed,
    Object? projectName = freezed,
    Object? updatedAt = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      nomenclatureId: freezed == nomenclatureId
          ? _value.nomenclatureId
          : nomenclatureId // ignore: cast_nullable_to_non_nullable
              as String?,
      nomenclature: freezed == nomenclature
          ? _value.nomenclature
          : nomenclature // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      quantities: freezed == quantities
          ? _value.quantities
          : quantities // ignore: cast_nullable_to_non_nullable
              as QuantitiesModel?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      unit: freezed == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      storageType: freezed == storageType
          ? _value.storageType
          : storageType // ignore: cast_nullable_to_non_nullable
              as StorageType?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectName: freezed == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of MaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuantitiesModelCopyWith<$Res>? get quantities {
    if (_value.quantities == null) {
      return null;
    }

    return $QuantitiesModelCopyWith<$Res>(_value.quantities!, (value) {
      return _then(_value.copyWith(quantities: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MaterialModelImplCopyWith<$Res>
    implements $MaterialModelCopyWith<$Res> {
  factory _$$MaterialModelImplCopyWith(
          _$MaterialModelImpl value, $Res Function(_$MaterialModelImpl) then) =
      __$$MaterialModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      @JsonKey(name: 'materialId') String? nomenclatureId,
      @JsonKey(name: 'material') NomenclatureModel? nomenclature,
      double? quantity,
      QuantitiesModel? quantities,
      String? status,
      UnitType? unit,
      StorageType? storageType,
      String? projectId,
      String? projectName,
      DateTime? updatedAt,
      DateTime? createdAt});

  @override
  $QuantitiesModelCopyWith<$Res>? get quantities;
}

/// @nodoc
class __$$MaterialModelImplCopyWithImpl<$Res>
    extends _$MaterialModelCopyWithImpl<$Res, _$MaterialModelImpl>
    implements _$$MaterialModelImplCopyWith<$Res> {
  __$$MaterialModelImplCopyWithImpl(
      _$MaterialModelImpl _value, $Res Function(_$MaterialModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of MaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? nomenclatureId = freezed,
    Object? nomenclature = freezed,
    Object? quantity = freezed,
    Object? quantities = freezed,
    Object? status = freezed,
    Object? unit = freezed,
    Object? storageType = freezed,
    Object? projectId = freezed,
    Object? projectName = freezed,
    Object? updatedAt = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_$MaterialModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      nomenclatureId: freezed == nomenclatureId
          ? _value.nomenclatureId
          : nomenclatureId // ignore: cast_nullable_to_non_nullable
              as String?,
      nomenclature: freezed == nomenclature
          ? _value.nomenclature
          : nomenclature // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      quantities: freezed == quantities
          ? _value.quantities
          : quantities // ignore: cast_nullable_to_non_nullable
              as QuantitiesModel?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      unit: freezed == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      storageType: freezed == storageType
          ? _value.storageType
          : storageType // ignore: cast_nullable_to_non_nullable
              as StorageType?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectName: freezed == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$MaterialModelImpl extends _MaterialModel {
  const _$MaterialModelImpl(
      {@JsonKey(name: '_id') this.id,
      @JsonKey(name: 'materialId') this.nomenclatureId,
      @JsonKey(name: 'material') this.nomenclature,
      this.quantity,
      this.quantities,
      this.status,
      this.unit,
      this.storageType,
      this.projectId,
      this.projectName,
      this.updatedAt,
      this.createdAt})
      : super._();

  factory _$MaterialModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$MaterialModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  @JsonKey(name: 'materialId')
  final String? nomenclatureId;
  @override
  @JsonKey(name: 'material')
  final NomenclatureModel? nomenclature;
  @override
  final double? quantity;
// TODO: kludge
  @override
  final QuantitiesModel? quantities;
  @override
  final String? status;
  @override
  final UnitType? unit;
  @override
  final StorageType? storageType;
  @override
  final String? projectId;
  @override
  final String? projectName;
  @override
  final DateTime? updatedAt;
  @override
  final DateTime? createdAt;

  @override
  String toString() {
    return 'MaterialModel(id: $id, nomenclatureId: $nomenclatureId, nomenclature: $nomenclature, quantity: $quantity, quantities: $quantities, status: $status, unit: $unit, storageType: $storageType, projectId: $projectId, projectName: $projectName, updatedAt: $updatedAt, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MaterialModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.nomenclatureId, nomenclatureId) ||
                other.nomenclatureId == nomenclatureId) &&
            (identical(other.nomenclature, nomenclature) ||
                other.nomenclature == nomenclature) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.quantities, quantities) ||
                other.quantities == quantities) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.unit, unit) || other.unit == unit) &&
            (identical(other.storageType, storageType) ||
                other.storageType == storageType) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.projectName, projectName) ||
                other.projectName == projectName) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      nomenclatureId,
      nomenclature,
      quantity,
      quantities,
      status,
      unit,
      storageType,
      projectId,
      projectName,
      updatedAt,
      createdAt);

  /// Create a copy of MaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MaterialModelImplCopyWith<_$MaterialModelImpl> get copyWith =>
      __$$MaterialModelImplCopyWithImpl<_$MaterialModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MaterialModelImplToJson(
      this,
    );
  }
}

abstract class _MaterialModel extends MaterialModel {
  const factory _MaterialModel(
      {@JsonKey(name: '_id') final String? id,
      @JsonKey(name: 'materialId') final String? nomenclatureId,
      @JsonKey(name: 'material') final NomenclatureModel? nomenclature,
      final double? quantity,
      final QuantitiesModel? quantities,
      final String? status,
      final UnitType? unit,
      final StorageType? storageType,
      final String? projectId,
      final String? projectName,
      final DateTime? updatedAt,
      final DateTime? createdAt}) = _$MaterialModelImpl;
  const _MaterialModel._() : super._();

  factory _MaterialModel.fromJson(Map<String, dynamic> json) =
      _$MaterialModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  @JsonKey(name: 'materialId')
  String? get nomenclatureId;
  @override
  @JsonKey(name: 'material')
  NomenclatureModel? get nomenclature;
  @override
  double? get quantity; // TODO: kludge
  @override
  QuantitiesModel? get quantities;
  @override
  String? get status;
  @override
  UnitType? get unit;
  @override
  StorageType? get storageType;
  @override
  String? get projectId;
  @override
  String? get projectName;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get createdAt;

  /// Create a copy of MaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MaterialModelImplCopyWith<_$MaterialModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QuantitiesModel _$QuantitiesModelFromJson(Map<String, dynamic> json) {
  return _QuantitiesModel.fromJson(json);
}

/// @nodoc
mixin _$QuantitiesModel {
  double? get ready => throw _privateConstructorUsedError;
  @JsonKey(name: 'in_production')
  double? get inProduction => throw _privateConstructorUsedError;
  @JsonKey(name: 'pending_qc')
  double? get pendingQc => throw _privateConstructorUsedError;
  @JsonKey(name: 'qc_rejected')
  double? get qcRejected => throw _privateConstructorUsedError;

  /// Serializes this QuantitiesModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QuantitiesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuantitiesModelCopyWith<QuantitiesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuantitiesModelCopyWith<$Res> {
  factory $QuantitiesModelCopyWith(
          QuantitiesModel value, $Res Function(QuantitiesModel) then) =
      _$QuantitiesModelCopyWithImpl<$Res, QuantitiesModel>;
  @useResult
  $Res call(
      {double? ready,
      @JsonKey(name: 'in_production') double? inProduction,
      @JsonKey(name: 'pending_qc') double? pendingQc,
      @JsonKey(name: 'qc_rejected') double? qcRejected});
}

/// @nodoc
class _$QuantitiesModelCopyWithImpl<$Res, $Val extends QuantitiesModel>
    implements $QuantitiesModelCopyWith<$Res> {
  _$QuantitiesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuantitiesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ready = freezed,
    Object? inProduction = freezed,
    Object? pendingQc = freezed,
    Object? qcRejected = freezed,
  }) {
    return _then(_value.copyWith(
      ready: freezed == ready
          ? _value.ready
          : ready // ignore: cast_nullable_to_non_nullable
              as double?,
      inProduction: freezed == inProduction
          ? _value.inProduction
          : inProduction // ignore: cast_nullable_to_non_nullable
              as double?,
      pendingQc: freezed == pendingQc
          ? _value.pendingQc
          : pendingQc // ignore: cast_nullable_to_non_nullable
              as double?,
      qcRejected: freezed == qcRejected
          ? _value.qcRejected
          : qcRejected // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuantitiesModelImplCopyWith<$Res>
    implements $QuantitiesModelCopyWith<$Res> {
  factory _$$QuantitiesModelImplCopyWith(_$QuantitiesModelImpl value,
          $Res Function(_$QuantitiesModelImpl) then) =
      __$$QuantitiesModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? ready,
      @JsonKey(name: 'in_production') double? inProduction,
      @JsonKey(name: 'pending_qc') double? pendingQc,
      @JsonKey(name: 'qc_rejected') double? qcRejected});
}

/// @nodoc
class __$$QuantitiesModelImplCopyWithImpl<$Res>
    extends _$QuantitiesModelCopyWithImpl<$Res, _$QuantitiesModelImpl>
    implements _$$QuantitiesModelImplCopyWith<$Res> {
  __$$QuantitiesModelImplCopyWithImpl(
      _$QuantitiesModelImpl _value, $Res Function(_$QuantitiesModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of QuantitiesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ready = freezed,
    Object? inProduction = freezed,
    Object? pendingQc = freezed,
    Object? qcRejected = freezed,
  }) {
    return _then(_$QuantitiesModelImpl(
      ready: freezed == ready
          ? _value.ready
          : ready // ignore: cast_nullable_to_non_nullable
              as double?,
      inProduction: freezed == inProduction
          ? _value.inProduction
          : inProduction // ignore: cast_nullable_to_non_nullable
              as double?,
      pendingQc: freezed == pendingQc
          ? _value.pendingQc
          : pendingQc // ignore: cast_nullable_to_non_nullable
              as double?,
      qcRejected: freezed == qcRejected
          ? _value.qcRejected
          : qcRejected // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$QuantitiesModelImpl implements _QuantitiesModel {
  const _$QuantitiesModelImpl(
      {this.ready,
      @JsonKey(name: 'in_production') this.inProduction,
      @JsonKey(name: 'pending_qc') this.pendingQc,
      @JsonKey(name: 'qc_rejected') this.qcRejected});

  factory _$QuantitiesModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuantitiesModelImplFromJson(json);

  @override
  final double? ready;
  @override
  @JsonKey(name: 'in_production')
  final double? inProduction;
  @override
  @JsonKey(name: 'pending_qc')
  final double? pendingQc;
  @override
  @JsonKey(name: 'qc_rejected')
  final double? qcRejected;

  @override
  String toString() {
    return 'QuantitiesModel(ready: $ready, inProduction: $inProduction, pendingQc: $pendingQc, qcRejected: $qcRejected)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuantitiesModelImpl &&
            (identical(other.ready, ready) || other.ready == ready) &&
            (identical(other.inProduction, inProduction) ||
                other.inProduction == inProduction) &&
            (identical(other.pendingQc, pendingQc) ||
                other.pendingQc == pendingQc) &&
            (identical(other.qcRejected, qcRejected) ||
                other.qcRejected == qcRejected));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, ready, inProduction, pendingQc, qcRejected);

  /// Create a copy of QuantitiesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuantitiesModelImplCopyWith<_$QuantitiesModelImpl> get copyWith =>
      __$$QuantitiesModelImplCopyWithImpl<_$QuantitiesModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuantitiesModelImplToJson(
      this,
    );
  }
}

abstract class _QuantitiesModel implements QuantitiesModel {
  const factory _QuantitiesModel(
          {final double? ready,
          @JsonKey(name: 'in_production') final double? inProduction,
          @JsonKey(name: 'pending_qc') final double? pendingQc,
          @JsonKey(name: 'qc_rejected') final double? qcRejected}) =
      _$QuantitiesModelImpl;

  factory _QuantitiesModel.fromJson(Map<String, dynamic> json) =
      _$QuantitiesModelImpl.fromJson;

  @override
  double? get ready;
  @override
  @JsonKey(name: 'in_production')
  double? get inProduction;
  @override
  @JsonKey(name: 'pending_qc')
  double? get pendingQc;
  @override
  @JsonKey(name: 'qc_rejected')
  double? get qcRejected;

  /// Create a copy of QuantitiesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuantitiesModelImplCopyWith<_$QuantitiesModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
