import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';

part 'index.g.dart';

@JsonSerializable(includeIfNull: false)
class SearchMaterialOutputModel {
  final List<MaterialModel>? items;
  final int? totalItems;
  final String? message;

  const SearchMaterialOutputModel({
    this.items,
    this.totalItems,
    this.message,
  });

  factory SearchMaterialOutputModel.fromJson(Map<String, dynamic> json) =>
      _$SearchMaterialOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$SearchMaterialOutputModelToJson(this);
}
