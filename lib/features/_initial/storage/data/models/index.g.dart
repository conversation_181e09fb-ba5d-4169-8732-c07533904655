// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SearchMaterialOutputModel _$SearchMaterialOutputModelFromJson(
        Map<String, dynamic> json) =>
    SearchMaterialOutputModel(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => MaterialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SearchMaterialOutputModelToJson(
        SearchMaterialOutputModel instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
      if (instance.message case final value?) 'message': value,
    };
