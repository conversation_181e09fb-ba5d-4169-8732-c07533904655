import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/models/index.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class StorageRepository {
  static Future<Response<SearchMaterialOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<SearchMaterialOutputModel>(
      url: '/storage/search',
      body: body,
      method: 'POST',
      fromJson: SearchMaterialOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<MaterialModel>?> add(
    MaterialModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<MaterialModel>(
      url: '/storage/add',
      body: body,
      method: 'POST',
      fromJson: MaterialModel.fromJson,
    );

    return request;
  }

  static Future<Response<MaterialModel>?> adjust(
    String id,
    QuantitiesModel quantities,
    UnitType unit,
  ) async {
    final body = jsonEncode({
      'storageItemId': id,
      'quantities': quantities.toJson(),
      'unit': unit.name,
    });

    final request = await API.request<MaterialModel>(
      url: '/storage/adjust',
      body: body,
      method: 'POST',
      fromJson: MaterialModel.fromJson,
    );

    return request;
  }

  static Future<Response<MaterialModel>?> transfer({
    required String id,
    required String toProjectId,
    String? description,
    required UnitType unit,
    required double quantity,
    String? toStatus = 'ready',
    String? fromStatus = 'ready',
  }) async {
    final body = jsonEncode({
      'fromStorageItemId': id,
      'toProjectId': toProjectId,
      if (description != null) 'description': description,
      'unit': unit.name,
      if (fromStatus != null) 'fromStatus': fromStatus,
      if (toStatus != null) 'toStatus': toStatus,
      'quantity': quantity,
    });

    final request = await API.request<MaterialModel>(
      url: '/storage/transfer',
      body: body,
      method: 'POST',
      fromJson: MaterialModel.fromJson,
    );

    return request;
  }

  static Future<Response<MaterialModel>?> writeOff(
    String id,
    UnitType unit,
    double quantity, {
    String? status = 'ready',
  }) async {
    final body = jsonEncode({
      'storageItemId': id,
      'unit': unit.name,
      'quantity': quantity,
      if (status != null) 'status': status,
    });

    final request = await API.request<MaterialModel>(
      url: '/storage/write-off',
      body: body,
      method: 'POST',
      fromJson: MaterialModel.fromJson,
    );

    return request;
  }
}
