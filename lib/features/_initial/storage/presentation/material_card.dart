import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class MaterialCard extends StatelessWidget {
  const MaterialCard({
    super.key,
    required this.material,
    this.onSecondaryTapDown,
    this.onTap,
    this.isLoading,
    this.withProjectName = false,
  });

  final MaterialModel material;
  final void Function(TapDownDetails details)? onSecondaryTapDown;
  final void Function()? onTap;
  final bool? isLoading;
  final bool withProjectName;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      onSecondaryTapDown: onSecondaryTapDown,
      isLoading: isLoading,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Expanded(
              child: Text(
                material.nomenclature?.name ?? '',
                style: Fonts.labelMedium,
              ),
            ),
            Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
              // Row(children: [
              //   Text('5', style: Fonts.bodyMedium),
              //   SizedBox(width: 8.0),
              //   SVG(Assets.icons.widgets),
              // ]),
              // SizedBox(height: 10),
              Row(children: [
                Text(
                  '${material.quantities?.ready} ${material.unit?.getName()}',
                  style: Fonts.labelSmall.merge(
                    TextStyle(
                      color: isDarkTheme
                          ? AppColors.darkSecondary
                          : AppColors.lightSecondary,
                    ),
                  ),
                ),
                // SizedBox(width: 8.0),
                // SVG(Assets.icons.weight),
              ]),
            ]),
          ]),
          if (material.projectName != null &&
              material.projectId != null &&
              withProjectName)
            SizedBox(height: 4.0),
          if (material.projectName != null &&
              material.projectId != null &&
              withProjectName)
            GhostButton(
              onTap: () => context.router.push(ProjectRoute(
                id: material.projectId,
              )),
              child: Text(
                '${material.projectName}',
                style: Fonts.labelSmall.merge(
                  TextStyle(
                    color: isDarkTheme
                        ? AppColors.darkSecondary
                        : AppColors.lightSecondary,
                  ),
                ),
              ),
            ),
          if (material.nomenclature?.alternativeUnits != null)
            SizedBox(height: 8.0),
          if (material.nomenclature?.alternativeUnits != null)
            Wrap(
              spacing: 8.0,
              runSpacing: 4.0,
              children: [
                ...material.nomenclature!.alternativeUnits!
                    .asMap()
                    .entries
                    .map((entry) {
                  final unit = entry.value;
                  // final index = entry.key;

                  return Text(
                    '${((unit.ratio ?? 1) * (material.quantities?.ready ?? 1)).toStringAsFixed(2)} ${unit.unit?.getName()}',
                    style: Fonts.labelSmall.merge(
                      TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                      ),
                    ),
                  );
                }),
              ],
            )
        ],
      ),
    );
  }
}
