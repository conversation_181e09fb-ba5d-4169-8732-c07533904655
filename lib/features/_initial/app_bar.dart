import 'package:flutter/material.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';

class InitialAppBar extends StatelessWidget implements PreferredSizeWidget {
  const InitialAppBar({super.key, required this.config});

  final CustomAppBarConfig config;

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      title: config.title,
      description: config.description,
      leftIcon: config.leftIcon,
      rightPart: config.rightPart,
      height: config.height,
      isLoading: config.isLoading,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(config.height);
}
