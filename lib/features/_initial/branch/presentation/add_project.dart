import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/core/helpers/get_full_name.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/client/data/repositories/index.dart';
import 'package:sphere/features/project/data/models/index.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class AddProjectBody extends StatefulWidget {
  const AddProjectBody({super.key, this.branchId, this.refresher});

  final String? branchId;
  final Function()? refresher;

  @override
  State<AddProjectBody> createState() => _AddProjectBodyState();
}

class _AddProjectBodyState extends State<AddProjectBody> {
  final _clientController = TextEditingController(text: '');
  final _headUserController = TextEditingController(text: '');
  final _nameController = TextEditingController(text: '');
  final _descriptionController = TextEditingController(text: '');
  final _templateNumberController = TextEditingController(text: '');
  List<ClientModel> _clients = [];
  List<UserModel> _headUsers = [];
  ClientModel? _selectedClient;
  UserModel? _selectedHeadUser;
  bool _isLoading = false;
  DateTime _releaseDate = DateTime.now();
  bool _calendarIsVisible = false;

  final _debouncer = Debouncer();

  Future<void> _getClients() async {
    if (_clientController.text.isEmpty) return;
    setState(() {
      _isLoading = true;
    });
    final result = await ClientRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _clientController.text,
        clientType: ClientType.client,
      ),
    ));

    if (result?.data != null && result!.data?.items != null) {
      final clients = result.data!.items!;
      setState(() {
        _clients = [
          ...clients,
        ];
        _isLoading = false;
      });
    }
  }

  Future<void> _getHeadUsers() async {
    if (_headUserController.text.isEmpty) {
      setState(() {
        _headUsers = [];
      });
      return;
    }
    setState(() {
      _isLoading = true;
    });
    final result = await UserRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _headUserController.text,
        roles: [UserRole.headmanager],
      ),
    ));

    if (result?.data != null && result!.data?.items != null) {
      final headUsers = result.data!.items!;
      setState(() {
        _headUsers = [
          ...headUsers,
        ];
        _isLoading = false;
      });
    }
  }

  void _createProject() async {
    setState(() {
      _isLoading = true;
    });

    final result = await ProjectRepository.create(ProjectCreateInputModel(
      name: _nameController.text,
      number: _templateNumberController.text,
      clientId: _selectedClient?.id,
      clientName: _selectedClient?.id == null ? _clientController.text : null,
      headUserId: _selectedHeadUser?.id,
      branchId: widget.branchId,
      description: _descriptionController.text,
      releaseDate: _releaseDate,
    ));
    final logger = Logger();
    logger.d(result?.data);

    setState(() {
      _isLoading = false;
    });
    widget.refresher?.call();
  }

  void _onSelectClient(ClientModel client) {
    setState(() {
      _selectedClient = client;
      _clientController.text = client.name ?? '';
      _clients = [];
    });
    // widget.refresher?.call();
  }

  void _onSelectHeadUser(UserModel headUser) {
    setState(() {
      _selectedHeadUser = headUser;
      _headUserController.text = getFullName(
        _selectedHeadUser?.name,
        _selectedHeadUser?.lastName,
        _selectedHeadUser?.patronymic,
      );
      _headUsers = [];
    });
    // widget.refresher?.call();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(
      children: [
        if (_isLoading)
          LinearProgressIndicator(
            minHeight: 2.0,
            borderRadius: BorderRadius.circular(20.0),
            color: isDarkTheme
                ? AppColors.darkSecondary
                : AppColors.lightSecondary,
          ),
        Column(
          children: [
            Expanded(
              child: ListView(padding: const EdgeInsets.all(20.0), children: [
                Text(
                  'Добавление проекта',
                  style: Fonts.titleMedium.merge(
                    const TextStyle(height: 1.6),
                  ),
                ),
                const SizedBox(height: 10.0),
                Text(
                  'Какое название проекта?',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                TextField(
                  controller: _nameController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    hintText: 'Название',
                  ),
                ),
                const SizedBox(height: 24.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Реализация проекта:',
                      style: Fonts.labelSmall,
                    ),
                    GhostButton(
                      onTap: () async {
                        setState(() {
                          _calendarIsVisible = !_calendarIsVisible;
                        });
                      },
                      child: Text(
                        getDateString(_releaseDate),
                        style: Fonts.labelSmall.merge(
                          TextStyle(
                            color: isDarkTheme
                                ? AppColors.darkSecondary
                                : AppColors.lightSecondary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                if (_calendarIsVisible) SizedBox(height: 8.0),
                if (_calendarIsVisible) Divider(height: 1.0),
                if (_calendarIsVisible)
                  CalendarDatePicker(
                    initialDate: _releaseDate,
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(Duration(days: 730)),
                    currentDate: _releaseDate,
                    onDateChanged: (DateTime newDate) {
                      setState(() {
                        _releaseDate = newDate;
                        _calendarIsVisible = false;
                      });
                    },
                  ),
                if (_calendarIsVisible) Divider(height: 1.0),
                SizedBox(height: 24.0),
                Text(
                  'Какой номер чертежа?',
                  style: Fonts.bodyMedium.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                TextField(
                  controller: _templateNumberController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    hintText: 'Номер чертежа',
                  ),
                ),
                const SizedBox(height: 24.0),
                Text(
                  'Какой клиент?',
                  style: Fonts.bodyMedium.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                TextField(
                  controller: _clientController,
                  style: Fonts.labelSmall,
                  onChanged: (value) {
                    if (_selectedClient != null) {
                      setState(() {
                        _selectedClient = null;
                      });
                    }
                    _debouncer.run(_getClients);
                  },
                  decoration: const InputDecoration(
                    hintText: 'Имя клиента',
                  ),
                ),
                if (_selectedClient == null) const SizedBox(height: 6.0),
                if (_selectedClient == null)
                  Text(
                    'Если вы не выбрали клиента из списка, он будет создан и применён к этому проекту',
                    style: Fonts.bodySmall.merge(
                      const TextStyle(
                        height: 1.5,
                        color: AppColors.medium,
                      ),
                    ),
                  ),
                if (_clients.isNotEmpty || _selectedClient != null)
                  const SizedBox(height: 10.0),
                if (_selectedClient != null)
                  Row(children: [
                    CustomChip(
                      selected: true,
                      text: _selectedClient!.name,
                      onTap: () {
                        setState(() {
                          _selectedClient = null;
                          _clientController.text = '';
                        });
                      },
                    ),
                  ]),
                if (_selectedClient != null) const SizedBox(height: 8.0),
                if (_clients.isNotEmpty)
                  Wrap(
                    spacing: 6.0,
                    runSpacing: 6.0,
                    children: _clients
                        .where((client) => client != _selectedClient)
                        .map((client) {
                      return CustomChip(
                        selected: _selectedClient == client,
                        text: client.name,
                        onTap: () => _onSelectClient(client),
                      );
                    }).toList(),
                  ),
                // Head user
                const SizedBox(height: 24.0),
                Text(
                  'Руководитель проекта?',
                  style: Fonts.bodyMedium.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                TextField(
                  controller: _headUserController,
                  style: Fonts.labelSmall,
                  onChanged: (value) {
                    if (_selectedHeadUser != null) {
                      setState(() {
                        _selectedHeadUser = null;
                      });
                    }
                    _debouncer.run(_getHeadUsers);
                  },
                  decoration: const InputDecoration(
                    hintText: 'Имя руководителя проекта',
                  ),
                ),
                // if (_selectedClient == null) const SizedBox(height: 6.0),
                // if (_selectedClient == null)
                //   Text(
                //     'Если вы не выбрали клиента из списка, он будет создан и применён к этому проекту',
                //     style: Fonts.bodySmall.merge(
                //       const TextStyle(
                //         height: 1.5,
                //         color: AppColors.medium,
                //       ),
                //     ),
                //   ),
                if (_headUsers.isNotEmpty || _selectedHeadUser != null)
                  const SizedBox(height: 10.0),
                if (_selectedHeadUser != null)
                  Row(children: [
                    CustomChip(
                      selected: true,
                      text: getFullName(
                        _selectedHeadUser?.name,
                        _selectedHeadUser?.lastName,
                        _selectedHeadUser?.patronymic,
                      ),
                      onTap: () {
                        setState(() {
                          _selectedHeadUser = null;
                          _headUserController.text = '';
                        });
                      },
                    ),
                  ]),
                if (_selectedHeadUser != null) const SizedBox(height: 8.0),
                if (_headUsers.isNotEmpty)
                  Wrap(
                    spacing: 6.0,
                    runSpacing: 6.0,
                    children: _headUsers
                        .where((headUser) => headUser != _selectedHeadUser)
                        .map((headUser) {
                      return CustomChip(
                        selected: _selectedHeadUser == headUser,
                        text: getFullName(
                          headUser.name,
                          headUser.lastName,
                          headUser.patronymic,
                        ),
                        onTap: () => _onSelectHeadUser(headUser),
                      );
                    }).toList(),
                  ),
                const SizedBox(height: 24.0),
                // Text(
                //   'Какой номер чертежа?',
                //   style: Fonts.bodyMedium.merge(
                //     const TextStyle(height: 1.5),
                //   ),
                // ),
                // const SizedBox(height: 10.0),
                // const TextField(
                //   decoration: InputDecoration(
                //     hintText: 'Номер чертежа',
                //   ),
                // ),
                // const SizedBox(height: 24.0),
                TextField(
                  controller: _descriptionController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    hintText: 'Описание',
                  ),
                ),
              ]),
            ),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: CustomElevatedButton(
                type: CustomElevatedButtonTypes.accent,
                onPressed: _createProject,
                text: 'Создать проект',
              ),
            ),
          ],
        ),
      ],
    );
  }
}
