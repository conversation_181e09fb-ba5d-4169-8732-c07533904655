import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/pick_folder.dart';
import 'package:sphere/core/helpers/zip_folder.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/features/project/domain/entities/card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';

class ProjectList extends StatefulWidget {
  final List<ProjectModel> projects;
  final ScrollController scrollController;
  final ValueNotifier<String> currentOpenedId;
  final Function(String) onOpenProject;
  final bool? isLoading;
  final Function(bool)? setLoading;
  final Function()? refresher;

  const ProjectList({
    super.key,
    required this.projects,
    required this.scrollController,
    required this.currentOpenedId,
    required this.onOpenProject,
    this.isLoading,
    this.setLoading,
    this.refresher,
  });

  @override
  State<ProjectList> createState() => _ProjectListState();
}

class _ProjectListState extends State<ProjectList>
    with TickerProviderStateMixin {
  Future<File?> _pickFolder() async {
    final folderPath = await pickFolder();
    if (folderPath == null) return null;
    final files = await scanFolder(folderPath);
    final archive = await zipFolder(files, folderPath);
    return archive;
  }

  void _handleImport(String? projectId) async {
    if (projectId == null) return;
    widget.setLoading?.call(true);
    final file = await _pickFolder();
    if (file != null) {
      ProjectRepository.importStructure(projectId, file);
    }
    widget.setLoading?.call(false);
  }

  Future<void> _delete(String? projectId) async {
    if (projectId == null) return;
    setState(() {
      widget.setLoading?.call(true);
    });

    await ProjectRepository.delete(projectId);

    setState(() {
      widget.setLoading?.call(false);
      widget.refresher?.call();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final isDesktop = MediaQuery.of(context).size.width >= 700;
    final isLarge = MediaQuery.of(context).size.width >= 1200;

    return MasonryGridView.builder(
      controller: widget.scrollController,
      padding: const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 32.0),
      gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: isDesktop
            ? isLarge
                ? 3
                : 2
            : 1,
      ),
      mainAxisSpacing: 12.0,
      crossAxisSpacing: 12.0,
      itemCount: widget.projects.length,
      itemBuilder: (context, index) {
        ProjectModel project = widget.projects[index];

        return ValueListenableBuilder<String>(
          valueListenable: widget.currentOpenedId,
          builder: (context, currentId, _) {
            return ProjectCard(
              isLoading: widget.isLoading,
              project: project,
              isOpened: project.id == currentId,
              isDesktop: isDesktop,
              onOpen: () {
                widget.onOpenProject(
                    project.id == currentId ? '' : project.id ?? '');
              },
              onSecondaryTapDown: (details) {
                CustomDropdownMenu.instance.hide();
                CustomDropdownMenu.instance.show(
                  context: context,
                  items: [
                    CustomDropdownMenuItem(
                      icon: Assets.icons.import,
                      text: 'Импортировать проект',
                      onTap: () {
                        CustomDropdownMenu.instance.hide();
                        _handleImport(project.id);
                      },
                    ),
                    CustomDropdownMenuItem(
                      disabled: true,
                      icon: Assets.icons.description,
                      text: 'Согласовать',
                      onTap: () {},
                    ),
                    CustomDropdownMenuItem(
                      disabled: true,
                      onTap: () {},
                      icon: Assets.icons.notifications,
                      text: 'Подписаться на изменения',
                    ),
                    CustomDropdownMenuItem(
                      icon: Assets.icons.delete,
                      text: 'Удалить',
                      onTap: () {
                        CustomDropdownMenu.instance.hide();
                        showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: const Text(
                                'Удаление изделия',
                                style: Fonts.titleSmall,
                              ),
                              content: Text(
                                'Вы уверены удалить проект: "${project.name}", а также все документы и дочерние элементы?',
                                style: Fonts.bodyMedium,
                              ),
                              actionsAlignment: MainAxisAlignment.spaceAround,
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    context.router.popForced();
                                  },
                                  child: const Text(
                                    'Отмена',
                                    style: Fonts.labelMedium,
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    context.router.popForced();
                                    _delete(project.id);
                                  },
                                  child: Text(
                                    'Да. удалить',
                                    style: Fonts.labelMedium.merge(
                                      TextStyle(
                                        color: isDarkTheme
                                            ? AppColors.darkError
                                            : AppColors.lightError,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                    ),
                  ],
                  position: details.globalPosition,
                );
              },
              onTap: () {
                context.router.push(ProjectRoute(id: project.id ?? '0'));
              },
            );
          },
        );
      },
    );
  }
}
