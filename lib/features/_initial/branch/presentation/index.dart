import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/branch/presentation/add_project.dart';
import 'package:sphere/features/_initial/branch/presentation/project_list.dart';
import 'package:sphere/features/_initial/branch/presentation/search_field.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';

@RoutePage()
class BranchScreen extends StatefulWidget {
  const BranchScreen({super.key});

  @override
  State<BranchScreen> createState() => _BranchScreenState();
}

class _BranchScreenState extends State<BranchScreen>
    with TickerProviderStateMixin, AutoRouteAwareStateMixin<BranchScreen> {
  final ValueNotifier<String> _currentOpenedId = ValueNotifier('');
  final ScrollController _scrollController = ScrollController();
  final ValueNotifier<double> _opacityNotifier = ValueNotifier(0.0);
  List<ProjectModel> _projects = [];
  bool _isLoading = false;

  Future<void> _getProjects() async {
    setState(() {
      _isLoading = true;
    });
    final result = await ProjectRepository.search(SearchModel(
      filters: SearchFiltersModel(
        branchId: '67405bd1f61fcbeb284d4eb5',
      ),
    ));

    if (result?.data != null && result!.data?.items != null) {
      final projects = result.data!.items!;
      setState(() {
        _projects = [
          ...projects,
        ];
        _isLoading = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
    _getProjects();
    // Future.delayed(Duration(milliseconds: 1000), () => _setAppBarConfig());
  }

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    await _getProjects();
    _setAppBarConfig();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    await _getProjects();
    _setAppBarConfig();
  }

  void _setAppBarConfig() {
    // final isMobile = MediaQuery.of(context).size.width < 600;

    final newConfig = CustomAppBarConfig(
      title: 'Нестандартное оборудование',
      description: 'Направление',
      rightPart: CustomAppBarFeatures.getPopupMenu(
        context: context,
        children: [
          CustomDropdownMenuItem(
            disabled: true,
            onTap: () {},
            icon: Assets.icons.edit,
            text: 'Изменить направление',
          ),
          CustomDropdownMenuItem(
            onTap: () {
              CustomDropdownMenu.instance.hide();
              CustomDrawer.instance.show(
                context: context,
                child: AddProjectBody(
                  branchId: '67405bd1f61fcbeb284d4eb5',
                  refresher: _getProjects,
                ),
                vsync: this,
                // alignRight: false,
              );
            },
            icon: Assets.icons.add,
            text: 'Добавить проект',
          ),
          CustomDropdownMenuItem(
            onTap: _getProjects,
            icon: Assets.icons.repeat,
            text: 'Обновить',
          ),
          CustomDropdownMenuItem(
            disabled: true,
            onTap: () {},
            icon: Assets.icons.notifications,
            text: 'Подписаться на изменения',
          ),
        ],
      ),
      isLoading: _isLoading,
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  void _handleScroll() {
    _opacityNotifier.value = _scrollController.offset > 0 ? 1.0 : 0.0;
  }

  void _onOpenProject(String id) {
    _currentOpenedId.value = id;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Wrapper(
      body: Column(
        children: [
          const BranchSearchField(),
          Expanded(
            child: ValueListenableBuilder<double>(
              valueListenable: _opacityNotifier,
              builder: (context, opacity, child) {
                return ShaderMask(
                  shaderCallback: (Rect rect) {
                    return LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        isDarkTheme
                            ? AppColors.darkBackground
                            : AppColors.lightBackground,
                        isDarkTheme
                            ? AppColors.darkBackground.withValues(alpha: 0)
                            : AppColors.lightBackground.withValues(alpha: 0),
                      ],
                      stops: [0.0, 0.1 * opacity],
                    ).createShader(rect);
                  },
                  blendMode: BlendMode.dstOut,
                  child: ProjectList(
                    setLoading: (state) {
                      setState(() {
                        _isLoading = state;
                      });
                    },
                    isLoading: _isLoading,
                    projects: _projects.isEmpty
                        ? _isLoading
                            ? [
                                const ProjectModel(),
                                const ProjectModel(),
                                const ProjectModel(),
                                const ProjectModel(),
                                const ProjectModel(),
                                const ProjectModel()
                              ]
                            : []
                        : _projects,
                    scrollController: _scrollController,
                    currentOpenedId: _currentOpenedId,
                    onOpenProject: _onOpenProject,
                    refresher: _getProjects,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
