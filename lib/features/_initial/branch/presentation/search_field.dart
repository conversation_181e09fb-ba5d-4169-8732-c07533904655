import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class BranchSearchField extends StatelessWidget {
  const BranchSearchField({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Opacity(
        opacity: 0.5,
        child: TextField(
          enabled: false,
          style: Fonts.labelSmall,
          decoration: InputDecoration(
            hintText: 'Поиск',
            suffixIcon: Align(
              widthFactor: 1.0,
              alignment: Alignment.center,
              child: SVG(
                Assets.icons.search,
                color: AppColors.medium,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
