// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BlocInitialState {
  CustomAppBarConfig get config => throw _privateConstructorUsedError;

  /// Create a copy of BlocInitialState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BlocInitialStateCopyWith<BlocInitialState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocInitialStateCopyWith<$Res> {
  factory $BlocInitialStateCopyWith(
          BlocInitialState value, $Res Function(BlocInitialState) then) =
      _$BlocInitialStateCopyWithImpl<$Res, BlocInitialState>;
  @useResult
  $Res call({CustomAppBarConfig config});

  $CustomAppBarConfigCopyWith<$Res> get config;
}

/// @nodoc
class _$BlocInitialStateCopyWithImpl<$Res, $Val extends BlocInitialState>
    implements $BlocInitialStateCopyWith<$Res> {
  _$BlocInitialStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocInitialState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = null,
  }) {
    return _then(_value.copyWith(
      config: null == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as CustomAppBarConfig,
    ) as $Val);
  }

  /// Create a copy of BlocInitialState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomAppBarConfigCopyWith<$Res> get config {
    return $CustomAppBarConfigCopyWith<$Res>(_value.config, (value) {
      return _then(_value.copyWith(config: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BlocInitialStateImplCopyWith<$Res>
    implements $BlocInitialStateCopyWith<$Res> {
  factory _$$BlocInitialStateImplCopyWith(_$BlocInitialStateImpl value,
          $Res Function(_$BlocInitialStateImpl) then) =
      __$$BlocInitialStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({CustomAppBarConfig config});

  @override
  $CustomAppBarConfigCopyWith<$Res> get config;
}

/// @nodoc
class __$$BlocInitialStateImplCopyWithImpl<$Res>
    extends _$BlocInitialStateCopyWithImpl<$Res, _$BlocInitialStateImpl>
    implements _$$BlocInitialStateImplCopyWith<$Res> {
  __$$BlocInitialStateImplCopyWithImpl(_$BlocInitialStateImpl _value,
      $Res Function(_$BlocInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocInitialState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = null,
  }) {
    return _then(_$BlocInitialStateImpl(
      config: null == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as CustomAppBarConfig,
    ));
  }
}

/// @nodoc

class _$BlocInitialStateImpl extends _BlocInitialState {
  const _$BlocInitialStateImpl(
      {this.config = const CustomAppBarConfig(title: 'Default title')})
      : super._();

  @override
  @JsonKey()
  final CustomAppBarConfig config;

  @override
  String toString() {
    return 'BlocInitialState(config: $config)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlocInitialStateImpl &&
            (identical(other.config, config) || other.config == config));
  }

  @override
  int get hashCode => Object.hash(runtimeType, config);

  /// Create a copy of BlocInitialState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BlocInitialStateImplCopyWith<_$BlocInitialStateImpl> get copyWith =>
      __$$BlocInitialStateImplCopyWithImpl<_$BlocInitialStateImpl>(
          this, _$identity);
}

abstract class _BlocInitialState extends BlocInitialState {
  const factory _BlocInitialState({final CustomAppBarConfig config}) =
      _$BlocInitialStateImpl;
  const _BlocInitialState._() : super._();

  @override
  CustomAppBarConfig get config;

  /// Create a copy of BlocInitialState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BlocInitialStateImplCopyWith<_$BlocInitialStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
