import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';

part 'bloc.freezed.dart';
part 'events.dart';
part 'state.dart';

class BlocInitial extends Bloc<BlocInitialEvents, BlocInitialState> {
  BlocInitial()
      : super(const BlocInitialState(
          config: CustomAppBarConfig(title: 'Сфера'),
        )) {
    on<SetAppBarConfig>((event, emit) {
      emit(state.copyWith(config: event.config));
    });
  }
}
