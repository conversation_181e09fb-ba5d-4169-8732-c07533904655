import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class AdminScreen extends StatefulWidget {
  const AdminScreen({super.key});

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen> {
  bool _isLoading = false;
  List<UserModel> _users = [];

  void _refresh({
    String? query,
  }) async {
    setState(() {
      _isLoading = true;
    });

    final result = await UserRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: query,
      ),
    ));

    setState(() {
      if (result?.data?.items != null) _users = [...result!.data!.items!];
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();

    _refresh();
  }

  void _blockUser(String userId) async {
    setState(() {
      _isLoading = true;
    });

    await UserRepository.block(userId);

    setState(() {
      _isLoading = false;
    });

    _refresh();
  }

  void _unblockUser(String userId) async {
    setState(() {
      _isLoading = true;
    });

    await UserRepository.unblock(userId);

    setState(() {
      _isLoading = false;
    });

    _refresh();
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CustomAppBar(
        title: 'Администрирование',
        isLoading: _isLoading,
      ),
      body: Column(children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 2),
          child: TextField(
            onChanged: (value) {
              if (value.isEmpty) {
                _refresh(query: null);
              } else {
                _refresh(query: value);
              }
            },
            style: Fonts.labelSmall,
            decoration: InputDecoration(
              hintText: 'Поиск',
              suffixIcon: Align(
                widthFactor: 1.0,
                alignment: Alignment.center,
                child: SVG(
                  Assets.icons.search,
                  color: AppColors.medium,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 4.0),
        // Divider(height: 1.0),
        Expanded(
          child: ListView.separated(
            padding: EdgeInsets.all(12.0),
            itemCount: _users.length,
            itemBuilder: (context, index) {
              final user = _users[index];
              return UserCard(
                isLoading: _isLoading,
                user: user,
                onSecondaryTapDown: (details) {
                  CustomDropdownMenu.instance.show(
                    context: context,
                    items: [
                      CustomDropdownMenuItem(
                        icon: Assets.icons.copy,
                        text: 'Скопировать id',
                        onTap: () {
                          Clipboard.setData(
                            ClipboardData(text: user.id ?? "id not found"),
                          );
                          CustomDropdownMenu.instance.hide();
                        },
                      ),
                      // CustomDropdownMenuItem(
                      //   icon: Assets.icons.edit,
                      //   text: 'Изменить пользователя',
                      //   onTap: () {
                      //     CustomDropdownMenu.instance.hide();
                      //   },
                      // ),
                      if (user.isBlocked != true)
                        CustomDropdownMenuItem(
                          icon: Assets.icons.personRemove,
                          text: 'Заблокировать пользователя',
                          onTap: () {
                            if (user.id != null) _blockUser(user.id!);
                            CustomDropdownMenu.instance.hide();
                          },
                        ),
                      if (user.isBlocked == true)
                        CustomDropdownMenuItem(
                          icon: Assets.icons.personRemove,
                          text: 'Разблокировать пользователя',
                          onTap: () {
                            if (user.id != null) _unblockUser(user.id!);
                            CustomDropdownMenu.instance.hide();
                          },
                        ),
                    ],
                    position: details.globalPosition,
                  );
                },
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(height: 8.0);
            },
          ),
        ),
      ]),
    );
  }
}
