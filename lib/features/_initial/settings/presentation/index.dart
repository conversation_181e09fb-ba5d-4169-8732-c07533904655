import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_notifier/local_notifier.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/auth/data/repositories/index.dart';
import 'package:sphere/features/auth/presentation/bloc/bloc.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

@RoutePage()
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with AutoRouteAwareStateMixin<SettingsScreen> {
  String _version = '';

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    _setAppBarConfig();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    _setAppBarConfig();
  }

  void _setAppBarConfig() {
    // final isMobile = MediaQuery.of(context).size.width < 600;

    final newConfig = CustomAppBarConfig(
      title: 'Настройки',
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  Future<PackageInfo> _getAppInfo() async {
    PackageInfo appInfo = await PackageInfo.fromPlatform();
    setState(() {
      _version = appInfo.version;
    });
    return appInfo;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    _getAppInfo();

    return BlocBuilder<BlocAuth, BlocAuthState>(builder: (context, state) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child:
            Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
          UserCard(user: state.user),
          const SizedBox(height: 16.0),
          Row(
            children: [
              GhostButton(
                onTap: () async {
                  await Clipboard.setData(
                    ClipboardData(text: state.user.id ?? 'ОШИБКА, БЕЗ ID'),
                  );
                  ScaffoldMessenger.of(context)
                      .showSnackBar(const CustomSnackbar(
                    text: 'Скопировано!',
                  ).toSnackBar(context));
                },
                child: Row(children: [
                  SVG(Assets.icons.copy),
                  const SizedBox(width: 8.0),
                  const Text('Копировать USER ID', style: Fonts.labelSmall),
                ]),
              ),
            ],
          ),
          if (state.user.role == UserRole.admin) const SizedBox(height: 24.0),
          if (state.user.role == UserRole.admin)
            CustomElevatedButton(
              onPressed: () {
                context.router.push(AdminRoute());
              },
              text: 'Администрирование',
              // child: SVG(Assets.icons.settings),
            ),
          const SizedBox(height: 24.0),
          CustomElevatedButton(
            onPressed: () {
              localNotifier.notify(LocalNotification(
                  title: 'Тестовое оповещение',
                  subtitle: 'Subtitle',
                  body: 'Body',
                  actions: [
                    LocalNotificationAction(
                      text: 'Открыть',
                    ),
                  ]));
            },
            text: 'Протестировать уведомления',
          ),
          const SizedBox(height: 24.0),
          CustomElevatedButton(
            onPressed: () async {
              const prefs = FlutterSecureStorage();
              final refreshToken = await prefs.read(key: 'refreshToken');
              await prefs.write(key: 'refreshToken', value: '');
              AuthRepository.logout(refreshToken: refreshToken ?? '');
              context.router.pushNamed('/auth');
            },
            text: 'Выйти из аккаунта',
            type: CustomElevatedButtonTypes.attention,
          ),
          const SizedBox(height: 24.0),
          Text(
            _version,
            textAlign: TextAlign.center,
            style: Fonts.bodySmall.merge(
              TextStyle(
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
              ),
            ),
          ),
        ]),
      );
    });
  }
}
