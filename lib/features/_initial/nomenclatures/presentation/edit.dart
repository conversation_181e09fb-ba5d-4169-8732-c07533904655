import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/repositories/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/dropdown_button.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

class NomenclatureEditBody extends StatefulWidget {
  const NomenclatureEditBody({super.key, this.onSave, required this.material});

  final void Function()? onSave;
  final NomenclatureModel material;

  @override
  State<NomenclatureEditBody> createState() => _NomenclatureEditBodyState();
}

class _NomenclatureEditBodyState extends State<NomenclatureEditBody> {
  bool _isLoading = false;
  final _nameController = TextEditingController();
  final _altNamesController = TextEditingController();
  UnitType _baseUnit = UnitType.kg;
  List<UnitModel> _units = [];
  bool _dropdownIsOpened = false;
  Future<void> Function()? toggleDropdown;
  List<TextEditingController> unitControllers =
      UnitType.values.map((_) => TextEditingController()).toList();

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.material.name ?? '';
    _altNamesController.text = widget.material.altNames?.join(', ') ?? '';
    _baseUnit = widget.material.baseUnit ?? UnitType.kg;
    _units = widget.material.alternativeUnits ?? [];

    UnitType.values.asMap().entries.forEach((entry) {
      final type = entry.value;
      final index = entry.key;
      final foundUnitIndex = _units.indexWhere((unit) => unit.unit == type);

      if (foundUnitIndex >= 0) {
        unitControllers[index].text = _units[foundUnitIndex].ratio.toString();
      }
    });
  }

  // void _onChangeBaseUnit() {
  //   _units.clear();
  // }

  void _onChangeUnitRatio(UnitType type, double ratio) {
    final index = _units.indexWhere((element) => element.unit == type);

    if (index >= 0) {
      if (ratio > 0) {
        _units[index].ratio = ratio;
      } else {
        _units.removeAt(index);
      }
    } else {
      if (ratio <= 0) return;
      _units.add(UnitModel(
        unit: type,
        ratio: ratio,
      ));
    }
  }

  List<String>? _getAltNamesFromString(String names) {
    if (names.isEmpty) return null;
    List<String> list = names.split(',');
    list = list.map((name) => name.trim()).toList();
    return list.isEmpty ? null : list;
  }

  Future<bool> _save() async {
    if (widget.material.id == null) return false;
    setState(() {
      _isLoading = true;
    });

    final alternativeUnits =
        _units.takeWhile((unit) => unit.unit != _baseUnit).toList();
    final altNames = _getAltNamesFromString(_altNamesController.text);

    final result = await NomenclaturesRepository.edit(
      widget.material.id!,
      NomenclatureModel(
        name: _nameController.text,
        altNames: altNames,
        baseUnit: _baseUnit,
        alternativeUnits: alternativeUnits.isNotEmpty ? alternativeUnits : null,
      ),
    );
    if ((result?.statusCode ?? 301) >= 300) {
      setState(() {
        _isLoading = false;
      });
      CustomDrawer.instance.hide();
      ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
        text: 'Ошибка изменения номенклатуры, проверьте входные данные',
      ).toSnackBar(context));
      return false;
    }

    setState(() {
      _isLoading = false;
    });
    CustomDrawer.instance.hide();
    ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
      text: 'Успешно изменена номенклатура материала "${_nameController.text}"',
    ).toSnackBar(context));
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(children: [
        Expanded(
          child: ListView(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0.0),
            children: [
              Text(
                'Изменение номенклатуры',
                style: Fonts.titleMedium.merge(
                  const TextStyle(height: 1.6),
                ),
              ),
              // name
              const SizedBox(height: 10.0),
              Text(
                'Название материала',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 10.0),
              TextField(
                controller: _nameController,
                style: Fonts.labelSmall,
                decoration: const InputDecoration(
                  hintText: 'Имя',
                ),
              ),
              // alt names
              const SizedBox(height: 24.0),
              Text(
                'Другие названия (альтернативные)',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              Text(
                'Перечислите через запятую (",") все альтернативные названия, если они есть.',
                style: Fonts.bodySmall.merge(
                  TextStyle(
                    color: isDarkTheme
                        ? AppColors.darkDescription
                        : AppColors.lightDescription,
                  ),
                ),
              ),
              const SizedBox(height: 10.0),
              TextField(
                controller: _altNamesController,
                style: Fonts.labelSmall,
                decoration: const InputDecoration(
                  hintText: 'Другие имена',
                ),
              ),
              // base unit
              const SizedBox(height: 24.0),
              Row(
                children: [
                  Text(
                    'Базовая единица измерения',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  ),
                  const SizedBox(width: 10.0),
                  CustomDropDownButton(
                    isOpened: _dropdownIsOpened,
                    setIsOpened: (value) {
                      setState(() {
                        _dropdownIsOpened = value;
                      });
                    },
                    onToggleOpen: (toggleFunction) {
                      toggleDropdown = toggleFunction;
                    },
                    text: _baseUnit.getName(),
                    children: UnitType.values.map((value) {
                      return CustomDropdownMenuItem(
                        text: value.getName(),
                        onTap: () {
                          toggleDropdown?.call();
                          setState(() {
                            _baseUnit = value;
                          });
                        },
                      );
                    }).toList(),
                  ),
                ],
              ),
              // ratios
              const SizedBox(height: 24.0),
              Text(
                'Коэффициенты (Соотношения)',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              SizedBox(height: 10.0),
              Row(
                children: UnitType.values.asMap().entries.map((entry) {
                  final type = entry.value;
                  final index = entry.key;
                  final controller = unitControllers[index];

                  if (type == _baseUnit) {
                    controller.text = '1.0';
                  }

                  return Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            enabled: _baseUnit != type,
                            controller: controller,
                            onChanged: (value) {
                              final ratio = double.tryParse(value);
                              if (ratio == null) {
                                _onChangeUnitRatio(type, 0);
                                return;
                              }

                              _onChangeUnitRatio(type, ratio);
                            },
                            style: Fonts.labelSmall,
                            decoration: InputDecoration(
                              labelText: type.getName(),
                            ),
                          ),
                        ),
                        if (index != UnitType.values.length - 1)
                          SizedBox(width: 8.0),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () async {
              final success = await _save();
              if (success) widget.onSave?.call();
            },
            text: 'Изменить',
          ),
        ),
      ]),
    ]);
  }
}
