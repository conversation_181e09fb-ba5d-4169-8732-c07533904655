import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class NomenclatureCard extends StatelessWidget {
  const NomenclatureCard({
    super.key,
    required this.nomenclature,
    this.isLoading = false,
    this.onTap,
    this.onSecondaryTapDown,
  });

  final NomenclatureModel nomenclature;
  final bool isLoading;
  final void Function()? onTap;
  final void Function(TapDownDetails details)? onSecondaryTapDown;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      isLoading: isLoading,
      onTap: onTap,
      onSecondaryTapDown: onSecondaryTapDown,
      padding: EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            nomenclature.name ?? 'NomenclatureName',
            style: Fonts.labelMedium,
          ),
          // SizedBox(height: ,)
          if (nomenclature.altNames != null &&
              nomenclature.altNames!.isNotEmpty)
            Text(
              nomenclature.altNames!.join(', '),
              style: Fonts.bodySmall.merge(TextStyle(
                color: AppColors.medium,
              )),
            ),
          if (nomenclature.alternativeUnits != null) SizedBox(height: 10.0),
          if (nomenclature.alternativeUnits != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SVG(Assets.icons.repeat),
                Text(
                  '${nomenclature.baseUnit?.getName()}',
                  style: Fonts.labelSmall.merge(
                    TextStyle(
                      color: isDarkTheme
                          ? AppColors.darkSecondary
                          : AppColors.lightSecondary,
                    ),
                  ),
                ),
                ...nomenclature.alternativeUnits!.asMap().entries.map((entry) {
                  final unit = entry.value;
                  // final index = entry.key;

                  return Text(
                    '${unit.ratio} ${unit.unit?.getName()}',
                    style: Fonts.labelSmall,
                  );
                })
              ],
            ),
        ],
      ),
    );
  }
}
