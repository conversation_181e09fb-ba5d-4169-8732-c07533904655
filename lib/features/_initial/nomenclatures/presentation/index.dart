import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/repositories/index.dart';
import 'package:sphere/features/_initial/nomenclatures/presentation/add.dart';
import 'package:sphere/features/_initial/nomenclatures/presentation/edit.dart';
import 'package:sphere/features/_initial/nomenclatures/presentation/nomenclature_card.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

@RoutePage()
class NomenclaturesPage extends StatefulWidget {
  const NomenclaturesPage({super.key});

  @override
  State<NomenclaturesPage> createState() => _NomenclaturesPageState();
}

class _NomenclaturesPageState extends State<NomenclaturesPage>
    with TickerProviderStateMixin, AutoRouteAwareStateMixin<NomenclaturesPage> {
  bool _isLoading = false;
  List<NomenclatureModel> materials = [];

  void _delete(String id) async {
    setState(() {
      _isLoading = true;
    });

    final response = await NomenclaturesRepository.delete(id);
    if (response?.data == true) {
      ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
        text: 'Успешно',
      ).toSnackBar(context));
    } else {
      ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
        text: 'Не удалось',
      ).toSnackBar(context));
    }

    setState(() {
      _isLoading = false;
    });
    _getMaterials();
  }

  Future<void> _getMaterials({
    String? searchInput,
  }) async {
    setState(() {
      _isLoading = true;
    });

    final response = await NomenclaturesRepository.search(
      SearchModel(filters: SearchFiltersModel(query: searchInput)),
    );
    final result = response?.data?.items;

    setState(() {
      if (result != null) materials = result;
      if (response?.data?.message != null) {
        ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
          text: 'Ошибка получения материалов: ${response!.data!.message}',
        ).toSnackBar(context));
      }
      _isLoading = false;
    });
  }

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    await _getMaterials();
    _setAppBarConfig();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    await _getMaterials();
    _setAppBarConfig();
  }

  void _setAppBarConfig() {
    // final isMobile = MediaQuery.of(context).size.width < 600;

    final newConfig = CustomAppBarConfig(
      title: 'Номенклатуры',
      description: 'Список всех известных номенклатур',
      rightPart: CustomAppBarFeatures.getPopupMenu(
        context: context,
        children: [
          CustomDropdownMenuItem(
            onTap: () {
              CustomDropdownMenu.instance.hide();
              CustomDrawer.instance.show(
                context: context,
                vsync: this,
                child: NomenclaturesAddBody(onSave: () => _getMaterials()),
              );
            },
            icon: Assets.icons.add,
            text: 'Добавить номенклатуру',
          ),
        ],
      ),
      isLoading: _isLoading,
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final isDesktop = MediaQuery.of(context).size.width >= 700;
    final isLarge = MediaQuery.of(context).size.width >= 1200;

    return Wrapper(
      body: Column(children: [
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: TextField(
            onChanged: (value) {
              _getMaterials(
                searchInput: value.trim().isEmpty ? null : value.trim(),
              );
            },
            style: Fonts.labelSmall,
            decoration: InputDecoration(
              hintText: 'Поиск',
              suffixIcon: Align(
                widthFactor: 1.0,
                alignment: Alignment.center,
                child: SVG(
                  Assets.icons.search,
                  color: AppColors.medium,
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: MasonryGridView.builder(
            padding: const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 32.0),
            gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: isDesktop
                  ? isLarge
                      ? 3
                      : 2
                  : 1,
            ),
            mainAxisSpacing: 12.0,
            crossAxisSpacing: 12.0,
            itemCount: materials.isNotEmpty ? materials.length : 6,
            itemBuilder: (context, index) {
              final material =
                  materials.isNotEmpty ? materials[index] : NomenclatureModel();
              return NomenclatureCard(
                nomenclature: material,
                isLoading: _isLoading,
                onSecondaryTapDown: (details) {
                  CustomDropdownMenu.instance.show(
                    context: context,
                    items: [
                      CustomDropdownMenuItem(
                        icon: Assets.icons.edit,
                        text: 'Изменить',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          CustomDrawer.instance.show(
                            context: context,
                            vsync: this,
                            child: NomenclatureEditBody(
                              material: material,
                              onSave: () => _getMaterials(),
                            ),
                          );
                        },
                      ),
                      CustomDropdownMenuItem(
                        icon: Assets.icons.delete,
                        text: 'Удалить',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          if (material.id == null) return;
                          showDialog(
                            context: context,
                            builder: (context) {
                              return AlertDialog(
                                title: const Text(
                                  'Удаление номенклатуры',
                                  style: Fonts.titleSmall,
                                ),
                                content: Text(
                                  'Вы уверены удалить номенклатуру: "${material.name}"?',
                                  style: Fonts.bodyMedium,
                                ),
                                actionsAlignment: MainAxisAlignment.spaceAround,
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      context.router.popForced();
                                    },
                                    child: const Text(
                                      'Отмена',
                                      style: Fonts.labelMedium,
                                    ),
                                  ),
                                  TextButton(
                                    // color: isDarkTheme
                                    //     ? AppColors.darkError
                                    //     : AppColors.lightError,
                                    onPressed: () {
                                      context.router.popForced();
                                      _delete(material.id!);
                                    },
                                    child: Text(
                                      'Да. удалить',
                                      style: Fonts.labelMedium.merge(
                                        TextStyle(
                                          color: isDarkTheme
                                              ? AppColors.darkError
                                              : AppColors.lightError,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      ),
                    ],
                    position: details.globalPosition,
                  );
                },
              );
            },
          ),
        )
      ]),
    );
  }
}
