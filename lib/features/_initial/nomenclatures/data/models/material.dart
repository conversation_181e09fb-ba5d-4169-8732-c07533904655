import 'package:json_annotation/json_annotation.dart';

part 'material.g.dart';

@JsonSerializable(includeIfNull: false)
class NomenclatureModel {
  @JsonKey(name: '_id')
  final String? id;
  final String? name;
  final bool? visible;
  final List<String>? altNames;
  final UnitType? baseUnit;
  final List<UnitModel>? alternativeUnits;

  const NomenclatureModel({
    this.id,
    this.name,
    this.visible,
    this.baseUnit,
    this.alternativeUnits,
    this.altNames,
  });

  factory NomenclatureModel.fromJson(Map<String, dynamic> json) =>
      _$NomenclatureModelFromJson(json);
  Map<String, dynamic> toJson() => _$NomenclatureModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class UnitModel {
  late double? ratio;
  final UnitType? unit;

  UnitModel({
    this.ratio,
    this.unit,
  });

  factory UnitModel.fromJson(Map<String, dynamic> json) =>
      _$UnitModelFromJson(json);
  Map<String, dynamic> toJson() => _$UnitModelToJson(this);
}

enum UnitType {
  @JsonValue('kg')
  kg,
  @JsonValue('pcs')
  pcs,
  @JsonValue('m2')
  m2,
  @JsonValue('m3')
  m3,
  @JsonValue('l')
  l;

  String getName() {
    switch (this) {
      case UnitType.kg:
        return 'кг';
      case UnitType.pcs:
        return 'шт.';
      case UnitType.m2:
        return 'м²';
      case UnitType.m3:
        return 'м³';
      case UnitType.l:
        return 'л';
    }
  }
}

enum NomenclatureType {
  materials,
  standard,
  other;

  String getName() {
    switch (this) {
      case NomenclatureType.materials:
        return 'Матераил';
      case NomenclatureType.standard:
        return 'Стандартное изделие';
      case NomenclatureType.other:
        return 'Другое изделие';
    }
  }
}
