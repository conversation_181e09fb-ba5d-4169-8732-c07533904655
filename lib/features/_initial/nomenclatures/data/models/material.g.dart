// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'material.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NomenclatureModel _$NomenclatureModelFromJson(Map<String, dynamic> json) =>
    NomenclatureModel(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      visible: json['visible'] as bool?,
      baseUnit: $enumDecodeNullable(_$UnitTypeEnumMap, json['baseUnit']),
      alternativeUnits: (json['alternativeUnits'] as List<dynamic>?)
          ?.map((e) => UnitModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      altNames: (json['altNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$NomenclatureModelToJson(NomenclatureModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.visible case final value?) 'visible': value,
      if (instance.altNames case final value?) 'altNames': value,
      if (_$UnitTypeEnumMap[instance.baseUnit] case final value?)
        'baseUnit': value,
      if (instance.alternativeUnits case final value?)
        'alternativeUnits': value,
    };

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};

UnitModel _$UnitModelFromJson(Map<String, dynamic> json) => UnitModel(
      ratio: (json['ratio'] as num?)?.toDouble(),
      unit: $enumDecodeNullable(_$UnitTypeEnumMap, json['unit']),
    );

Map<String, dynamic> _$UnitModelToJson(UnitModel instance) => <String, dynamic>{
      if (instance.ratio case final value?) 'ratio': value,
      if (_$UnitTypeEnumMap[instance.unit] case final value?) 'unit': value,
    };
