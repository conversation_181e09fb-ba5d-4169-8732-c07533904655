import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';

part 'index.g.dart';

@JsonSerializable(includeIfNull: false)
class SearchNomenclaturesOutputModel {
  final List<NomenclatureModel>? items;
  final int? totalItems;
  final String? message;

  const SearchNomenclaturesOutputModel({
    this.items,
    this.totalItems,
    this.message,
  });

  factory SearchNomenclaturesOutputModel.fromJson(Map<String, dynamic> json) =>
      _$SearchNomenclaturesOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$SearchNomenclaturesOutputModelToJson(this);
}
