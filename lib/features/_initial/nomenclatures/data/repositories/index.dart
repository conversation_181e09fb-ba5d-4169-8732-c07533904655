import 'package:dio/dio.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/index.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class NomenclaturesRepository {
  static Future<Response<SearchNomenclaturesOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<SearchNomenclaturesOutputModel>(
      url: '/materials/search',
      body: body,
      method: 'POST',
      fromJson: SearchNomenclaturesOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<NomenclatureModel>?> create(
    NomenclatureModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<NomenclatureModel>(
      url: '/materials/create',
      body: body,
      method: 'POST',
      fromJson: NomenclatureModel.fromJson,
    );

    return request;
  }

  static Future<Response<NomenclatureModel>?> edit(
    String id,
    NomenclatureModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<NomenclatureModel>(
      url: '/materials/edit?materialId=$id',
      body: body,
      method: 'POST',
      fromJson: NomenclatureModel.fromJson,
    );

    return request;
  }

  static Future<Response<bool>?> delete(
    String id,
  ) async {
    final request = await API.request<bool>(
      url: '/materials/delete?materialId=$id',
      method: 'POST',
      // fromJson: MaterialModel.fromJson,
    );

    return request;
  }
}
