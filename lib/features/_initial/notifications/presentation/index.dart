import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/notifications/data/models/index.dart';
import 'package:sphere/features/_initial/notifications/data/models/notification.dart';
import 'package:sphere/features/_initial/notifications/data/repositories/index.dart';
import 'package:sphere/features/_initial/notifications/presentation/card.dart';
import 'package:sphere/features/_initial/notifications/presentation/widgets/actions.dart';
import 'package:sphere/features/auth/presentation/bloc/bloc.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';

@RoutePage()
class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen>
    with AutoRouteAwareStateMixin<NotificationScreen> {
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMore = true;
  final int _limit = 20;
  int _offset = 0;
  NotificationsModel notifications = NotificationsModel();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _getNotifications();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _getNotifications({bool loadMore = false}) async {
    if (_isLoading || (loadMore && _isLoadingMore)) return;

    setState(() {
      if (loadMore) {
        _isLoadingMore = true;
      } else {
        _isLoading = true;
      }
    });

    final userId = context.read<BlocAuth>().state.user.id;
    if (userId == null) {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
      });
      return;
    }

    final result = await NotificationsRepository.search(
      SearchModel(
        filters: SearchFiltersModel(
          userId: userId,
        ),
        pagination: SearchPaginationModel(
          limit: _limit,
          offset: loadMore ? _offset : 0,
        ),
      ),
    );

    final preparedData = result?.data;

    setState(() {
      if (preparedData != null) {
        if (loadMore) {
          notifications = notifications.copyWith(items: [
            ...notifications.items ?? [],
            ...preparedData.items ?? []
          ]);
        } else {
          notifications = preparedData;
        }
        _offset = notifications.items?.length ?? 0;
        _hasMore = (preparedData.items?.length ?? 0) >= _limit;
      }
      _isLoading = false;
      _isLoadingMore = false;
    });

    if (!loadMore) {
      _setAppBarConfig();
    }
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
            _scrollController.position.maxScrollExtent &&
        _hasMore &&
        !_isLoadingMore) {
      _getNotifications(loadMore: true);
    }
  }

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    await _getNotifications();
    _setAppBarConfig();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    await _getNotifications();
    _setAppBarConfig();
  }

  void _setAppBarConfig() {
    // final isMobile = MediaQuery.of(context).size.width < 600;

    final newConfig = CustomAppBarConfig(
      title: 'Уведомления',
      description: '${notifications.items?.length ?? 0}',
      rightPart: Row(
        children: [
          TextButton(
            onPressed: () {
              final id = context.read<BlocAuth>().state.user.id;

              NotificationsRepository.sendTest(
                SendTestNotificationModel(
                  title: 'Заголовок тестового уведомления',
                  message:
                      'Тело тестового уведомления, здесь, обычно, имеется много текста, например, вот столько',
                  userIds: [
                    id ?? '67401ba7cc5adfc07d13d7ed',
                  ],
                ),
              );
            },
            child: Text('Отправить тестовое уведомление'),
          )
          // CustomAppBarFeatures.getPopupMenu(
          // context: context,
          // children: [
          //   CustomDropdownMenuItem(
          //     onTap: () {
          //       CustomDropdownMenu.instance.hide();
          //       CustomDrawer.instance.show(
          //         context: context,
          //         vsync: this,
          //         child: NomenclaturesAddBody(),
          //       );
          //     },
          //     icon: Assets.icons.add,
          //     text: 'Добавить материал',
          //   ),
        ],
      ),
      isLoading: _isLoading,
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ActionsSection(),
        ),
        const VerticalDivider(width: 24.0),
        Expanded(
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Уведомления',
                    style: Fonts.titleLarge,
                  ),
                ),
              ),
              if (_isLoading && notifications.items == null)
                const SliverFillRemaining(
                  child: Center(child: CircularProgressIndicator()),
                ),
              if (notifications.items == null || notifications.items!.isEmpty)
                SliverFillRemaining(
                  child: Center(
                    child: Text(
                      'Уведомлений пока нет',
                      style: Fonts.bodySmall.merge(
                        const TextStyle(color: AppColors.medium),
                      ),
                    ),
                  ),
                ),
              if (notifications.items != null &&
                  notifications.items!.isNotEmpty)
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final notification = notifications.items![index];
                      return Column(
                        children: [
                          NotificationCard(data: notification),
                          if (index < notifications.items!.length - 1)
                            const SizedBox(height: 12.0),
                        ],
                      );
                    },
                    childCount: notifications.items?.length ?? 0,
                  ),
                ),
              if (_isLoadingMore)
                const SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(child: CircularProgressIndicator()),
                  ),
                ),
              if (!_hasMore && notifications.items?.isNotEmpty == true)
                const SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        'Все уведомления загружены',
                        style: TextStyle(color: AppColors.medium),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
