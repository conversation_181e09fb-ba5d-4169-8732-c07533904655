import 'package:flutter/widgets.dart';
import 'package:sphere/features/_initial/notifications/presentation/widgets/action_card.dart';
import 'package:sphere/shared/styles/fonts.dart';

class ActionsSection extends StatelessWidget {
  const ActionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Padding(
        padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 8.0),
        child: const Text('Действия', style: Fonts.titleMedium),
      ),
      Expanded(
        child: ListView.separated(
          padding: EdgeInsets.all(12.0),
          itemBuilder: (context, i) {
            return ActionCard(title: 'Title $i');
          },
          separatorBuilder: (context, i) {
            return const SizedBox(height: 12.0);
          },
          itemCount: 100,
        ),
      )
    ]);
  }
}
