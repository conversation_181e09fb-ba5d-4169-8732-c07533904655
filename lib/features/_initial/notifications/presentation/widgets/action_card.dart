import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class ActionCard extends StatelessWidget {
  const ActionCard({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GhostButton(
            onTap: () {},
            child: Text(
              title,
              style: Fonts.titleSmall.merge(TextStyle(
                color: AppColors.lightSecondary,
              )),
            ),
          ),
          Si<PERSON><PERSON>ox(height: 12.0),
          UserCard(
            minified: true,
            wrapped: false,
            user: User<PERSON><PERSON>l(
              name: 'Name',
              lastName: 'Lastname',
              patronymic: 'Patronymic',
              role: UserRole.worker,
              department: Department.ogk,
            ),
          ),
          SizedBox(height: 12.0),
          Text(
            'Описание задачи, данной этим человеком, примите к сведению эту задачу, а также выполните её.',
            style: Fonts.bodyMedium,
          ),
          // Divider(height: 24.0),
          SizedBox(height: 12.0),
          TextField(
            decoration: InputDecoration(
              hintText: 'Комментарий',
            ),
            style: Fonts.labelSmall,
          ),
          SizedBox(height: 12.0),
          Row(
            children: [
              // GhostButton(
              //   onTap: () {},
              //   child: Text(
              //     'Отмена',
              //     style: Fonts.labelMedium,
              //   ),
              // ),
              Flexible(
                child: ElevatedButton(
                  // text: 'Пометить выполненной',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(CupertinoIcons.minus_circle),
                      SizedBox(width: 8.0),
                      Text('Отклонить', style: Fonts.labelSmall),
                    ],
                  ),
                  onPressed: () {},
                ),
              ),
              SizedBox(width: 12.0),
              Flexible(
                child: ElevatedButton(
                  // text: 'Пометить выполненной',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(CupertinoIcons.check_mark_circled),
                      SizedBox(width: 8.0),
                      Text('Пометить выполненной', style: Fonts.labelSmall),
                    ],
                  ),
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
