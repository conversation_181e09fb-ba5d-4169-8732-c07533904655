import 'package:flutter/material.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/_initial/notifications/data/models/notification.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';

class NotificationCard extends StatelessWidget {
  const NotificationCard({super.key, required this.data});

  final NotificationModel data;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Expanded(
            child: Text(
              data.title ?? 'Title',
              style: Fonts.titleMedium,
            ),
          ),
          Text(
            getDateString(data.createdAt ?? DateTime.now()),
            style: Fonts.bodySmall.merge(TextStyle(
              color: isDarkTheme
                  ? AppColors.darkDescription
                  : AppColors.lightDescription,
            )),
          ),
        ]),
        Text(
          data.message ?? 'Message',
          style: Fonts.bodySmall,
        ),
      ]),
    );
  }
}
