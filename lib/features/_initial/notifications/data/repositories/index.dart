import 'package:dio/dio.dart';
import 'package:sphere/features/_initial/notifications/data/models/index.dart';
import 'package:sphere/features/_initial/notifications/data/models/notification.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class NotificationsRepository {
  static Future<Response<NotificationsModel>?> search(
    SearchModel data,
  ) async {
    final request = await API.request<NotificationsModel>(
      url: '/notifications/search',
      body: data.toJson(),
      method: 'POST',
      fromJson: NotificationsModel.fromJson,
    );

    return request;
  }

  static Future<Response<void>?> sendTest(
    SendTestNotificationModel data,
  ) async {
    final request = await API.request<void>(
      url: '/notifications/sendTest',
      body: data.toJson(),
      method: 'POST',
      // fromJson: NotificationsModel.fromJson,
    );

    return request;
  }
}
