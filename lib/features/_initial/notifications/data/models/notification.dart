// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification.freezed.dart';
part 'notification.g.dart';

@freezed
class NotificationsModel with _$NotificationsModel {
  @JsonSerializable()
  const factory NotificationsModel({
    List<NotificationModel>? items,
    int? totalItems,
  }) = _NotificationsModel;

  factory NotificationsModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationsModelFromJson(json);
}

@freezed
class NotificationModel with _$NotificationModel {
  @JsonSerializable(includeIfNull: false)
  const factory NotificationModel({
    @JsonKey(name: '_id') String? id,
    String? title,
    String? message,
    String? userId,
    String? projectId,
    String? projectName,
    bool? isRead,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);
}
