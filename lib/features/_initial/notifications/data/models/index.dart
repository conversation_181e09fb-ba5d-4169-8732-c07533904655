import 'package:freezed_annotation/freezed_annotation.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
class SendTestNotificationModel with _$SendTestNotificationModel {
  @JsonSerializable(includeIfNull: false)
  const factory SendTestNotificationModel({
    String? title,
    String? message,
    List<String>? userIds,
    String? projectId,
  }) = _SendTestNotificationModel;

  factory SendTestNotificationModel.fromJson(Map<String, dynamic> json) =>
      _$SendTestNotificationModelFromJson(json);
}
