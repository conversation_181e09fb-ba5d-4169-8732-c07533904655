// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SendTestNotificationModelImpl _$$SendTestNotificationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SendTestNotificationModelImpl(
      title: json['title'] as String?,
      message: json['message'] as String?,
      userIds:
          (json['userIds'] as List<dynamic>?)?.map((e) => e as String).toList(),
      projectId: json['projectId'] as String?,
    );

Map<String, dynamic> _$$SendTestNotificationModelImplToJson(
        _$SendTestNotificationModelImpl instance) =>
    <String, dynamic>{
      if (instance.title case final value?) 'title': value,
      if (instance.message case final value?) 'message': value,
      if (instance.userIds case final value?) 'userIds': value,
      if (instance.projectId case final value?) 'projectId': value,
    };
