// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SendTestNotificationModel _$SendTestNotificationModelFromJson(
    Map<String, dynamic> json) {
  return _SendTestNotificationModel.fromJson(json);
}

/// @nodoc
mixin _$SendTestNotificationModel {
  String? get title => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  List<String>? get userIds => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;

  /// Serializes this SendTestNotificationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SendTestNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SendTestNotificationModelCopyWith<SendTestNotificationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendTestNotificationModelCopyWith<$Res> {
  factory $SendTestNotificationModelCopyWith(SendTestNotificationModel value,
          $Res Function(SendTestNotificationModel) then) =
      _$SendTestNotificationModelCopyWithImpl<$Res, SendTestNotificationModel>;
  @useResult
  $Res call(
      {String? title,
      String? message,
      List<String>? userIds,
      String? projectId});
}

/// @nodoc
class _$SendTestNotificationModelCopyWithImpl<$Res,
        $Val extends SendTestNotificationModel>
    implements $SendTestNotificationModelCopyWith<$Res> {
  _$SendTestNotificationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SendTestNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? message = freezed,
    Object? userIds = freezed,
    Object? projectId = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      userIds: freezed == userIds
          ? _value.userIds
          : userIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendTestNotificationModelImplCopyWith<$Res>
    implements $SendTestNotificationModelCopyWith<$Res> {
  factory _$$SendTestNotificationModelImplCopyWith(
          _$SendTestNotificationModelImpl value,
          $Res Function(_$SendTestNotificationModelImpl) then) =
      __$$SendTestNotificationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? message,
      List<String>? userIds,
      String? projectId});
}

/// @nodoc
class __$$SendTestNotificationModelImplCopyWithImpl<$Res>
    extends _$SendTestNotificationModelCopyWithImpl<$Res,
        _$SendTestNotificationModelImpl>
    implements _$$SendTestNotificationModelImplCopyWith<$Res> {
  __$$SendTestNotificationModelImplCopyWithImpl(
      _$SendTestNotificationModelImpl _value,
      $Res Function(_$SendTestNotificationModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendTestNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? message = freezed,
    Object? userIds = freezed,
    Object? projectId = freezed,
  }) {
    return _then(_$SendTestNotificationModelImpl(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      userIds: freezed == userIds
          ? _value._userIds
          : userIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SendTestNotificationModelImpl implements _SendTestNotificationModel {
  const _$SendTestNotificationModelImpl(
      {this.title, this.message, final List<String>? userIds, this.projectId})
      : _userIds = userIds;

  factory _$SendTestNotificationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SendTestNotificationModelImplFromJson(json);

  @override
  final String? title;
  @override
  final String? message;
  final List<String>? _userIds;
  @override
  List<String>? get userIds {
    final value = _userIds;
    if (value == null) return null;
    if (_userIds is EqualUnmodifiableListView) return _userIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? projectId;

  @override
  String toString() {
    return 'SendTestNotificationModel(title: $title, message: $message, userIds: $userIds, projectId: $projectId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendTestNotificationModelImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._userIds, _userIds) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, message,
      const DeepCollectionEquality().hash(_userIds), projectId);

  /// Create a copy of SendTestNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendTestNotificationModelImplCopyWith<_$SendTestNotificationModelImpl>
      get copyWith => __$$SendTestNotificationModelImplCopyWithImpl<
          _$SendTestNotificationModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendTestNotificationModelImplToJson(
      this,
    );
  }
}

abstract class _SendTestNotificationModel implements SendTestNotificationModel {
  const factory _SendTestNotificationModel(
      {final String? title,
      final String? message,
      final List<String>? userIds,
      final String? projectId}) = _$SendTestNotificationModelImpl;

  factory _SendTestNotificationModel.fromJson(Map<String, dynamic> json) =
      _$SendTestNotificationModelImpl.fromJson;

  @override
  String? get title;
  @override
  String? get message;
  @override
  List<String>? get userIds;
  @override
  String? get projectId;

  /// Create a copy of SendTestNotificationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendTestNotificationModelImplCopyWith<_$SendTestNotificationModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
