import 'dart:io';

import 'package:dio/dio.dart';
import 'package:sphere/features/_initial/otk/data/models/defect_act.dart';
import 'package:sphere/features/_initial/otk/data/models/delivery.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class OtkRepository {
  static Future<Response<OtkDeliveriesResponse>?> deliveriesSearch(
    SearchModel data,
  ) async {
    final request = await API.request<OtkDeliveriesResponse>(
      url: '/quality-control/deliveries/search',
      body: data.toJson(),
      method: 'POST',
      fromJson: OtkDeliveriesResponse.fromJson,
    );

    return request;
  }

  static Future<Response<OtkDeliveryModel>?> deliveriesProcess(
    String deliveryId,
    List<OtkProcessItem> items,
    List<File> files,
  ) async {
    final request = await API.request<OtkDeliveryModel>(
      url: '/quality-control/deliveries/process',
      body: {
        'deliveryId': deliveryId,
        'qcItems': items.map((e) => e.toJson()).toList(),
        'documents': files.map((e) => e.path).toList(),
      },
      method: 'POST',
      fromJson: OtkDeliveryModel.fromJson,
    );

    return request;
  }

  static Future<Response<OtkDefectActsResponse>?> defectActsSearch(
    SearchModel data,
  ) async {
    final request = await API.request<OtkDefectActsResponse>(
      url: '/quality-control/defect-acts/search',
      body: data.toJson(),
      method: 'POST',
      fromJson: OtkDefectActsResponse.fromJson,
    );

    return request;
  }

  static Future<Response<OtkDefectActModel>?> defectActsDecision(
    String defectActId,
    OtkDecisionType decision,
    String comment,
  ) async {
    final request = await API.request<OtkDefectActModel>(
      url: '/quality-control/defect-acts/decision',
      body: {
        'defectActId': defectActId,
        'decision': decision.getJsonName(),
        'comment': comment,
      },
      method: 'POST',
      fromJson: OtkDefectActModel.fromJson,
    );

    return request;
  }

  // Производственные методы для работы с актами о браке
  static Future<Response<dynamic>?> startRepair(String defectActId) async {
    final request = await API.request<dynamic>(
      url: '/production/defect-acts/start-repair',
      body: {
        'defectActId': defectActId,
      },
      method: 'POST',
    );

    return request;
  }

  static Future<Response<dynamic>?> completeRepair(String defectActId) async {
    final request = await API.request<dynamic>(
      url: '/production/defect-acts/complete-repair',
      body: {
        'defectActId': defectActId,
      },
      method: 'POST',
    );

    return request;
  }
}
