import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/shared/styles/colors.dart';

part 'defect_act.freezed.dart';
part 'defect_act.g.dart';

// POST
// /production/defect-acts/start-repair
// Взять акт о браке в работу

// Производственный работник берет акт в работу для выполнения ремонта

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "defectActId": "string"
// }
// Responses
// Code	Description	Links
// 200
// Ремонт начат

// POST
// /production/defect-acts/complete-repair
// Завершить ремонт

// Отметить завершение ремонта и передать акт в ОТК для повторной проверки

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "defectActId": "string"
// }
// Responses
// Code	Description	Links
// 200
// Ремонт завершен

// Специальная модель склада для OTK, где все поля nullable
@freezed
class OtkWarehouse with _$OtkWarehouse {
  @JsonSerializable(includeIfNull: false)
  const factory OtkWarehouse({
    @JsonKey(name: '_id') String? id,
    String? name,
    @JsonKey(name: 'type') WarehouseType? type,
    String? address,
    String? description,
    String? projectId,
    String? physicalWarehouseId,
    String? contractorId,
    List<WarehouseKeeper>? keepers,
    bool? isActive,
    @JsonKey(name: 'createdAt') String? createdAt,
    @JsonKey(name: 'updatedAt') String? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) = _OtkWarehouse;

  factory OtkWarehouse.fromJson(Map<String, dynamic> json) =>
      _$OtkWarehouseFromJson(json);
}

@freezed
class OtkDefectActsResponse with _$OtkDefectActsResponse {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDefectActsResponse({
    @Default([]) List<OtkDefectActModel> items,
    @Default(0) int totalItems,
  }) = _OtkDefectActsResponse;

  factory OtkDefectActsResponse.fromJson(Map<String, dynamic> json) =>
      _$OtkDefectActsResponseFromJson(json);
}

@freezed
class OtkDefectActModel with _$OtkDefectActModel {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDefectActModel({
    @JsonKey(name: '_id') String? id,
    String? temporaryStorageId,
    String? deliveryId,
    List<OtkDefectItemModel>? defectItems,
    OtkDecisionModel? decision,
    String? status,
    String? replacementDeliveryId,
    String? repairTaskId,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? updatedBy,
    OtkWarehouse? tempStorage,
    DeliveryModel? delivery,
    ProductModel? product,
    UserModel? assignedUser,
    UserModel? repairWorker,
    bool? canStartRepair,
    bool? isInProgress,
    bool? isCompleted,
    bool? canMakeDecision,
    bool? needsRecheck,
    double? repairDuration,
  }) = _OtkDefectActModel;

  factory OtkDefectActModel.fromJson(Map<String, dynamic> json) =>
      _$OtkDefectActModelFromJson(json);
}

@freezed
class OtkDefectItemModel with _$OtkDefectItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDefectItemModel({
    String? productId,
    double? quantity,
    String? description,
    List<String>? photos,
    OtkDecisionModel? decision,
  }) = _OtkDefectItemModel;

  factory OtkDefectItemModel.fromJson(Map<String, dynamic> json) =>
      _$OtkDefectItemModelFromJson(json);
}

@freezed
class OtkDecisionModel with _$OtkDecisionModel {
  @JsonSerializable(includeIfNull: false)
  const factory OtkDecisionModel({
    OtkDecisionType? type,
    String? decidedBy,
    DateTime? decidedAt,
    String? comment,
  }) = _OtkDecisionModel;

  factory OtkDecisionModel.fromJson(Map<String, dynamic> json) =>
      _$OtkDecisionModelFromJson(json);
}

enum OtkDecisionType {
  @JsonValue('repair_internal')
  repairableInternal,
  @JsonValue('replace_supplier')
  repairableSupplier,
  @JsonValue('write_off')
  nonRepairable;

  String getName() {
    switch (this) {
      case OtkDecisionType.repairableInternal:
        return 'Ремонтируемый';
      case OtkDecisionType.repairableSupplier:
        return 'Ремонтируемый поставщиком';
      case OtkDecisionType.nonRepairable:
        return 'Неремонтируемый';
    }
  }

  Color getColor() {
    switch (this) {
      case OtkDecisionType.repairableInternal:
        return AppColors.lightSuccess;
      case OtkDecisionType.repairableSupplier:
        return AppColors.lightSuccess;
      case OtkDecisionType.nonRepairable:
        return AppColors.lightError;
    }
  }

  String getJsonName() {
    switch (this) {
      case OtkDecisionType.repairableInternal:
        return 'repair_internal';
      case OtkDecisionType.repairableSupplier:
        return 'replace_supplier';
      case OtkDecisionType.nonRepairable:
        return 'write_off';
    }
  }
}
