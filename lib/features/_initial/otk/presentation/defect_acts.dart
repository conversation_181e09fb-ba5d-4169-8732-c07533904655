import 'package:auto_route/auto_route.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/otk/data/models/defect_act.dart';
import 'package:sphere/features/_initial/otk/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class OtkDefectActsScreen extends StatefulWidget {
  const OtkDefectActsScreen({super.key});

  @override
  State<OtkDefectActsScreen> createState() => _OtkDefectActsScreenState();
}

class _OtkDefectActsScreenState extends State<OtkDefectActsScreen>
    with AutoRouteAwareStateMixin<OtkDefectActsScreen> {
  bool _isLoading = false;
  List<OtkDefectActModel> _defectActs = [];
  String? _error;

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    await _getData();
    _setAppBarConfig();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    await _getData();
    _setAppBarConfig();
  }

  Future<void> _getData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await OtkRepository.defectActsSearch(SearchModel());
      if (response?.data != null) {
        setState(() {
          _defectActs = response!.data!.items;
          _isLoading = false;
        });
        print('Loaded ${_defectActs.length} defect acts');
      } else {
        setState(() {
          _defectActs = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
      print('Error loading OTK defect acts: $e');
    }
  }

  void _setAppBarConfig() {
    final newConfig = CustomAppBarConfig(
      title: 'Акты о браке',
      rightPart: Row(
        children: [
          TextButton(
            onPressed: () {
              context.router.push(const OtkDeliveriesRoute());
            },
            child: const Text('Поставки'),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _getData,
            icon: SVG(Assets.icons.repeat, width: 20.0),
          ),
        ],
      ),
      isLoading: _isLoading,
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  void _showDecisionDialog(OtkDefectActModel defectAct) {
    showBaseDialog(
      context,
      maxWidth: 720,
      builder: (context) => DefectActDecisionDialog(
        defectAct: defectAct,
        onDecision: (decision, comment) async {
          try {
            await OtkRepository.defectActsDecision(
              defectAct.id!,
              decision,
              comment,
            );
            Navigator.of(context).pop();
            _getData(); // Обновляем данные
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Ошибка: $e')),
            );
          }
        },
      ),
    );
  }

  String _getStatusText(OtkDefectActModel defectAct) {
    if (defectAct.isCompleted == true) {
      return 'Завершен';
    } else if (defectAct.isInProgress == true) {
      return 'В процессе';
    } else if (defectAct.needsRecheck == true) {
      return 'Требует перепроверки';
    } else if (defectAct.canMakeDecision == true) {
      return 'Ожидает решения';
    }
    return defectAct.status ?? 'Неизвестно';
  }

  Color _getStatusColor(OtkDefectActModel defectAct) {
    if (defectAct.isCompleted == true) {
      return AppColors.lightSuccess;
    } else if (defectAct.isInProgress == true) {
      return AppColors.lightSecondary;
    } else if (defectAct.needsRecheck == true) {
      return AppColors.lightError;
    } else if (defectAct.canMakeDecision == true) {
      return AppColors.lightWarning;
    }
    return AppColors.lightDescription;
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: Column(
        children: [
          // Заголовок
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.report_problem,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Акты о браке',
                  style: Fonts.headlineSmall,
                ),
              ],
            ),
          ),

          // Ошибка
          if (_error != null)
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red),
              ),
              child: Row(
                children: [
                  Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(child: Text(_error!)),
                  IconButton(
                    onPressed: () => setState(() => _error = null),
                    icon: Icon(Icons.close, color: Colors.red),
                  ),
                ],
              ),
            ),

          // Таблица
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _defectActs.isEmpty
                    ? const Center(
                        child: Text('Нет актов о браке'),
                      )
                    : DataTable2(
                        minWidth: 1000,
                        dividerThickness: 1.0,
                        showBottomBorder: true,
                        columnSpacing: 8,
                        columns: [
                          DataColumn2(
                            label:
                                Text('Дата создания', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Статус', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Поставщик', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Продукт', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Решение', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                          DataColumn2(
                            label: Text('Действия', style: Fonts.labelMedium),
                            size: ColumnSize.L,
                          ),
                        ],
                        rows: _defectActs
                            .map((defectAct) => DataRow2(
                                  cells: [
                                    DataCell(Text(
                                      defectAct.createdAt != null
                                          ? DateFormat('dd.MM.yyyy HH:mm')
                                              .format(defectAct.createdAt!)
                                          : '',
                                      style: Fonts.bodySmall,
                                    )),
                                    DataCell(Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: _getStatusColor(defectAct),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        _getStatusText(defectAct),
                                        style: Fonts.bodySmall
                                            .copyWith(color: Colors.white),
                                      ),
                                    )),
                                    DataCell(Text(
                                      defectAct.delivery?.supplier?.name ?? '',
                                      style: Fonts.bodySmall,
                                    )),
                                    DataCell(Text(
                                      defectAct.product?.name ?? '',
                                      style: Fonts.bodySmall,
                                    )),
                                    DataCell(Text(
                                      defectAct.decision?.type?.getName() ??
                                          'Нет решения',
                                      style: Fonts.bodySmall,
                                    )),
                                    DataCell(Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        if (defectAct.canMakeDecision == true)
                                          IconButton(
                                            tooltip: 'Принять решение',
                                            onPressed: () =>
                                                _showDecisionDialog(defectAct),
                                            icon: const Icon(Icons.gavel,
                                                size: 18),
                                          ),
                                        // IconButton(
                                        //   tooltip: 'Просмотр деталей',
                                        //   onPressed: () {
                                        //     // TODO: Показать детали акта о браке
                                        //   },
                                        //   icon: SVG(Assets.icons.visibility,
                                        //       width: 16),
                                        // ),
                                      ],
                                    )),
                                  ],
                                ))
                            .toList(),
                      ),
          ),
        ],
      ),
    );
  }
}

class DefectActDecisionDialog extends StatefulWidget {
  final OtkDefectActModel defectAct;
  final void Function(OtkDecisionType, String) onDecision;

  const DefectActDecisionDialog({
    super.key,
    required this.defectAct,
    required this.onDecision,
  });

  @override
  State<DefectActDecisionDialog> createState() =>
      _DefectActDecisionDialogState();
}

class _DefectActDecisionDialogState extends State<DefectActDecisionDialog> {
  final TextEditingController _commentController = TextEditingController();
  OtkDecisionType? _selectedDecision;

  String _getSupplierName() {
    return widget.defectAct.delivery?.supplier?.name ?? 'Неизвестный поставщик';
  }

  String _getProductName() {
    return widget.defectAct.product?.name ?? 'Неизвестный продукт';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Заголовок
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.gavel,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Принятие решения по акту о браке',
                  style: Fonts.headlineSmall,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
        ),

        // Содержимое
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Поставщик: ${_getSupplierName()}',
                  style: Fonts.bodyMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'Продукт: ${_getProductName()}',
                  style: Fonts.bodyMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'Дефектные элементы:',
                  style: Fonts.labelMedium,
                ),
                const SizedBox(height: 8),
                if (widget.defectAct.defectItems != null)
                  ...widget.defectAct.defectItems!.map((item) => CustomCard(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Количество: ${item.quantity?.toStringAsFixed(2) ?? '0'}',
                                style: Fonts.bodySmall,
                              ),
                              if (item.description != null) ...[
                                const SizedBox(height: 4),
                                Text(
                                  'Описание: ${item.description}',
                                  style: Fonts.bodySmall,
                                ),
                              ],
                            ],
                          ),
                        ),
                      )),
                const SizedBox(height: 16),
                Text(
                  'Тип решения:',
                  style: Fonts.labelMedium,
                ),
                const SizedBox(height: 8),
                ...OtkDecisionType.values
                    .map((decision) => RadioListTile<OtkDecisionType>(
                          title: Text(decision.getName()),
                          value: decision,
                          groupValue: _selectedDecision,
                          onChanged: (value) {
                            setState(() {
                              _selectedDecision = value;
                            });
                          },
                        )),
                const SizedBox(height: 16),
                TextField(
                  controller: _commentController,
                  decoration: const InputDecoration(
                    labelText: 'Комментарий',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),

        // Кнопки
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: Colors.grey.shade300),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Flexible(
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Отмена'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _selectedDecision != null &&
                          _commentController.text.isNotEmpty
                      ? () {
                          widget.onDecision(
                              _selectedDecision!, _commentController.text);
                        }
                      : null,
                  child: const Text('Принять решение'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
