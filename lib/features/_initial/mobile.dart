import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/app_bar.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/shared/widgets/overlay/bottom_navigation/index.dart';
import 'package:sphere/shared/widgets/overlay/bottom_navigation/navigation_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class MobileLayout extends StatelessWidget {
  const MobileLayout({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BlocInitial, BlocInitialState>(
        builder: (context, state) {
      return AutoTabsScaffold(
        lazyLoad: true,
        routes: const [
          NomenclaturesRoute(),
          StorageRoute(),
          WarehousesRoute(),
          // DeliveriesRoute(),
          BranchRoute(),
          NotificationRoute(),
          SettingsRoute(),
        ],
        appBarBuilder: (context, tabsRouter) {
          return InitialAppBar(config: state.config);
        },
        bottomNavigationBuilder: (context, tabsRouter) {
          return CustomBottomNavigationBar(
            currentIndex: tabsRouter.activeIndex,
            onTap: tabsRouter.setActiveIndex,
            children: [
              CustomNavigationButton(icon: SVG(Assets.icons.developerGuide)),
              CustomNavigationButton(icon: SVG(Assets.icons.warehouse)),
              CustomNavigationButton(icon: SVG(Assets.icons.warehouse)),
              // CustomNavigationButton(icon: SVG(Assets.icons.pallet)),
              CustomNavigationButton(icon: SVG(Assets.icons.home)),
              CustomNavigationButton(
                icon: SVG(Assets.icons.notifications),
                // withNotification: true,
              ),
              CustomNavigationButton(icon: SVG(Assets.icons.settings)),
            ],
          );
        },
      );
    });
  }
}
