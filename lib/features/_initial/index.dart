import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/desktop.dart';
import 'package:sphere/features/_initial/mobile.dart';
import 'package:tray_manager/tray_manager.dart';
import 'package:window_manager/window_manager.dart';

@RoutePage()
class InitialScreen extends StatefulWidget {
  const InitialScreen({super.key});

  @override
  State<InitialScreen> createState() => _InitialScreenState();
}

class _InitialScreenState extends State<InitialScreen>
    with TrayListener, WindowListener {
  @override
  void initState() {
    super.initState();
    _initTray();
    windowManager.addListener(this);

    // create background service
    // Workmanager().initialize(
    //   callbackDispatcher,
    //   isInDebugMode: true,
    // );
  }

  @override
  void onWindowClose() {
    appWindow.hide();
  }

  Future<void> _initTray() async {
    // Инициализация TrayManager
    await trayManager.setIcon(
      'assets/images/start.ico',
    ); // Укажите путь к иконке

    trayManager.setToolTip('Сфера'); // Установка всплывающей подсказки

    // Создание меню для иконки в трее
    final menuItems = Menu(items: [
      MenuItem(
        key: 'show_app',
        label: 'Показать приложение',
      ),
      MenuItem(
        key: 'exit_app',
        label: 'Выход',
      ),
    ]);

    await trayManager.setContextMenu(menuItems);

    // Подписка на события
    trayManager.addListener(this);
  }

  @override
  void dispose() {
    trayManager.removeListener(this);
    windowManager.removeListener(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return isMobile ? const MobileLayout() : const DesktopLayout();
  }

  // Обработка событий меню трея
  @override
  void onTrayIconMouseDown() {
    // Действие при клике на иконку в трее
    appWindow.maximize();
  }

  @override
  void onTrayIconRightMouseDown() {
    // Действие при правом клике на иконку в трее
    trayManager.popUpContextMenu();
  }

  @override
  void onTrayMenuItemClick(MenuItem menuItem) {
    // Обработка кликов по пунктам меню
    switch (menuItem.key) {
      case 'show_app':
        // Показать приложение
        appWindow.maximizeOrRestore();
        break;
      case 'exit_app':
        // Закрыть приложение
        exit(0);
    }
  }
}
