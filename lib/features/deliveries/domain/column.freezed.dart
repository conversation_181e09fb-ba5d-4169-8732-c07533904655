// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'column.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TableColumnModel {
  String? get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  double? get width => throw _privateConstructorUsedError;

  /// Create a copy of TableColumnModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TableColumnModelCopyWith<TableColumnModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TableColumnModelCopyWith<$Res> {
  factory $TableColumnModelCopyWith(
          TableColumnModel value, $Res Function(TableColumnModel) then) =
      _$TableColumnModelCopyWithImpl<$Res, TableColumnModel>;
  @useResult
  $Res call({String? name, String? description, double? width});
}

/// @nodoc
class _$TableColumnModelCopyWithImpl<$Res, $Val extends TableColumnModel>
    implements $TableColumnModelCopyWith<$Res> {
  _$TableColumnModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TableColumnModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? description = freezed,
    Object? width = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TableColumnModelImplCopyWith<$Res>
    implements $TableColumnModelCopyWith<$Res> {
  factory _$$TableColumnModelImplCopyWith(_$TableColumnModelImpl value,
          $Res Function(_$TableColumnModelImpl) then) =
      __$$TableColumnModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? description, double? width});
}

/// @nodoc
class __$$TableColumnModelImplCopyWithImpl<$Res>
    extends _$TableColumnModelCopyWithImpl<$Res, _$TableColumnModelImpl>
    implements _$$TableColumnModelImplCopyWith<$Res> {
  __$$TableColumnModelImplCopyWithImpl(_$TableColumnModelImpl _value,
      $Res Function(_$TableColumnModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TableColumnModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? description = freezed,
    Object? width = freezed,
  }) {
    return _then(_$TableColumnModelImpl(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$TableColumnModelImpl implements _TableColumnModel {
  _$TableColumnModelImpl({this.name, this.description, this.width});

  @override
  final String? name;
  @override
  final String? description;
  @override
  final double? width;

  @override
  String toString() {
    return 'TableColumnModel(name: $name, description: $description, width: $width)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TableColumnModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.width, width) || other.width == width));
  }

  @override
  int get hashCode => Object.hash(runtimeType, name, description, width);

  /// Create a copy of TableColumnModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TableColumnModelImplCopyWith<_$TableColumnModelImpl> get copyWith =>
      __$$TableColumnModelImplCopyWithImpl<_$TableColumnModelImpl>(
          this, _$identity);
}

abstract class _TableColumnModel implements TableColumnModel {
  factory _TableColumnModel(
      {final String? name,
      final String? description,
      final double? width}) = _$TableColumnModelImpl;

  @override
  String? get name;
  @override
  String? get description;
  @override
  double? get width;

  /// Create a copy of TableColumnModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TableColumnModelImplCopyWith<_$TableColumnModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
