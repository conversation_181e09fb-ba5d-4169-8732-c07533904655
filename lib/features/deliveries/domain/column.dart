import 'package:freezed_annotation/freezed_annotation.dart';

part 'column.freezed.dart';

@freezed
abstract class TableColumnModel with _$TableColumnModel {
  factory TableColumnModel({
    String? name,
    String? description,
    double? width,
  }) = _TableColumnModel;
}

class DeliveryTableData {
  static final List<TableColumnModel> columns = [
    // 0
    TableColumnModel(
      name: 'Дата создания',
      description: 'Дата создания поставки',
      width: 200,
    ),
    // 1
    TableColumnModel(
      name: 'Статус',
      description: 'Статус поставки',
      width: 150,
    ),
    // 2
    TableColumnModel(
      name: 'Поставщик',
      description: 'Наименование поставщика',
      width: 300,
    ),
    // 3
    TableColumnModel(
      name: 'Ожидаемая дата',
      description: 'Ожидаемая дата поставки',
      width: 200,
    ),
    // 4
    TableColumnModel(
      name: 'Фактическая дата',
      description: 'Фактическая дата поставки',
      width: 200,
    ),
    // 5
    TableColumnModel(
      name: 'Материалы',
      description: 'Количество материалов в поставке',
      width: 150,
    ),
    // 6
    TableColumnModel(
      name: 'Общее количество',
      description: 'Общее количество материалов',
      width: 180,
    ),
    // 7
    TableColumnModel(
      name: 'Общая стоимость',
      description: 'Общая стоимость поставки',
      width: 180,
    ),
    // 8
    TableColumnModel(
      name: 'Комментарий',
      description: 'Комментарий к поставке',
      width: 300,
    ),
    // 9
    TableColumnModel(
      name: 'Действия',
      description: 'Доступные действия',
      width: 150,
    ),
  ];
}
