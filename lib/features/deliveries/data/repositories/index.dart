import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:sphere/features/deliveries/data/models/index.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class DeliveriesRepository {
  static Future<Response<DeliveriesSearchOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<DeliveriesSearchOutputModel>(
      url: '/deliveries/search',
      body: body,
      method: 'POST',
      fromJson: DeliveriesSearchOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>?> acceptDelivery({
    String? deliveryId,
    String? comment,
    required List<File> files,
  }) async {
    // Преобразуем файлы в MultipartFile, дожидаясь завершения всех операций
    final multipartFiles = await Future.wait(
      files.map((file) async => await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
            contentType: DioMediaType('application', 'pdf'),
          )),
    );

    final body = FormData.fromMap({
      'deliveryId': deliveryId,
      'comment': comment,
      'documents': multipartFiles, // используем уже готовые MultipartFile
    });

    final args = [
      // if (projectId != null) 'projectId=$projectId',
      // if (productId != null) 'productId=$productId',
    ];

    final request = await API.request<dynamic>(
      url: '/deliveries/accept?${args.join('&')}',
      body: body,
      method: 'POST',
      // fromJson: FileModel.fromJson,
      options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  static Future<Response<dynamic>?> qualityControl({
    String? deliveryId,
    required List<QCItemModel> qcItems,
    required List<File> files,
  }) async {
    final qcItemsJson =
        jsonEncode(qcItems.map((qcItem) => qcItem.toJson()).toList());
    print(qcItemsJson);

    final body = FormData.fromMap({
      'deliveryId': deliveryId,
      'qcItems': qcItemsJson,
      'documents': await Future.wait(files.map((file) async {
        return await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
          contentType: DioMediaType('application', 'pdf'),
        );
      })),
    });

    final args = [
      // if (projectId != null) 'projectId=$projectId',
      // if (productId != null) 'productId=$productId',
    ];

    final request = await API.request<dynamic>(
      url: '/deliveries/quality-control?${args.join('&')}',
      body: body,
      method: 'POST',
      options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  static Future<Response<dynamic>?> receiveDelivery({
    required String deliveryId,
    required List<ReceiveDeliveryItemModel> items,
    List<File>? files,
  }) async {
    final body = FormData.fromMap({
      'deliveryId': deliveryId,
      'items': items.map((item) => item.toJson()).toList(),
      'documents': await Future.wait(
        files?.map((file) async {
              return await MultipartFile.fromFile(
                file.path,
                filename: file.path.split('/').last,
                contentType: DioMediaType('application', 'pdf'),
              );
            }) ??
            [],
      ),
    });

    print(body.fields);

    final request = await API.request<dynamic>(
      url: '/deliveries/receive',
      body: body,
      method: 'POST',
      options: Options(contentType: 'multipart/form-data'),
    );
    return request;
  }
}
