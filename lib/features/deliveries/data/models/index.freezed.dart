// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DeliveriesSearchOutputModel _$DeliveriesSearchOutputModelFromJson(
    Map<String, dynamic> json) {
  return _DeliveriesSearchOutputModel.fromJson(json);
}

/// @nodoc
mixin _$DeliveriesSearchOutputModel {
  List<DeliveryModel>? get items => throw _privateConstructorUsedError;
  int? get totalItems => throw _privateConstructorUsedError;

  /// Serializes this DeliveriesSearchOutputModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeliveriesSearchOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliveriesSearchOutputModelCopyWith<DeliveriesSearchOutputModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliveriesSearchOutputModelCopyWith<$Res> {
  factory $DeliveriesSearchOutputModelCopyWith(
          DeliveriesSearchOutputModel value,
          $Res Function(DeliveriesSearchOutputModel) then) =
      _$DeliveriesSearchOutputModelCopyWithImpl<$Res,
          DeliveriesSearchOutputModel>;
  @useResult
  $Res call({List<DeliveryModel>? items, int? totalItems});
}

/// @nodoc
class _$DeliveriesSearchOutputModelCopyWithImpl<$Res,
        $Val extends DeliveriesSearchOutputModel>
    implements $DeliveriesSearchOutputModelCopyWith<$Res> {
  _$DeliveriesSearchOutputModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliveriesSearchOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<DeliveryModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliveriesSearchOutputModelImplCopyWith<$Res>
    implements $DeliveriesSearchOutputModelCopyWith<$Res> {
  factory _$$DeliveriesSearchOutputModelImplCopyWith(
          _$DeliveriesSearchOutputModelImpl value,
          $Res Function(_$DeliveriesSearchOutputModelImpl) then) =
      __$$DeliveriesSearchOutputModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<DeliveryModel>? items, int? totalItems});
}

/// @nodoc
class __$$DeliveriesSearchOutputModelImplCopyWithImpl<$Res>
    extends _$DeliveriesSearchOutputModelCopyWithImpl<$Res,
        _$DeliveriesSearchOutputModelImpl>
    implements _$$DeliveriesSearchOutputModelImplCopyWith<$Res> {
  __$$DeliveriesSearchOutputModelImplCopyWithImpl(
      _$DeliveriesSearchOutputModelImpl _value,
      $Res Function(_$DeliveriesSearchOutputModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliveriesSearchOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_$DeliveriesSearchOutputModelImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<DeliveryModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$DeliveriesSearchOutputModelImpl
    implements _DeliveriesSearchOutputModel {
  const _$DeliveriesSearchOutputModelImpl(
      {final List<DeliveryModel>? items, this.totalItems})
      : _items = items;

  factory _$DeliveriesSearchOutputModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$DeliveriesSearchOutputModelImplFromJson(json);

  final List<DeliveryModel>? _items;
  @override
  List<DeliveryModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalItems;

  @override
  String toString() {
    return 'DeliveriesSearchOutputModel(items: $items, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliveriesSearchOutputModelImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), totalItems);

  /// Create a copy of DeliveriesSearchOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliveriesSearchOutputModelImplCopyWith<_$DeliveriesSearchOutputModelImpl>
      get copyWith => __$$DeliveriesSearchOutputModelImplCopyWithImpl<
          _$DeliveriesSearchOutputModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeliveriesSearchOutputModelImplToJson(
      this,
    );
  }
}

abstract class _DeliveriesSearchOutputModel
    implements DeliveriesSearchOutputModel {
  const factory _DeliveriesSearchOutputModel(
      {final List<DeliveryModel>? items,
      final int? totalItems}) = _$DeliveriesSearchOutputModelImpl;

  factory _DeliveriesSearchOutputModel.fromJson(Map<String, dynamic> json) =
      _$DeliveriesSearchOutputModelImpl.fromJson;

  @override
  List<DeliveryModel>? get items;
  @override
  int? get totalItems;

  /// Create a copy of DeliveriesSearchOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliveriesSearchOutputModelImplCopyWith<_$DeliveriesSearchOutputModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

QCItemModel _$QCItemModelFromJson(Map<String, dynamic> json) {
  return _QCItemModel.fromJson(json);
}

/// @nodoc
mixin _$QCItemModel {
  String? get provisionItemId => throw _privateConstructorUsedError;
  double? get quantityApproved => throw _privateConstructorUsedError;
  String? get qcComment => throw _privateConstructorUsedError;

  /// Serializes this QCItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QCItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QCItemModelCopyWith<QCItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QCItemModelCopyWith<$Res> {
  factory $QCItemModelCopyWith(
          QCItemModel value, $Res Function(QCItemModel) then) =
      _$QCItemModelCopyWithImpl<$Res, QCItemModel>;
  @useResult
  $Res call(
      {String? provisionItemId, double? quantityApproved, String? qcComment});
}

/// @nodoc
class _$QCItemModelCopyWithImpl<$Res, $Val extends QCItemModel>
    implements $QCItemModelCopyWith<$Res> {
  _$QCItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QCItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionItemId = freezed,
    Object? quantityApproved = freezed,
    Object? qcComment = freezed,
  }) {
    return _then(_value.copyWith(
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantityApproved: freezed == quantityApproved
          ? _value.quantityApproved
          : quantityApproved // ignore: cast_nullable_to_non_nullable
              as double?,
      qcComment: freezed == qcComment
          ? _value.qcComment
          : qcComment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QCItemModelImplCopyWith<$Res>
    implements $QCItemModelCopyWith<$Res> {
  factory _$$QCItemModelImplCopyWith(
          _$QCItemModelImpl value, $Res Function(_$QCItemModelImpl) then) =
      __$$QCItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? provisionItemId, double? quantityApproved, String? qcComment});
}

/// @nodoc
class __$$QCItemModelImplCopyWithImpl<$Res>
    extends _$QCItemModelCopyWithImpl<$Res, _$QCItemModelImpl>
    implements _$$QCItemModelImplCopyWith<$Res> {
  __$$QCItemModelImplCopyWithImpl(
      _$QCItemModelImpl _value, $Res Function(_$QCItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of QCItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionItemId = freezed,
    Object? quantityApproved = freezed,
    Object? qcComment = freezed,
  }) {
    return _then(_$QCItemModelImpl(
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantityApproved: freezed == quantityApproved
          ? _value.quantityApproved
          : quantityApproved // ignore: cast_nullable_to_non_nullable
              as double?,
      qcComment: freezed == qcComment
          ? _value.qcComment
          : qcComment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$QCItemModelImpl implements _QCItemModel {
  const _$QCItemModelImpl(
      {this.provisionItemId, this.quantityApproved, this.qcComment});

  factory _$QCItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$QCItemModelImplFromJson(json);

  @override
  final String? provisionItemId;
  @override
  final double? quantityApproved;
  @override
  final String? qcComment;

  @override
  String toString() {
    return 'QCItemModel(provisionItemId: $provisionItemId, quantityApproved: $quantityApproved, qcComment: $qcComment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QCItemModelImpl &&
            (identical(other.provisionItemId, provisionItemId) ||
                other.provisionItemId == provisionItemId) &&
            (identical(other.quantityApproved, quantityApproved) ||
                other.quantityApproved == quantityApproved) &&
            (identical(other.qcComment, qcComment) ||
                other.qcComment == qcComment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, provisionItemId, quantityApproved, qcComment);

  /// Create a copy of QCItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QCItemModelImplCopyWith<_$QCItemModelImpl> get copyWith =>
      __$$QCItemModelImplCopyWithImpl<_$QCItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QCItemModelImplToJson(
      this,
    );
  }
}

abstract class _QCItemModel implements QCItemModel {
  const factory _QCItemModel(
      {final String? provisionItemId,
      final double? quantityApproved,
      final String? qcComment}) = _$QCItemModelImpl;

  factory _QCItemModel.fromJson(Map<String, dynamic> json) =
      _$QCItemModelImpl.fromJson;

  @override
  String? get provisionItemId;
  @override
  double? get quantityApproved;
  @override
  String? get qcComment;

  /// Create a copy of QCItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QCItemModelImplCopyWith<_$QCItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReceiveDeliveryItemModel _$ReceiveDeliveryItemModelFromJson(
    Map<String, dynamic> json) {
  return _ReceiveDeliveryItemModel.fromJson(json);
}

/// @nodoc
mixin _$ReceiveDeliveryItemModel {
  String? get productId => throw _privateConstructorUsedError;
  String? get materialId => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;

  /// Serializes this ReceiveDeliveryItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReceiveDeliveryItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReceiveDeliveryItemModelCopyWith<ReceiveDeliveryItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReceiveDeliveryItemModelCopyWith<$Res> {
  factory $ReceiveDeliveryItemModelCopyWith(ReceiveDeliveryItemModel value,
          $Res Function(ReceiveDeliveryItemModel) then) =
      _$ReceiveDeliveryItemModelCopyWithImpl<$Res, ReceiveDeliveryItemModel>;
  @useResult
  $Res call(
      {String? productId,
      String? materialId,
      double? quantity,
      String? location});
}

/// @nodoc
class _$ReceiveDeliveryItemModelCopyWithImpl<$Res,
        $Val extends ReceiveDeliveryItemModel>
    implements $ReceiveDeliveryItemModelCopyWith<$Res> {
  _$ReceiveDeliveryItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReceiveDeliveryItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? materialId = freezed,
    Object? quantity = freezed,
    Object? location = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReceiveDeliveryItemModelImplCopyWith<$Res>
    implements $ReceiveDeliveryItemModelCopyWith<$Res> {
  factory _$$ReceiveDeliveryItemModelImplCopyWith(
          _$ReceiveDeliveryItemModelImpl value,
          $Res Function(_$ReceiveDeliveryItemModelImpl) then) =
      __$$ReceiveDeliveryItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      String? materialId,
      double? quantity,
      String? location});
}

/// @nodoc
class __$$ReceiveDeliveryItemModelImplCopyWithImpl<$Res>
    extends _$ReceiveDeliveryItemModelCopyWithImpl<$Res,
        _$ReceiveDeliveryItemModelImpl>
    implements _$$ReceiveDeliveryItemModelImplCopyWith<$Res> {
  __$$ReceiveDeliveryItemModelImplCopyWithImpl(
      _$ReceiveDeliveryItemModelImpl _value,
      $Res Function(_$ReceiveDeliveryItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReceiveDeliveryItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? materialId = freezed,
    Object? quantity = freezed,
    Object? location = freezed,
  }) {
    return _then(_$ReceiveDeliveryItemModelImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ReceiveDeliveryItemModelImpl implements _ReceiveDeliveryItemModel {
  const _$ReceiveDeliveryItemModelImpl(
      {this.productId, this.materialId, this.quantity, this.location});

  factory _$ReceiveDeliveryItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReceiveDeliveryItemModelImplFromJson(json);

  @override
  final String? productId;
  @override
  final String? materialId;
  @override
  final double? quantity;
  @override
  final String? location;

  @override
  String toString() {
    return 'ReceiveDeliveryItemModel(productId: $productId, materialId: $materialId, quantity: $quantity, location: $location)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReceiveDeliveryItemModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.location, location) ||
                other.location == location));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, productId, materialId, quantity, location);

  /// Create a copy of ReceiveDeliveryItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReceiveDeliveryItemModelImplCopyWith<_$ReceiveDeliveryItemModelImpl>
      get copyWith => __$$ReceiveDeliveryItemModelImplCopyWithImpl<
          _$ReceiveDeliveryItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReceiveDeliveryItemModelImplToJson(
      this,
    );
  }
}

abstract class _ReceiveDeliveryItemModel implements ReceiveDeliveryItemModel {
  const factory _ReceiveDeliveryItemModel(
      {final String? productId,
      final String? materialId,
      final double? quantity,
      final String? location}) = _$ReceiveDeliveryItemModelImpl;

  factory _ReceiveDeliveryItemModel.fromJson(Map<String, dynamic> json) =
      _$ReceiveDeliveryItemModelImpl.fromJson;

  @override
  String? get productId;
  @override
  String? get materialId;
  @override
  double? get quantity;
  @override
  String? get location;

  /// Create a copy of ReceiveDeliveryItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReceiveDeliveryItemModelImplCopyWith<_$ReceiveDeliveryItemModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
