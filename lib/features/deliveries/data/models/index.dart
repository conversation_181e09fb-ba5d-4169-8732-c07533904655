// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';

export 'delivery_search_filters.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
class DeliveriesSearchOutputModel with _$DeliveriesSearchOutputModel {
  @JsonSerializable(includeIfNull: false)
  const factory DeliveriesSearchOutputModel({
    List<DeliveryModel>? items,
    int? totalItems,
  }) = _DeliveriesSearchOutputModel;

  factory DeliveriesSearchOutputModel.fromJson(Map<String, dynamic> json) =>
      _$DeliveriesSearchOutputModelFromJson(json);
}

@freezed
class QCItemModel with _$QCItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory QCItemModel({
    String? provisionItemId,
    double? quantityApproved,
    String? qcComment,
  }) = _QCItemModel;

  factory QCItemModel.fromJson(Map<String, dynamic> json) =>
      _$QCItemModelFromJson(json);
}

@freezed
class ReceiveDeliveryItemModel with _$ReceiveDeliveryItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory ReceiveDeliveryItemModel({
    String? productId,
    String? materialId,
    double? quantity,
    String? location,
  }) = _ReceiveDeliveryItemModel;

  factory ReceiveDeliveryItemModel.fromJson(Map<String, dynamic> json) =>
      _$ReceiveDeliveryItemModelFromJson(json);
}
