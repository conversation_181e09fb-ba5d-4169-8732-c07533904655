// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'delivery.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DeliveryGroupModel _$DeliveryGroupModelFromJson(Map<String, dynamic> json) {
  return _DeliveryGroupModel.fromJson(json);
}

/// @nodoc
mixin _$DeliveryGroupModel {
  DateTime? get expectedDate => throw _privateConstructorUsedError;
  List<DeliveryMaterialModel>? get materials =>
      throw _privateConstructorUsedError;

  /// Serializes this DeliveryGroupModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeliveryGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliveryGroupModelCopyWith<DeliveryGroupModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliveryGroupModelCopyWith<$Res> {
  factory $DeliveryGroupModelCopyWith(
          DeliveryGroupModel value, $Res Function(DeliveryGroupModel) then) =
      _$DeliveryGroupModelCopyWithImpl<$Res, DeliveryGroupModel>;
  @useResult
  $Res call({DateTime? expectedDate, List<DeliveryMaterialModel>? materials});
}

/// @nodoc
class _$DeliveryGroupModelCopyWithImpl<$Res, $Val extends DeliveryGroupModel>
    implements $DeliveryGroupModelCopyWith<$Res> {
  _$DeliveryGroupModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliveryGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expectedDate = freezed,
    Object? materials = freezed,
  }) {
    return _then(_value.copyWith(
      expectedDate: freezed == expectedDate
          ? _value.expectedDate
          : expectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      materials: freezed == materials
          ? _value.materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<DeliveryMaterialModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliveryGroupModelImplCopyWith<$Res>
    implements $DeliveryGroupModelCopyWith<$Res> {
  factory _$$DeliveryGroupModelImplCopyWith(_$DeliveryGroupModelImpl value,
          $Res Function(_$DeliveryGroupModelImpl) then) =
      __$$DeliveryGroupModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime? expectedDate, List<DeliveryMaterialModel>? materials});
}

/// @nodoc
class __$$DeliveryGroupModelImplCopyWithImpl<$Res>
    extends _$DeliveryGroupModelCopyWithImpl<$Res, _$DeliveryGroupModelImpl>
    implements _$$DeliveryGroupModelImplCopyWith<$Res> {
  __$$DeliveryGroupModelImplCopyWithImpl(_$DeliveryGroupModelImpl _value,
      $Res Function(_$DeliveryGroupModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliveryGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? expectedDate = freezed,
    Object? materials = freezed,
  }) {
    return _then(_$DeliveryGroupModelImpl(
      expectedDate: freezed == expectedDate
          ? _value.expectedDate
          : expectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      materials: freezed == materials
          ? _value._materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<DeliveryMaterialModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$DeliveryGroupModelImpl implements _DeliveryGroupModel {
  const _$DeliveryGroupModelImpl(
      {this.expectedDate, final List<DeliveryMaterialModel>? materials})
      : _materials = materials;

  factory _$DeliveryGroupModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeliveryGroupModelImplFromJson(json);

  @override
  final DateTime? expectedDate;
  final List<DeliveryMaterialModel>? _materials;
  @override
  List<DeliveryMaterialModel>? get materials {
    final value = _materials;
    if (value == null) return null;
    if (_materials is EqualUnmodifiableListView) return _materials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'DeliveryGroupModel(expectedDate: $expectedDate, materials: $materials)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliveryGroupModelImpl &&
            (identical(other.expectedDate, expectedDate) ||
                other.expectedDate == expectedDate) &&
            const DeepCollectionEquality()
                .equals(other._materials, _materials));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, expectedDate,
      const DeepCollectionEquality().hash(_materials));

  /// Create a copy of DeliveryGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliveryGroupModelImplCopyWith<_$DeliveryGroupModelImpl> get copyWith =>
      __$$DeliveryGroupModelImplCopyWithImpl<_$DeliveryGroupModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeliveryGroupModelImplToJson(
      this,
    );
  }
}

abstract class _DeliveryGroupModel implements DeliveryGroupModel {
  const factory _DeliveryGroupModel(
      {final DateTime? expectedDate,
      final List<DeliveryMaterialModel>? materials}) = _$DeliveryGroupModelImpl;

  factory _DeliveryGroupModel.fromJson(Map<String, dynamic> json) =
      _$DeliveryGroupModelImpl.fromJson;

  @override
  DateTime? get expectedDate;
  @override
  List<DeliveryMaterialModel>? get materials;

  /// Create a copy of DeliveryGroupModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliveryGroupModelImplCopyWith<_$DeliveryGroupModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeliveryModel _$DeliveryModelFromJson(Map<String, dynamic> json) {
  return _DeliveryModel.fromJson(json);
}

/// @nodoc
mixin _$DeliveryModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get provisionId => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  DeliveryStatus? get status => throw _privateConstructorUsedError;
  ClientModel? get supplier => throw _privateConstructorUsedError;
  DateTime? get expectedDate =>
      throw _privateConstructorUsedError; // List<DateTime>? deliveryDates,
  DateTime? get deliveryDate => throw _privateConstructorUsedError;
  List<DeliveryMaterialModel>? get items => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this DeliveryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliveryModelCopyWith<DeliveryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliveryModelCopyWith<$Res> {
  factory $DeliveryModelCopyWith(
          DeliveryModel value, $Res Function(DeliveryModel) then) =
      _$DeliveryModelCopyWithImpl<$Res, DeliveryModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? provisionId,
      String? projectId,
      DeliveryStatus? status,
      ClientModel? supplier,
      DateTime? expectedDate,
      DateTime? deliveryDate,
      List<DeliveryMaterialModel>? items,
      String? comment,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$DeliveryModelCopyWithImpl<$Res, $Val extends DeliveryModel>
    implements $DeliveryModelCopyWith<$Res> {
  _$DeliveryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? provisionId = freezed,
    Object? projectId = freezed,
    Object? status = freezed,
    Object? supplier = freezed,
    Object? expectedDate = freezed,
    Object? deliveryDate = freezed,
    Object? items = freezed,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DeliveryStatus?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      expectedDate: freezed == expectedDate
          ? _value.expectedDate
          : expectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDate: freezed == deliveryDate
          ? _value.deliveryDate
          : deliveryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<DeliveryMaterialModel>?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliveryModelImplCopyWith<$Res>
    implements $DeliveryModelCopyWith<$Res> {
  factory _$$DeliveryModelImplCopyWith(
          _$DeliveryModelImpl value, $Res Function(_$DeliveryModelImpl) then) =
      __$$DeliveryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? provisionId,
      String? projectId,
      DeliveryStatus? status,
      ClientModel? supplier,
      DateTime? expectedDate,
      DateTime? deliveryDate,
      List<DeliveryMaterialModel>? items,
      String? comment,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$DeliveryModelImplCopyWithImpl<$Res>
    extends _$DeliveryModelCopyWithImpl<$Res, _$DeliveryModelImpl>
    implements _$$DeliveryModelImplCopyWith<$Res> {
  __$$DeliveryModelImplCopyWithImpl(
      _$DeliveryModelImpl _value, $Res Function(_$DeliveryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? provisionId = freezed,
    Object? projectId = freezed,
    Object? status = freezed,
    Object? supplier = freezed,
    Object? expectedDate = freezed,
    Object? deliveryDate = freezed,
    Object? items = freezed,
    Object? comment = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$DeliveryModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DeliveryStatus?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      expectedDate: freezed == expectedDate
          ? _value.expectedDate
          : expectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDate: freezed == deliveryDate
          ? _value.deliveryDate
          : deliveryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<DeliveryMaterialModel>?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$DeliveryModelImpl implements _DeliveryModel {
  const _$DeliveryModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.provisionId,
      this.projectId,
      this.status,
      this.supplier,
      this.expectedDate,
      this.deliveryDate,
      final List<DeliveryMaterialModel>? items,
      this.comment,
      this.createdAt,
      this.updatedAt})
      : _items = items;

  factory _$DeliveryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeliveryModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? provisionId;
  @override
  final String? projectId;
  @override
  final DeliveryStatus? status;
  @override
  final ClientModel? supplier;
  @override
  final DateTime? expectedDate;
// List<DateTime>? deliveryDates,
  @override
  final DateTime? deliveryDate;
  final List<DeliveryMaterialModel>? _items;
  @override
  List<DeliveryMaterialModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? comment;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'DeliveryModel(id: $id, provisionId: $provisionId, projectId: $projectId, status: $status, supplier: $supplier, expectedDate: $expectedDate, deliveryDate: $deliveryDate, items: $items, comment: $comment, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliveryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.provisionId, provisionId) ||
                other.provisionId == provisionId) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.supplier, supplier) ||
                other.supplier == supplier) &&
            (identical(other.expectedDate, expectedDate) ||
                other.expectedDate == expectedDate) &&
            (identical(other.deliveryDate, deliveryDate) ||
                other.deliveryDate == deliveryDate) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      provisionId,
      projectId,
      status,
      supplier,
      expectedDate,
      deliveryDate,
      const DeepCollectionEquality().hash(_items),
      comment,
      createdAt,
      updatedAt);

  /// Create a copy of DeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliveryModelImplCopyWith<_$DeliveryModelImpl> get copyWith =>
      __$$DeliveryModelImplCopyWithImpl<_$DeliveryModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeliveryModelImplToJson(
      this,
    );
  }
}

abstract class _DeliveryModel implements DeliveryModel {
  const factory _DeliveryModel(
      {@JsonKey(name: '_id') final String? id,
      final String? provisionId,
      final String? projectId,
      final DeliveryStatus? status,
      final ClientModel? supplier,
      final DateTime? expectedDate,
      final DateTime? deliveryDate,
      final List<DeliveryMaterialModel>? items,
      final String? comment,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$DeliveryModelImpl;

  factory _DeliveryModel.fromJson(Map<String, dynamic> json) =
      _$DeliveryModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get provisionId;
  @override
  String? get projectId;
  @override
  DeliveryStatus? get status;
  @override
  ClientModel? get supplier;
  @override
  DateTime? get expectedDate; // List<DateTime>? deliveryDates,
  @override
  DateTime? get deliveryDate;
  @override
  List<DeliveryMaterialModel>? get items;
  @override
  String? get comment;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of DeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliveryModelImplCopyWith<_$DeliveryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DeliveryMaterialModel _$DeliveryMaterialModelFromJson(
    Map<String, dynamic> json) {
  return _DeliveryMaterialModel.fromJson(json);
}

/// @nodoc
mixin _$DeliveryMaterialModel {
  String? get productId => throw _privateConstructorUsedError;
  String? get provisionItemId =>
      throw _privateConstructorUsedError; // String? materialRequirements,
// List<ProvisionDeliveryLinkModel>? provisionItemLinks,
  double? get quantityApproved => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  UnitType? get unitType => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;

  /// Serializes this DeliveryMaterialModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeliveryMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliveryMaterialModelCopyWith<DeliveryMaterialModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliveryMaterialModelCopyWith<$Res> {
  factory $DeliveryMaterialModelCopyWith(DeliveryMaterialModel value,
          $Res Function(DeliveryMaterialModel) then) =
      _$DeliveryMaterialModelCopyWithImpl<$Res, DeliveryMaterialModel>;
  @useResult
  $Res call(
      {String? productId,
      String? provisionItemId,
      double? quantityApproved,
      double? quantity,
      double? price,
      UnitType? unitType,
      String? materialName});
}

/// @nodoc
class _$DeliveryMaterialModelCopyWithImpl<$Res,
        $Val extends DeliveryMaterialModel>
    implements $DeliveryMaterialModelCopyWith<$Res> {
  _$DeliveryMaterialModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliveryMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? provisionItemId = freezed,
    Object? quantityApproved = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? unitType = freezed,
    Object? materialName = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantityApproved: freezed == quantityApproved
          ? _value.quantityApproved
          : quantityApproved // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliveryMaterialModelImplCopyWith<$Res>
    implements $DeliveryMaterialModelCopyWith<$Res> {
  factory _$$DeliveryMaterialModelImplCopyWith(
          _$DeliveryMaterialModelImpl value,
          $Res Function(_$DeliveryMaterialModelImpl) then) =
      __$$DeliveryMaterialModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      String? provisionItemId,
      double? quantityApproved,
      double? quantity,
      double? price,
      UnitType? unitType,
      String? materialName});
}

/// @nodoc
class __$$DeliveryMaterialModelImplCopyWithImpl<$Res>
    extends _$DeliveryMaterialModelCopyWithImpl<$Res,
        _$DeliveryMaterialModelImpl>
    implements _$$DeliveryMaterialModelImplCopyWith<$Res> {
  __$$DeliveryMaterialModelImplCopyWithImpl(_$DeliveryMaterialModelImpl _value,
      $Res Function(_$DeliveryMaterialModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliveryMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? provisionItemId = freezed,
    Object? quantityApproved = freezed,
    Object? quantity = freezed,
    Object? price = freezed,
    Object? unitType = freezed,
    Object? materialName = freezed,
  }) {
    return _then(_$DeliveryMaterialModelImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantityApproved: freezed == quantityApproved
          ? _value.quantityApproved
          : quantityApproved // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$DeliveryMaterialModelImpl implements _DeliveryMaterialModel {
  const _$DeliveryMaterialModelImpl(
      {this.productId,
      this.provisionItemId,
      this.quantityApproved,
      this.quantity,
      this.price,
      this.unitType,
      this.materialName});

  factory _$DeliveryMaterialModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeliveryMaterialModelImplFromJson(json);

  @override
  final String? productId;
  @override
  final String? provisionItemId;
// String? materialRequirements,
// List<ProvisionDeliveryLinkModel>? provisionItemLinks,
  @override
  final double? quantityApproved;
  @override
  final double? quantity;
  @override
  final double? price;
  @override
  final UnitType? unitType;
  @override
  final String? materialName;

  @override
  String toString() {
    return 'DeliveryMaterialModel(productId: $productId, provisionItemId: $provisionItemId, quantityApproved: $quantityApproved, quantity: $quantity, price: $price, unitType: $unitType, materialName: $materialName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliveryMaterialModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.provisionItemId, provisionItemId) ||
                other.provisionItemId == provisionItemId) &&
            (identical(other.quantityApproved, quantityApproved) ||
                other.quantityApproved == quantityApproved) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.unitType, unitType) ||
                other.unitType == unitType) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, productId, provisionItemId,
      quantityApproved, quantity, price, unitType, materialName);

  /// Create a copy of DeliveryMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliveryMaterialModelImplCopyWith<_$DeliveryMaterialModelImpl>
      get copyWith => __$$DeliveryMaterialModelImplCopyWithImpl<
          _$DeliveryMaterialModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeliveryMaterialModelImplToJson(
      this,
    );
  }
}

abstract class _DeliveryMaterialModel implements DeliveryMaterialModel {
  const factory _DeliveryMaterialModel(
      {final String? productId,
      final String? provisionItemId,
      final double? quantityApproved,
      final double? quantity,
      final double? price,
      final UnitType? unitType,
      final String? materialName}) = _$DeliveryMaterialModelImpl;

  factory _DeliveryMaterialModel.fromJson(Map<String, dynamic> json) =
      _$DeliveryMaterialModelImpl.fromJson;

  @override
  String? get productId;
  @override
  String? get provisionItemId; // String? materialRequirements,
// List<ProvisionDeliveryLinkModel>? provisionItemLinks,
  @override
  double? get quantityApproved;
  @override
  double? get quantity;
  @override
  double? get price;
  @override
  UnitType? get unitType;
  @override
  String? get materialName;

  /// Create a copy of DeliveryMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliveryMaterialModelImplCopyWith<_$DeliveryMaterialModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
