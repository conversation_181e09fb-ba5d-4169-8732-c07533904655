// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'delivery_search_filters.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DeliverySearchFilters _$DeliverySearchFiltersFromJson(
    Map<String, dynamic> json) {
  return _DeliverySearchFilters.fromJson(json);
}

/// @nodoc
mixin _$DeliverySearchFilters {
// Основные фильтры
  DateTime? get dateFrom => throw _privateConstructorUsedError;
  DateTime? get dateTo => throw _privateConstructorUsedError;
  DateTime? get expectedDateFrom => throw _privateConstructorUsedError;
  DateTime? get expectedDateTo => throw _privateConstructorUsedError;
  DateTime? get deliveryDateFrom => throw _privateConstructorUsedError;
  DateTime? get deliveryDateTo =>
      throw _privateConstructorUsedError; // Статус поставки
  List<DeliveryStatus>? get statuses =>
      throw _privateConstructorUsedError; // Поставщик
  String? get supplierId => throw _privateConstructorUsedError;
  String? get supplierName => throw _privateConstructorUsedError; // Проект
  String get projectId => throw _privateConstructorUsedError;
  String? get provisionId => throw _privateConstructorUsedError; // Материалы
  String? get materialName => throw _privateConstructorUsedError;
  List<String>? get materialIds =>
      throw _privateConstructorUsedError; // Количество и стоимость
  double? get quantityFrom => throw _privateConstructorUsedError;
  double? get quantityTo => throw _privateConstructorUsedError;
  double? get priceFrom => throw _privateConstructorUsedError;
  double? get priceTo => throw _privateConstructorUsedError; // Комментарии
  String? get comment => throw _privateConstructorUsedError; // Поиск по тексту
  String? get query => throw _privateConstructorUsedError;

  /// Serializes this DeliverySearchFilters to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeliverySearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliverySearchFiltersCopyWith<DeliverySearchFilters> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliverySearchFiltersCopyWith<$Res> {
  factory $DeliverySearchFiltersCopyWith(DeliverySearchFilters value,
          $Res Function(DeliverySearchFilters) then) =
      _$DeliverySearchFiltersCopyWithImpl<$Res, DeliverySearchFilters>;
  @useResult
  $Res call(
      {DateTime? dateFrom,
      DateTime? dateTo,
      DateTime? expectedDateFrom,
      DateTime? expectedDateTo,
      DateTime? deliveryDateFrom,
      DateTime? deliveryDateTo,
      List<DeliveryStatus>? statuses,
      String? supplierId,
      String? supplierName,
      String projectId,
      String? provisionId,
      String? materialName,
      List<String>? materialIds,
      double? quantityFrom,
      double? quantityTo,
      double? priceFrom,
      double? priceTo,
      String? comment,
      String? query});
}

/// @nodoc
class _$DeliverySearchFiltersCopyWithImpl<$Res,
        $Val extends DeliverySearchFilters>
    implements $DeliverySearchFiltersCopyWith<$Res> {
  _$DeliverySearchFiltersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliverySearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? expectedDateFrom = freezed,
    Object? expectedDateTo = freezed,
    Object? deliveryDateFrom = freezed,
    Object? deliveryDateTo = freezed,
    Object? statuses = freezed,
    Object? supplierId = freezed,
    Object? supplierName = freezed,
    Object? projectId = null,
    Object? provisionId = freezed,
    Object? materialName = freezed,
    Object? materialIds = freezed,
    Object? quantityFrom = freezed,
    Object? quantityTo = freezed,
    Object? priceFrom = freezed,
    Object? priceTo = freezed,
    Object? comment = freezed,
    Object? query = freezed,
  }) {
    return _then(_value.copyWith(
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expectedDateFrom: freezed == expectedDateFrom
          ? _value.expectedDateFrom
          : expectedDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expectedDateTo: freezed == expectedDateTo
          ? _value.expectedDateTo
          : expectedDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateFrom: freezed == deliveryDateFrom
          ? _value.deliveryDateFrom
          : deliveryDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateTo: freezed == deliveryDateTo
          ? _value.deliveryDateTo
          : deliveryDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      statuses: freezed == statuses
          ? _value.statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<DeliveryStatus>?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      supplierName: freezed == supplierName
          ? _value.supplierName
          : supplierName // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialIds: freezed == materialIds
          ? _value.materialIds
          : materialIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      quantityFrom: freezed == quantityFrom
          ? _value.quantityFrom
          : quantityFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityTo: freezed == quantityTo
          ? _value.quantityTo
          : quantityTo // ignore: cast_nullable_to_non_nullable
              as double?,
      priceFrom: freezed == priceFrom
          ? _value.priceFrom
          : priceFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      priceTo: freezed == priceTo
          ? _value.priceTo
          : priceTo // ignore: cast_nullable_to_non_nullable
              as double?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      query: freezed == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliverySearchFiltersImplCopyWith<$Res>
    implements $DeliverySearchFiltersCopyWith<$Res> {
  factory _$$DeliverySearchFiltersImplCopyWith(
          _$DeliverySearchFiltersImpl value,
          $Res Function(_$DeliverySearchFiltersImpl) then) =
      __$$DeliverySearchFiltersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime? dateFrom,
      DateTime? dateTo,
      DateTime? expectedDateFrom,
      DateTime? expectedDateTo,
      DateTime? deliveryDateFrom,
      DateTime? deliveryDateTo,
      List<DeliveryStatus>? statuses,
      String? supplierId,
      String? supplierName,
      String projectId,
      String? provisionId,
      String? materialName,
      List<String>? materialIds,
      double? quantityFrom,
      double? quantityTo,
      double? priceFrom,
      double? priceTo,
      String? comment,
      String? query});
}

/// @nodoc
class __$$DeliverySearchFiltersImplCopyWithImpl<$Res>
    extends _$DeliverySearchFiltersCopyWithImpl<$Res,
        _$DeliverySearchFiltersImpl>
    implements _$$DeliverySearchFiltersImplCopyWith<$Res> {
  __$$DeliverySearchFiltersImplCopyWithImpl(_$DeliverySearchFiltersImpl _value,
      $Res Function(_$DeliverySearchFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliverySearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? expectedDateFrom = freezed,
    Object? expectedDateTo = freezed,
    Object? deliveryDateFrom = freezed,
    Object? deliveryDateTo = freezed,
    Object? statuses = freezed,
    Object? supplierId = freezed,
    Object? supplierName = freezed,
    Object? projectId = null,
    Object? provisionId = freezed,
    Object? materialName = freezed,
    Object? materialIds = freezed,
    Object? quantityFrom = freezed,
    Object? quantityTo = freezed,
    Object? priceFrom = freezed,
    Object? priceTo = freezed,
    Object? comment = freezed,
    Object? query = freezed,
  }) {
    return _then(_$DeliverySearchFiltersImpl(
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expectedDateFrom: freezed == expectedDateFrom
          ? _value.expectedDateFrom
          : expectedDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expectedDateTo: freezed == expectedDateTo
          ? _value.expectedDateTo
          : expectedDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateFrom: freezed == deliveryDateFrom
          ? _value.deliveryDateFrom
          : deliveryDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateTo: freezed == deliveryDateTo
          ? _value.deliveryDateTo
          : deliveryDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      statuses: freezed == statuses
          ? _value._statuses
          : statuses // ignore: cast_nullable_to_non_nullable
              as List<DeliveryStatus>?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      supplierName: freezed == supplierName
          ? _value.supplierName
          : supplierName // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialIds: freezed == materialIds
          ? _value._materialIds
          : materialIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      quantityFrom: freezed == quantityFrom
          ? _value.quantityFrom
          : quantityFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityTo: freezed == quantityTo
          ? _value.quantityTo
          : quantityTo // ignore: cast_nullable_to_non_nullable
              as double?,
      priceFrom: freezed == priceFrom
          ? _value.priceFrom
          : priceFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      priceTo: freezed == priceTo
          ? _value.priceTo
          : priceTo // ignore: cast_nullable_to_non_nullable
              as double?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      query: freezed == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeliverySearchFiltersImpl implements _DeliverySearchFilters {
  const _$DeliverySearchFiltersImpl(
      {this.dateFrom,
      this.dateTo,
      this.expectedDateFrom,
      this.expectedDateTo,
      this.deliveryDateFrom,
      this.deliveryDateTo,
      final List<DeliveryStatus>? statuses,
      this.supplierId,
      this.supplierName,
      required this.projectId,
      this.provisionId,
      this.materialName,
      final List<String>? materialIds,
      this.quantityFrom,
      this.quantityTo,
      this.priceFrom,
      this.priceTo,
      this.comment,
      this.query})
      : _statuses = statuses,
        _materialIds = materialIds;

  factory _$DeliverySearchFiltersImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeliverySearchFiltersImplFromJson(json);

// Основные фильтры
  @override
  final DateTime? dateFrom;
  @override
  final DateTime? dateTo;
  @override
  final DateTime? expectedDateFrom;
  @override
  final DateTime? expectedDateTo;
  @override
  final DateTime? deliveryDateFrom;
  @override
  final DateTime? deliveryDateTo;
// Статус поставки
  final List<DeliveryStatus>? _statuses;
// Статус поставки
  @override
  List<DeliveryStatus>? get statuses {
    final value = _statuses;
    if (value == null) return null;
    if (_statuses is EqualUnmodifiableListView) return _statuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Поставщик
  @override
  final String? supplierId;
  @override
  final String? supplierName;
// Проект
  @override
  final String projectId;
  @override
  final String? provisionId;
// Материалы
  @override
  final String? materialName;
  final List<String>? _materialIds;
  @override
  List<String>? get materialIds {
    final value = _materialIds;
    if (value == null) return null;
    if (_materialIds is EqualUnmodifiableListView) return _materialIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Количество и стоимость
  @override
  final double? quantityFrom;
  @override
  final double? quantityTo;
  @override
  final double? priceFrom;
  @override
  final double? priceTo;
// Комментарии
  @override
  final String? comment;
// Поиск по тексту
  @override
  final String? query;

  @override
  String toString() {
    return 'DeliverySearchFilters(dateFrom: $dateFrom, dateTo: $dateTo, expectedDateFrom: $expectedDateFrom, expectedDateTo: $expectedDateTo, deliveryDateFrom: $deliveryDateFrom, deliveryDateTo: $deliveryDateTo, statuses: $statuses, supplierId: $supplierId, supplierName: $supplierName, projectId: $projectId, provisionId: $provisionId, materialName: $materialName, materialIds: $materialIds, quantityFrom: $quantityFrom, quantityTo: $quantityTo, priceFrom: $priceFrom, priceTo: $priceTo, comment: $comment, query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliverySearchFiltersImpl &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo) &&
            (identical(other.expectedDateFrom, expectedDateFrom) ||
                other.expectedDateFrom == expectedDateFrom) &&
            (identical(other.expectedDateTo, expectedDateTo) ||
                other.expectedDateTo == expectedDateTo) &&
            (identical(other.deliveryDateFrom, deliveryDateFrom) ||
                other.deliveryDateFrom == deliveryDateFrom) &&
            (identical(other.deliveryDateTo, deliveryDateTo) ||
                other.deliveryDateTo == deliveryDateTo) &&
            const DeepCollectionEquality().equals(other._statuses, _statuses) &&
            (identical(other.supplierId, supplierId) ||
                other.supplierId == supplierId) &&
            (identical(other.supplierName, supplierName) ||
                other.supplierName == supplierName) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.provisionId, provisionId) ||
                other.provisionId == provisionId) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            const DeepCollectionEquality()
                .equals(other._materialIds, _materialIds) &&
            (identical(other.quantityFrom, quantityFrom) ||
                other.quantityFrom == quantityFrom) &&
            (identical(other.quantityTo, quantityTo) ||
                other.quantityTo == quantityTo) &&
            (identical(other.priceFrom, priceFrom) ||
                other.priceFrom == priceFrom) &&
            (identical(other.priceTo, priceTo) || other.priceTo == priceTo) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.query, query) || other.query == query));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        dateFrom,
        dateTo,
        expectedDateFrom,
        expectedDateTo,
        deliveryDateFrom,
        deliveryDateTo,
        const DeepCollectionEquality().hash(_statuses),
        supplierId,
        supplierName,
        projectId,
        provisionId,
        materialName,
        const DeepCollectionEquality().hash(_materialIds),
        quantityFrom,
        quantityTo,
        priceFrom,
        priceTo,
        comment,
        query
      ]);

  /// Create a copy of DeliverySearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliverySearchFiltersImplCopyWith<_$DeliverySearchFiltersImpl>
      get copyWith => __$$DeliverySearchFiltersImplCopyWithImpl<
          _$DeliverySearchFiltersImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeliverySearchFiltersImplToJson(
      this,
    );
  }
}

abstract class _DeliverySearchFilters implements DeliverySearchFilters {
  const factory _DeliverySearchFilters(
      {final DateTime? dateFrom,
      final DateTime? dateTo,
      final DateTime? expectedDateFrom,
      final DateTime? expectedDateTo,
      final DateTime? deliveryDateFrom,
      final DateTime? deliveryDateTo,
      final List<DeliveryStatus>? statuses,
      final String? supplierId,
      final String? supplierName,
      required final String projectId,
      final String? provisionId,
      final String? materialName,
      final List<String>? materialIds,
      final double? quantityFrom,
      final double? quantityTo,
      final double? priceFrom,
      final double? priceTo,
      final String? comment,
      final String? query}) = _$DeliverySearchFiltersImpl;

  factory _DeliverySearchFilters.fromJson(Map<String, dynamic> json) =
      _$DeliverySearchFiltersImpl.fromJson;

// Основные фильтры
  @override
  DateTime? get dateFrom;
  @override
  DateTime? get dateTo;
  @override
  DateTime? get expectedDateFrom;
  @override
  DateTime? get expectedDateTo;
  @override
  DateTime? get deliveryDateFrom;
  @override
  DateTime? get deliveryDateTo; // Статус поставки
  @override
  List<DeliveryStatus>? get statuses; // Поставщик
  @override
  String? get supplierId;
  @override
  String? get supplierName; // Проект
  @override
  String get projectId;
  @override
  String? get provisionId; // Материалы
  @override
  String? get materialName;
  @override
  List<String>? get materialIds; // Количество и стоимость
  @override
  double? get quantityFrom;
  @override
  double? get quantityTo;
  @override
  double? get priceFrom;
  @override
  double? get priceTo; // Комментарии
  @override
  String? get comment; // Поиск по тексту
  @override
  String? get query;

  /// Create a copy of DeliverySearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliverySearchFiltersImplCopyWith<_$DeliverySearchFiltersImpl>
      get copyWith => throw _privateConstructorUsedError;
}
