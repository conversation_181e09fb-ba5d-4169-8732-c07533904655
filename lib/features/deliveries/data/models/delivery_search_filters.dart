// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';

part 'delivery_search_filters.freezed.dart';
part 'delivery_search_filters.g.dart';

/// Фильтры для поиска поставок
@freezed
class DeliverySearchFilters with _$DeliverySearchFilters {
  const factory DeliverySearchFilters({
    // Основные фильтры
    DateTime? dateFrom,
    DateTime? dateTo,
    DateTime? expectedDateFrom,
    DateTime? expectedDateTo,
    DateTime? deliveryDateFrom,
    DateTime? deliveryDateTo,
    
    // Статус поставки
    List<DeliveryStatus>? statuses,
    
    // Поставщик
    String? supplierId,
    String? supplierName,
    
    // Проект
    required String projectId,
    String? provisionId,
    
    // Материалы
    String? materialName,
    List<String>? materialIds,
    
    // Количество и стоимость
    double? quantityFrom,
    double? quantityTo,
    double? priceFrom,
    double? priceTo,
    
    // Комментарии
    String? comment,
    
    // Поиск по тексту
    String? query,
  }) = _DeliverySearchFilters;

  factory DeliverySearchFilters.fromJson(Map<String, dynamic> json) =>
      _$DeliverySearchFiltersFromJson(json);
}

enum DeliveryFilter {
  all,
  pending,
  inTransit,
  delivered,
  accepted,
  rejected,
  cancelled;

  String getName() {
    switch (this) {
      case DeliveryFilter.all:
        return 'Все';
      case DeliveryFilter.pending:
        return 'Ожидают';
      case DeliveryFilter.inTransit:
        return 'В пути';
      case DeliveryFilter.delivered:
        return 'Доставлено';
      case DeliveryFilter.accepted:
        return 'Принято';
      case DeliveryFilter.rejected:
        return 'Отклонено';
      case DeliveryFilter.cancelled:
        return 'Отменено';
    }
  }
}
