// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeliveriesSearchOutputModelImpl _$$DeliveriesSearchOutputModelImplFromJson(
        Map<String, dynamic> json) =>
    _$DeliveriesSearchOutputModelImpl(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => DeliveryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$DeliveriesSearchOutputModelImplToJson(
        _$DeliveriesSearchOutputModelImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_$QCItemModelImpl _$$QCItemModelImplFromJson(Map<String, dynamic> json) =>
    _$QCItemModelImpl(
      provisionItemId: json['provisionItemId'] as String?,
      quantityApproved: (json['quantityApproved'] as num?)?.toDouble(),
      qcComment: json['qcComment'] as String?,
    );

Map<String, dynamic> _$$QCItemModelImplToJson(_$QCItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.provisionItemId case final value?) 'provisionItemId': value,
      if (instance.quantityApproved case final value?)
        'quantityApproved': value,
      if (instance.qcComment case final value?) 'qcComment': value,
    };

_$ReceiveDeliveryItemModelImpl _$$ReceiveDeliveryItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ReceiveDeliveryItemModelImpl(
      productId: json['productId'] as String?,
      materialId: json['materialId'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble(),
      location: json['location'] as String?,
    );

Map<String, dynamic> _$$ReceiveDeliveryItemModelImplToJson(
        _$ReceiveDeliveryItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.materialId case final value?) 'materialId': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.location case final value?) 'location': value,
    };
