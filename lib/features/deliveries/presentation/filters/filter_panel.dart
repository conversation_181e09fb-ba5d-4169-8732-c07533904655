import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/deliveries/presentation/bloc/list/bloc.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/fonts.dart';

class DeliveryFilterPanel extends StatefulWidget {
  const DeliveryFilterPanel({
    super.key,
    this.projectId,
    this.columnIndex,
  });

  final String? projectId;
  final int? columnIndex;

  @override
  State<DeliveryFilterPanel> createState() => _DeliveryFilterPanelState();
}

class _DeliveryFilterPanelState extends State<DeliveryFilterPanel> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _dateFromController = TextEditingController();
  final TextEditingController _dateToController = TextEditingController();
  final TextEditingController _quantityFromController = TextEditingController();
  final TextEditingController _quantityToController = TextEditingController();
  final TextEditingController _priceFromController = TextEditingController();
  final TextEditingController _priceToController = TextEditingController();

  DateTime? _dateFrom;
  DateTime? _dateTo;
  final List<DeliveryStatus> _selectedStatuses = [];

  @override
  void initState() {
    super.initState();
    _initializeFilters();
  }

  void _initializeFilters() {
    // Initialize with current filters if any
    final currentFilters = context.read<BlocDeliveries>().state.searchFilters;
    if (currentFilters?.filters != null) {
      _searchController.text = currentFilters!.filters!.query ?? '';
      _dateFrom = currentFilters.filters!.dateFrom;
      _dateTo = currentFilters.filters!.dateTo;

      if (_dateFrom != null) {
        _dateFromController.text = DateFormat('dd.MM.yyyy').format(_dateFrom!);
      }
      if (_dateTo != null) {
        _dateToController.text = DateFormat('dd.MM.yyyy').format(_dateTo!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BlocDeliveries, BlocDeliveriesState>(
      builder: (context, state) {
        return Container(
          width: 400,
          height: 600,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Фильтры поставок',
                      style: Fonts.titleMedium,
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),

              // Filters content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Search field
                      TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          labelText: 'Поиск',
                          hintText: 'Введите текст для поиска',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Date range
                      Text('Период создания', style: Fonts.labelMedium),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _dateFromController,
                              decoration: const InputDecoration(
                                labelText: 'С',
                                hintText: 'дд.мм.гггг',
                                border: OutlineInputBorder(),
                              ),
                              readOnly: true,
                              onTap: () => _selectDate(context, true),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextField(
                              controller: _dateToController,
                              decoration: const InputDecoration(
                                labelText: 'По',
                                hintText: 'дд.мм.гггг',
                                border: OutlineInputBorder(),
                              ),
                              readOnly: true,
                              onTap: () => _selectDate(context, false),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Status filter
                      Text('Статус поставки', style: Fonts.labelMedium),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: DeliveryStatus.values.map((status) {
                          final isSelected = _selectedStatuses.contains(status);
                          return FilterChip(
                            label: Text(status.getName()),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  _selectedStatuses.add(status);
                                } else {
                                  _selectedStatuses.remove(status);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 16),

                      // Quantity range
                      Text('Количество', style: Fonts.labelMedium),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _quantityFromController,
                              decoration: const InputDecoration(
                                labelText: 'От',
                                hintText: '0',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextField(
                              controller: _quantityToController,
                              decoration: const InputDecoration(
                                labelText: 'До',
                                hintText: '999999',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Price range
                      Text('Стоимость', style: Fonts.labelMedium),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _priceFromController,
                              decoration: const InputDecoration(
                                labelText: 'От',
                                hintText: '0',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextField(
                              controller: _priceToController,
                              decoration: const InputDecoration(
                                labelText: 'До',
                                hintText: '999999',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Footer buttons
              const Divider(height: 1),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: _clearFilters,
                        child: const Text('Очистить'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _applyFilters,
                        child: const Text('Применить'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isFromDate ? _dateFrom ?? DateTime.now() : _dateTo ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _dateFrom = picked;
          _dateFromController.text = DateFormat('dd.MM.yyyy').format(picked);
        } else {
          _dateTo = picked;
          _dateToController.text = DateFormat('dd.MM.yyyy').format(picked);
        }
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _dateFromController.clear();
      _dateToController.clear();
      _quantityFromController.clear();
      _quantityToController.clear();
      _priceFromController.clear();
      _priceToController.clear();
      _dateFrom = null;
      _dateTo = null;
      _selectedStatuses.clear();
    });
  }

  void _applyFilters() {
    final filters = SearchModel(
      filters: SearchFiltersModel(
        projectId: widget.projectId,
        query: _searchController.text.isEmpty ? null : _searchController.text,
        dateFrom: _dateFrom,
        dateTo: _dateTo,
        deliveryStatus: _selectedStatuses.isEmpty ? null : _selectedStatuses,
      ),
    );

    context.read<BlocDeliveries>().add(ApplyFilters(filters));
    Navigator.of(context).pop();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _dateFromController.dispose();
    _dateToController.dispose();
    _quantityFromController.dispose();
    _quantityToController.dispose();
    _priceFromController.dispose();
    _priceToController.dispose();
    super.dispose();
  }
}
