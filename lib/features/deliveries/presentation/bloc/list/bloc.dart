import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/deliveries/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';

part 'bloc.freezed.dart';
part 'events.dart';
part 'state.dart';

class BlocDeliveries extends Bloc<BlocDeliveriesEvents, BlocDeliveriesState> {
  static const String _columnVisibilityKey = 'deliveries_column_visibility';

  BlocDeliveries()
      : super(
          BlocDeliveriesState(
            selecting: false,
            selectedDeliveries: {},
            deliveries: [],
            columnVisibility: _getDefaultColumnVisibility(),
          ),
        ) {
    _loadColumnVisibility();

    on<SetSelectedDeliveries>((event, emit) {
      emit(state.copyWith(selectedDeliveries: event.selectedDeliveries));
    });

    on<SetSelecting>((event, emit) {
      emit(state.copyWith(selecting: event.selecting));
    });

    on<ToggleSelecting>((event, emit) {
      emit(state.copyWith(selecting: !state.selecting));
    });

    on<ToggleDeliverySelection>((event, emit) {
      final updatedDeliveries =
          Map<String, DeliveryModel>.from(state.selectedDeliveries);
      final id = event.delivery.id;
      if (id != null) {
        if (updatedDeliveries.containsKey(id)) {
          updatedDeliveries.remove(id);
        } else {
          updatedDeliveries[id] = event.delivery;
        }
      }
      emit(state.copyWith(selectedDeliveries: updatedDeliveries));
    });

    on<ClearDeliverySelections>((event, emit) {
      emit(state.copyWith(selectedDeliveries: {}));
    });

    on<SetDeliveries>((event, emit) {
      emit(state.copyWith(deliveries: event.deliveries));
    });

    on<UpdateColumnVisibility>((event, emit) async {
      emit(state.copyWith(columnVisibility: event.columnVisibility));
      await _saveColumnVisibility(event.columnVisibility);
    });

    on<ApplyFilters>((event, emit) async {
      emit(state.copyWith(searchFilters: event.filters, isFiltersOpen: false));
      add(LoadDataWithFilters());
    });

    on<ClearFilters>((event, emit) {
      final projectId = state.searchFilters?.filters?.projectId ?? '';
      emit(state.copyWith(
        searchFilters: SearchModel(
          filters: SearchFiltersModel(
            projectId: projectId,
          ),
        ),
      ));
      add(LoadDataWithFilters());
    });

    on<ToggleFiltersPanel>((event, emit) {
      emit(state.copyWith(isFiltersOpen: !state.isFiltersOpen));
    });

    on<LoadDataWithFilters>((event, emit) async {
      emit(state.copyWith(isLoading: true, error: null));
      try {
        final filters = state.searchFilters;
        if (filters == null) {
          emit(state.copyWith(isLoading: false, error: 'No filters applied'));
          return;
        }

        final response = await DeliveriesRepository.search(filters);
        emit(state.copyWith(
          deliveries: response?.data?.items ?? [],
          totalItems: response?.data?.totalItems ?? 0,
          isLoading: false,
        ));
      } catch (e) {
        emit(state.copyWith(isLoading: false, error: e.toString()));
      }
    });

    on<RemoveFilter>((event, emit) {
      if (state.searchFilters == null) return;
      // TODO: Implement filter removal logic based on filterKey
      add(LoadDataWithFilters());
    });
  }

  static Map<String, bool> _getDefaultColumnVisibility() {
    final Map<String, bool> visibility = {};
    // Initialize all columns as visible by default
    for (int i = 0; i < 10; i++) {
      visibility[i.toString()] = true;
    }
    return visibility;
  }

  Future<void> _loadColumnVisibility() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final visibilityJson = prefs.getString(_columnVisibilityKey);
      if (visibilityJson != null) {
        final Map<String, dynamic> decoded = jsonDecode(visibilityJson);
        final Map<String, bool> visibility = decoded.map(
          (key, value) => MapEntry(key, value as bool),
        );
        add(UpdateColumnVisibility(visibility));
      }
    } catch (e) {
      // Handle error silently, use default visibility
    }
  }

  Future<void> _saveColumnVisibility(Map<String, bool> visibility) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final visibilityJson = jsonEncode(visibility);
      await prefs.setString(_columnVisibilityKey, visibilityJson);
    } catch (e) {
      // Handle error silently
    }
  }
}
