part of 'bloc.dart';

sealed class BlocDeliveriesEvents {}

final class SetSelectedDeliveries extends BlocDeliveriesEvents {
  final Map<String, DeliveryModel> selectedDeliveries;
  SetSelectedDeliveries(this.selectedDeliveries);
}

final class SetSelecting extends BlocDeliveriesEvents {
  final bool selecting;
  SetSelecting(this.selecting);
}

final class ToggleSelecting extends BlocDeliveriesEvents {
  ToggleSelecting();
}

final class ToggleDeliverySelection extends BlocDeliveriesEvents {
  final DeliveryModel delivery;
  ToggleDeliverySelection(this.delivery);
}

final class ClearDeliverySelections extends BlocDeliveriesEvents {
  ClearDeliverySelections();
}

final class SetDeliveries extends BlocDeliveriesEvents {
  final List<DeliveryModel> deliveries;
  SetDeliveries(this.deliveries);
}

final class UpdateColumnVisibility extends BlocDeliveriesEvents {
  final Map<String, bool> columnVisibility;
  UpdateColumnVisibility(this.columnVisibility);
}

final class ApplyFilters extends BlocDeliveriesEvents {
  final SearchModel filters;
  ApplyFilters(this.filters);
}

final class ClearFilters extends BlocDeliveriesEvents {}

final class ToggleFiltersPanel extends BlocDeliveriesEvents {}

final class LoadDataWithFilters extends BlocDeliveriesEvents {
  final int? offset;
  final int? limit;
  LoadDataWithFilters({this.offset, this.limit});
}

final class RemoveFilter extends BlocDeliveriesEvents {
  final String filterKey;
  RemoveFilter(this.filterKey);
}
