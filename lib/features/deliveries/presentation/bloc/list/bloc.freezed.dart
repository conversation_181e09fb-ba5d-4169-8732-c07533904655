// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BlocDeliveriesState {
  bool get selecting => throw _privateConstructorUsedError;
  Map<String, DeliveryModel> get selectedDeliveries =>
      throw _privateConstructorUsedError;
  List<DeliveryModel> get deliveries => throw _privateConstructorUsedError;
  Map<String, bool> get columnVisibility => throw _privateConstructorUsedError;
  SearchModel? get searchFilters => throw _privateConstructorUsedError;
  bool get isFiltersOpen => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  int get totalItems => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of BlocDeliveriesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BlocDeliveriesStateCopyWith<BlocDeliveriesState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocDeliveriesStateCopyWith<$Res> {
  factory $BlocDeliveriesStateCopyWith(
          BlocDeliveriesState value, $Res Function(BlocDeliveriesState) then) =
      _$BlocDeliveriesStateCopyWithImpl<$Res, BlocDeliveriesState>;
  @useResult
  $Res call(
      {bool selecting,
      Map<String, DeliveryModel> selectedDeliveries,
      List<DeliveryModel> deliveries,
      Map<String, bool> columnVisibility,
      SearchModel? searchFilters,
      bool isFiltersOpen,
      bool isLoading,
      int totalItems,
      String? error});

  $SearchModelCopyWith<$Res>? get searchFilters;
}

/// @nodoc
class _$BlocDeliveriesStateCopyWithImpl<$Res, $Val extends BlocDeliveriesState>
    implements $BlocDeliveriesStateCopyWith<$Res> {
  _$BlocDeliveriesStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocDeliveriesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selecting = null,
    Object? selectedDeliveries = null,
    Object? deliveries = null,
    Object? columnVisibility = null,
    Object? searchFilters = freezed,
    Object? isFiltersOpen = null,
    Object? isLoading = null,
    Object? totalItems = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      selecting: null == selecting
          ? _value.selecting
          : selecting // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedDeliveries: null == selectedDeliveries
          ? _value.selectedDeliveries
          : selectedDeliveries // ignore: cast_nullable_to_non_nullable
              as Map<String, DeliveryModel>,
      deliveries: null == deliveries
          ? _value.deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<DeliveryModel>,
      columnVisibility: null == columnVisibility
          ? _value.columnVisibility
          : columnVisibility // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      searchFilters: freezed == searchFilters
          ? _value.searchFilters
          : searchFilters // ignore: cast_nullable_to_non_nullable
              as SearchModel?,
      isFiltersOpen: null == isFiltersOpen
          ? _value.isFiltersOpen
          : isFiltersOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of BlocDeliveriesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SearchModelCopyWith<$Res>? get searchFilters {
    if (_value.searchFilters == null) {
      return null;
    }

    return $SearchModelCopyWith<$Res>(_value.searchFilters!, (value) {
      return _then(_value.copyWith(searchFilters: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BlocDeliveriesStateImplCopyWith<$Res>
    implements $BlocDeliveriesStateCopyWith<$Res> {
  factory _$$BlocDeliveriesStateImplCopyWith(_$BlocDeliveriesStateImpl value,
          $Res Function(_$BlocDeliveriesStateImpl) then) =
      __$$BlocDeliveriesStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool selecting,
      Map<String, DeliveryModel> selectedDeliveries,
      List<DeliveryModel> deliveries,
      Map<String, bool> columnVisibility,
      SearchModel? searchFilters,
      bool isFiltersOpen,
      bool isLoading,
      int totalItems,
      String? error});

  @override
  $SearchModelCopyWith<$Res>? get searchFilters;
}

/// @nodoc
class __$$BlocDeliveriesStateImplCopyWithImpl<$Res>
    extends _$BlocDeliveriesStateCopyWithImpl<$Res, _$BlocDeliveriesStateImpl>
    implements _$$BlocDeliveriesStateImplCopyWith<$Res> {
  __$$BlocDeliveriesStateImplCopyWithImpl(_$BlocDeliveriesStateImpl _value,
      $Res Function(_$BlocDeliveriesStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocDeliveriesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selecting = null,
    Object? selectedDeliveries = null,
    Object? deliveries = null,
    Object? columnVisibility = null,
    Object? searchFilters = freezed,
    Object? isFiltersOpen = null,
    Object? isLoading = null,
    Object? totalItems = null,
    Object? error = freezed,
  }) {
    return _then(_$BlocDeliveriesStateImpl(
      selecting: null == selecting
          ? _value.selecting
          : selecting // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedDeliveries: null == selectedDeliveries
          ? _value._selectedDeliveries
          : selectedDeliveries // ignore: cast_nullable_to_non_nullable
              as Map<String, DeliveryModel>,
      deliveries: null == deliveries
          ? _value._deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<DeliveryModel>,
      columnVisibility: null == columnVisibility
          ? _value._columnVisibility
          : columnVisibility // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      searchFilters: freezed == searchFilters
          ? _value.searchFilters
          : searchFilters // ignore: cast_nullable_to_non_nullable
              as SearchModel?,
      isFiltersOpen: null == isFiltersOpen
          ? _value.isFiltersOpen
          : isFiltersOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$BlocDeliveriesStateImpl extends _BlocDeliveriesState {
  const _$BlocDeliveriesStateImpl(
      {this.selecting = false,
      final Map<String, DeliveryModel> selectedDeliveries = const {},
      final List<DeliveryModel> deliveries = const [],
      final Map<String, bool> columnVisibility = const {},
      this.searchFilters,
      this.isFiltersOpen = false,
      this.isLoading = false,
      this.totalItems = 0,
      this.error = null})
      : _selectedDeliveries = selectedDeliveries,
        _deliveries = deliveries,
        _columnVisibility = columnVisibility,
        super._();

  @override
  @JsonKey()
  final bool selecting;
  final Map<String, DeliveryModel> _selectedDeliveries;
  @override
  @JsonKey()
  Map<String, DeliveryModel> get selectedDeliveries {
    if (_selectedDeliveries is EqualUnmodifiableMapView)
      return _selectedDeliveries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_selectedDeliveries);
  }

  final List<DeliveryModel> _deliveries;
  @override
  @JsonKey()
  List<DeliveryModel> get deliveries {
    if (_deliveries is EqualUnmodifiableListView) return _deliveries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deliveries);
  }

  final Map<String, bool> _columnVisibility;
  @override
  @JsonKey()
  Map<String, bool> get columnVisibility {
    if (_columnVisibility is EqualUnmodifiableMapView) return _columnVisibility;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_columnVisibility);
  }

  @override
  final SearchModel? searchFilters;
  @override
  @JsonKey()
  final bool isFiltersOpen;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final int totalItems;
  @override
  @JsonKey()
  final String? error;

  @override
  String toString() {
    return 'BlocDeliveriesState(selecting: $selecting, selectedDeliveries: $selectedDeliveries, deliveries: $deliveries, columnVisibility: $columnVisibility, searchFilters: $searchFilters, isFiltersOpen: $isFiltersOpen, isLoading: $isLoading, totalItems: $totalItems, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlocDeliveriesStateImpl &&
            (identical(other.selecting, selecting) ||
                other.selecting == selecting) &&
            const DeepCollectionEquality()
                .equals(other._selectedDeliveries, _selectedDeliveries) &&
            const DeepCollectionEquality()
                .equals(other._deliveries, _deliveries) &&
            const DeepCollectionEquality()
                .equals(other._columnVisibility, _columnVisibility) &&
            (identical(other.searchFilters, searchFilters) ||
                other.searchFilters == searchFilters) &&
            (identical(other.isFiltersOpen, isFiltersOpen) ||
                other.isFiltersOpen == isFiltersOpen) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selecting,
      const DeepCollectionEquality().hash(_selectedDeliveries),
      const DeepCollectionEquality().hash(_deliveries),
      const DeepCollectionEquality().hash(_columnVisibility),
      searchFilters,
      isFiltersOpen,
      isLoading,
      totalItems,
      error);

  /// Create a copy of BlocDeliveriesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BlocDeliveriesStateImplCopyWith<_$BlocDeliveriesStateImpl> get copyWith =>
      __$$BlocDeliveriesStateImplCopyWithImpl<_$BlocDeliveriesStateImpl>(
          this, _$identity);
}

abstract class _BlocDeliveriesState extends BlocDeliveriesState {
  const factory _BlocDeliveriesState(
      {final bool selecting,
      final Map<String, DeliveryModel> selectedDeliveries,
      final List<DeliveryModel> deliveries,
      final Map<String, bool> columnVisibility,
      final SearchModel? searchFilters,
      final bool isFiltersOpen,
      final bool isLoading,
      final int totalItems,
      final String? error}) = _$BlocDeliveriesStateImpl;
  const _BlocDeliveriesState._() : super._();

  @override
  bool get selecting;
  @override
  Map<String, DeliveryModel> get selectedDeliveries;
  @override
  List<DeliveryModel> get deliveries;
  @override
  Map<String, bool> get columnVisibility;
  @override
  SearchModel? get searchFilters;
  @override
  bool get isFiltersOpen;
  @override
  bool get isLoading;
  @override
  int get totalItems;
  @override
  String? get error;

  /// Create a copy of BlocDeliveriesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BlocDeliveriesStateImplCopyWith<_$BlocDeliveriesStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
