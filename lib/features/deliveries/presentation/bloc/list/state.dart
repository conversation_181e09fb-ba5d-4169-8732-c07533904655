part of 'bloc.dart';

@freezed
class BlocDeliveriesState with _$BlocDeliveriesState {
  const BlocDeliveriesState._();
  const factory BlocDeliveriesState({
    @Default(false) bool selecting,
    @Default({}) Map<String, DeliveryModel> selectedDeliveries,
    @Default([]) List<DeliveryModel> deliveries,
    @Default({}) Map<String, bool> columnVisibility,
    SearchModel? searchFilters,
    @Default(false) bool isFiltersOpen,
    @Default(false) bool isLoading,
    @Default(0) int totalItems,
    @Default(null) String? error,
  }) = _BlocDeliveriesState;
}
