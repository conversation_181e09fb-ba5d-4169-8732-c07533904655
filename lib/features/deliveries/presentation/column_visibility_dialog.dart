import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/features/deliveries/domain/column.dart';
import 'package:sphere/features/deliveries/presentation/bloc/list/bloc.dart';
import 'package:sphere/shared/styles/fonts.dart';

class DeliveryColumnVisibilityDialog extends StatefulWidget {
  const DeliveryColumnVisibilityDialog({super.key});

  @override
  State<DeliveryColumnVisibilityDialog> createState() =>
      _DeliveryColumnVisibilityDialogState();
}

class _DeliveryColumnVisibilityDialogState
    extends State<DeliveryColumnVisibilityDialog> {
  late Map<String, bool> _columnVisibility;

  @override
  void initState() {
    super.initState();
    _columnVisibility = Map<String, bool>.from(
      context.read<BlocDeliveries>().state.columnVisibility,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 400,
        height: 500,
        padding: const EdgeInsets.all(0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Настройка колонок',
                    style: Fonts.titleMedium,
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),

            // Column list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16.0),
                itemCount: DeliveryTableData.columns.length,
                itemBuilder: (context, index) {
                  final column = DeliveryTableData.columns[index];
                  final isVisible = _columnVisibility[index.toString()] ?? true;

                  return CheckboxListTile(
                    title: Text(
                      column.name ?? 'Колонка ${index + 1}',
                      style: Fonts.bodyMedium,
                    ),
                    subtitle: column.description != null
                        ? Text(
                            column.description!,
                            style: Fonts.bodySmall.copyWith(
                              color: Colors.grey[600],
                            ),
                          )
                        : null,
                    value: isVisible,
                    onChanged: (value) {
                      setState(() {
                        _columnVisibility[index.toString()] = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                  );
                },
              ),
            ),

            // Footer buttons
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: _resetToDefault,
                      child: const Text('По умолчанию'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _applyChanges,
                      child: const Text('Применить'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _resetToDefault() {
    setState(() {
      _columnVisibility = {};
      for (int i = 0; i < DeliveryTableData.columns.length; i++) {
        _columnVisibility[i.toString()] = true;
      }
    });
  }

  void _applyChanges() {
    context
        .read<BlocDeliveries>()
        .add(UpdateColumnVisibility(_columnVisibility));
    Navigator.of(context).pop();
  }
}
