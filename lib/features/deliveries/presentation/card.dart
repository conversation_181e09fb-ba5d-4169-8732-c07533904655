import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/core/helpers/pick_file.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/deliveries/data/models/index.dart';
import 'package:sphere/features/deliveries/data/repositories/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class DeliveryCard extends StatelessWidget {
  const DeliveryCard({
    super.key,
    required this.data,
    required this.refresher,
  });

  final DeliveryModel data;
  final void Function() refresher;

  Color? getPlanColor(bool isDarkTheme) {
    if (data.expectedDate == null) return null;

    final duration =
        data.expectedDate!.difference(data.deliveryDate ?? DateTime.now());

    if (!duration.isNegative && duration.inDays <= 3) {
      if (data.deliveryDate != null) {
        return isDarkTheme ? AppColors.darkSuccess : AppColors.lightSuccess;
      }
      return isDarkTheme ? AppColors.darkWarning : AppColors.lightWarning;
    } else if (duration.isNegative) {
      return isDarkTheme ? AppColors.darkError : AppColors.lightError;
    }

    if (data.deliveryDate != null) {
      return isDarkTheme ? AppColors.darkSuccess : AppColors.lightSuccess;
    }
    return null;
  }

  Future<void> _showAcceptDeliveryDialog(BuildContext context) async {
    final commentController = TextEditingController();
    List<File> files = [];
    bool isLoading = false;

    final result = await showDialog(
      context: context,
      builder: (context) {
        final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: isDarkTheme
                  ? AppColors.darkBackground
                  : AppColors.lightBackground,
              title: const Text('Принять поставку'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: commentController,
                      decoration: const InputDecoration(
                        labelText: 'Комментарий',
                        border: OutlineInputBorder(),
                      ),
                      minLines: 1,
                      maxLines: 4,
                    ),
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed: isLoading
                          ? null
                          : () async {
                              final file = await pickFile();
                              if (file != null) {
                                setState(() {
                                  files.add(file);
                                });
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                      content: Text(
                                          'Добавлен файл: ${file.path.split('/').last}')),
                                );
                              }
                            },
                      child: const Text('Добавить файл'),
                    ),
                    if (files.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Выбрано файлов: ${files.length}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      ...files.asMap().entries.map((entry) {
                        final index = entry.key;
                        final file = entry.value;

                        return ListTile(
                          leading: const Icon(Icons.insert_drive_file),
                          title: Text(file.path.split('/').last),
                          trailing: IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: isLoading
                                ? null
                                : () {
                                    setState(() {
                                      files.removeAt(index);
                                    });
                                  },
                          ),
                        );
                      })
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                          if (commentController.text.isEmpty && files.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content:
                                      Text('Добавьте комментарий или файлы')),
                            );
                            return;
                          }

                          setState(() => isLoading = true);
                          try {
                            await DeliveriesRepository.acceptDelivery(
                              deliveryId: data.id,
                              comment: commentController.text,
                              files: files,
                            );
                            Navigator.pop(context, true);
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text('Ошибка: ${e.toString()}')),
                            );
                          } finally {
                            setState(() => isLoading = false);
                          }
                        },
                  child: isLoading
                      ? const CircularProgressIndicator()
                      : const Text('Подтвердить'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Поставка успешно принята')),
      );
    }
  }

  Future<void> _showQualityControlDialog(BuildContext context) async {
    final List<QCItemModel> qcItems = data.items
            ?.map((item) => QCItemModel(
                  provisionItemId: item.provisionItemId,
                  quantityApproved: item.quantity,
                  qcComment: '',
                ))
            .toList() ??
        [];

    List<File> files = [];
    bool isLoading = false;

    final result = await showDialog(
      context: context,
      builder: (context) {
        final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: isDarkTheme
                  ? AppColors.darkBackground
                  : AppColors.lightBackground,
              title: const Text('Контроль качества'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ...qcItems.asMap().entries.map((entry) {
                      final index = entry.key;
                      final item = entry.value;
                      final material = data.items![index];
                      final quantityController = TextEditingController(
                        text: item.quantityApproved?.toString() ?? '',
                      );
                      final commentController = TextEditingController(
                        text: item.qcComment ?? '',
                      );

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (index > 0) const Divider(height: 24),
                          Text(
                            material.materialName ?? 'Материал',
                            style: Fonts.bodyMedium,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: quantityController,
                                  decoration: const InputDecoration(
                                    labelText: 'Кол-во принято',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    qcItems[index] = qcItems[index].copyWith(
                                      quantityApproved: double.tryParse(value),
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                '/ ${material.quantity} ${material.unitType?.getName()}',
                                style: Fonts.bodySmall,
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: commentController,
                            decoration: const InputDecoration(
                              labelText: 'Комментарий',
                              border: OutlineInputBorder(),
                            ),
                            minLines: 1,
                            maxLines: 2,
                            onChanged: (value) {
                              qcItems[index] = qcItems[index].copyWith(
                                qcComment: value,
                              );
                            },
                          ),
                        ],
                      );
                    }),
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed: isLoading
                          ? null
                          : () async {
                              final file = await pickFile();
                              if (file != null) {
                                setState(() {
                                  files.add(file);
                                });
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                      content: Text(
                                          'Добавлен файл: ${file.path.split('/').last}')),
                                );
                              }
                            },
                      child: const Text('Добавить файл'),
                    ),
                    if (files.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Выбрано файлов: ${files.length}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      ...files.asMap().entries.map((entry) {
                        final index = entry.key;
                        final file = entry.value;

                        return ListTile(
                          leading: const Icon(Icons.insert_drive_file),
                          title: Text(file.path.split('/').last),
                          trailing: IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: isLoading
                                ? null
                                : () {
                                    setState(() {
                                      files.removeAt(index);
                                    });
                                  },
                          ),
                        );
                      })
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isLoading
                      ? null
                      : () {
                          Navigator.pop(context);
                        },
                  child: const Text('Отмена'),
                ),
                TextButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                          jsonEncode(
                            qcItems.map((qcItem) {
                              print(qcItem.toJson());
                              return qcItem.toJson();
                            }).toList(),
                          );
                          // Validate quantities
                          for (final item in qcItems) {
                            if (item.quantityApproved == null) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text(
                                        'Укажите количество для всех позиций')),
                              );
                              return;
                            }
                          }

                          setState(() => isLoading = true);
                          try {
                            await DeliveriesRepository.qualityControl(
                              deliveryId: data.id,
                              qcItems: qcItems,
                              files: files,
                            );
                            Navigator.pop(context, true);
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text('Ошибка: ${e.toString()}')),
                            );
                          } finally {
                            setState(() => isLoading = false);
                          }
                        },
                  child: isLoading
                      ? const CircularProgressIndicator()
                      : const Text('Подтвердить'),
                ),
              ],
            );
          },
        );
      },
    );

    if (result == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Контроль качества завершен')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      onSecondaryTapDown: (details) {
        CustomDropdownMenu.instance.hide();
        CustomDropdownMenu.instance.show(
            context: context,
            items: [
              if (data.status == DeliveryStatus.pending ||
                  data.status == DeliveryStatus.qcRejected)
                CustomDropdownMenuItem(
                  icon: Assets.icons.warehouse,
                  text: 'Принять поставку',
                  onTap: () {
                    CustomDropdownMenu.instance.hide();
                    _showAcceptDeliveryDialog(context);
                  },
                ),
              // if (data.status != DeliveryStatus.pending)
              //   CustomDropdownMenuItem(
              //     icon: Assets.icons.description,
              //     text: 'Документы поставки',
              //     onTap: () {
              //       CustomDropdownMenu.instance.hide();
              //     },
              //   ),
              if (data.status == DeliveryStatus.qcPending ||
                  data.status == DeliveryStatus.partiallyAccepted)
                CustomDropdownMenuItem(
                  icon: Assets.icons.description,
                  text: 'Контроль качества',
                  onTap: () {
                    CustomDropdownMenu.instance.hide();
                    _showQualityControlDialog(context);
                  },
                ),
            ],
            position: details.globalPosition);
      },
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(children: [
          SVG(Assets.icons.event, color: getPlanColor(isDarkTheme)),
          const SizedBox(width: 8.0),
          Text(
            '-> ${getDateString(data.expectedDate ?? DateTime.now())}',
            style: Fonts.labelMedium.merge(TextStyle(
              color: getPlanColor(isDarkTheme),
            )),
          ),
          // const Spacer(),
          if (data.deliveryDate != null) const SizedBox(width: 8.0),
          if (data.deliveryDate != null) SVG(Assets.icons.warehouse),
          if (data.deliveryDate != null) const SizedBox(width: 8.0),
          if (data.deliveryDate != null)
            Text(
              '<- ${getDateString(data.deliveryDate!)}',
              style: Fonts.labelMedium,
            ),
          if (data.status != null) SizedBox(width: 12.0),
          if (data.status != null)
            Text(
              data.status!.getName(),
              style: Fonts.labelSmall
                  .merge(TextStyle(color: getPlanColor(isDarkTheme))),
            ),
        ]),
        const SizedBox(height: 12.0),
        GhostButton(
          onTap: () {},
          child: Text(
            data.supplier?.name ?? 'Client',
            style: Fonts.labelSmall.merge(TextStyle(
              color: isDarkTheme
                  ? AppColors.darkSecondary
                  : AppColors.lightSecondary,
            )),
          ),
        ),
        const SizedBox(height: 12.0),
        Row(
          children: [
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: data.items?.length ?? 0,
                itemBuilder: (context, index) {
                  final material = data.items![index];
                  final materialName = material.materialName;
                  final materialQuantity = material.quantity;

                  return Row(children: [
                    Text(
                      '${index + 1}. $materialName',
                      style: Fonts.bodySmall,
                    ),
                    const SizedBox(width: 8.0),
                    if (material.quantityApproved != null)
                      Text(
                        '${material.quantityApproved} /',
                        style: Fonts.bodySmall.merge(TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        )),
                      ),
                    if (material.quantityApproved != null)
                      const SizedBox(width: 4),
                    Text(
                      '$materialQuantity ${material.unitType?.getName()}',
                      style: Fonts.bodySmall.merge(TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                      )),
                    ),
                  ]);
                },
                separatorBuilder: (context, index) {
                  return const Divider(height: 1.0);
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12.0),
      ]),
    );
  }
}
