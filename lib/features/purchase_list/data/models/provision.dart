// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/files/data/models/file.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/supply.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/user/data/models/user.dart';

part 'provision.freezed.dart';
part 'provision.g.dart';

// product
@freezed
class ProvisionProductModel with _$ProvisionProductModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionProductModel({
    // String? provisionItemId,
    String? uniqueId,
    String? productId,
    ProductModel? product,
    String? productName,
    NomenclatureModel? material,
    ProductModel? parentProduct,
    // UnitType? unitType,
    double? quantity,
    double? totalMass,
    double? totalQuantity,
    List<ParametersFeatureType>? features,
    UserModel? responsibleUser,
    String? responsibleUserName,
    SupplyStatus? status,
    ContractModel? contract,
    List<LotModel>? lots,
    ClientModel? supplier,
    DateTime? deliveryDate,
    double? unitCost,
    double? totalCost,
    ProductModel? parent,
    ProvisionsFilter? filterType,
    TaskProgressModel? task,

    // double? totalQuantity,
    // List<AdjustmentModel>? adjustments,
    // ParametersModel? parameters,
  }) = _ProvisionProductModel;

  factory ProvisionProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionProductModelFromJson(json);
}

enum SupplyStatus {
  @JsonValue('not_started')
  notStarted,
  confirmed,
  delivered,
  @JsonValue('qc_passed')
  qcPassed,
  @JsonValue('pending_approval')
  pendingApproval,
  completed;

  String getName() {
    switch (this) {
      case SupplyStatus.notStarted:
        return 'Не начато';
      case SupplyStatus.confirmed:
        return 'Законтрактовано';
      case SupplyStatus.delivered:
        return 'Доставлено';
      case SupplyStatus.qcPassed:
        return 'Пройден ТК';
      case SupplyStatus.completed:
        return 'Завершено';
      case SupplyStatus.pendingApproval:
        return 'На согласовании';
    }
  }
}

@freezed
class ContractModel with _$ContractModel {
  @JsonSerializable(includeIfNull: false)
  factory ContractModel({
    @JsonKey(name: '_id') String? id,
    String? projectId,
    String? contractNumber,
    DateTime? contractDate,
    DateTime? plannedDeliveryDate,
    String? supplierId,
    ClientModel? supplier,
    List<String>? productIds,
    double? contractPrice,
    dynamic productDetails,
    List<String>? documentIds,
    SupplyStatus? status,
    String? notes,
    String? createdBy,
    String? updatedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ContractModel;

  factory ContractModel.fromJson(Map<String, dynamic> json) =>
      _$ContractModelFromJson(json);
}

@freezed
class LotModel with _$LotModel {
  @JsonSerializable(includeIfNull: false)
  factory LotModel({
    @JsonKey(name: '_id') String? id,
    String? projectId,
    int? lotNumber,
    String? lotName,
    List<String>? productIds,
    String? createdBy,
    DateTime? plannedTenderCompletionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _LotModel;
  factory LotModel.fromJson(Map<String, dynamic> json) =>
      _$LotModelFromJson(json);
}

// material
@freezed
class ProvisionMaterialModel with _$ProvisionMaterialModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionMaterialModel({
    String? materialId,
    String? materialName,
    UnitType? unitType,
    double? quantity,
    String? materialRequirements,
    double? totalQuantity,
    List<ProvisionItemModel>? products,
    // other
    double? storageQuantity,
    double? totalPrice,
    List<String>? operations,
    List<ProvisionDeliveryModel>? deliveries,
  }) = _ProvisionMaterialModel;

  factory ProvisionMaterialModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionMaterialModelFromJson(json);
}

@freezed
class ProvisionDeliveryModel with _$ProvisionDeliveryModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionDeliveryModel({
    @JsonKey(name: '_id') String? id,
    // SupplyState? status,
    DateTime? expectedDate,
    List<DateTime>? deliveryDates,
    List<SupplyInputModel>? links,
  }) = _ProvisionDeliviresModel;

  factory ProvisionDeliveryModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionDeliveryModelFromJson(json);
}

@freezed
class ProvisionDeliveryLinkModel with _$ProvisionDeliveryLinkModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionDeliveryLinkModel({
    String? provisionItemId,
    double? quantity,
  }) = _ProvisionDeliveryLinkModel;

  factory ProvisionDeliveryLinkModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionDeliveryLinkModelFromJson(json);
}

// allocated materials
@freezed
class ProvisionLotModel with _$ProvisionLotModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionLotModel({
    @JsonKey(name: '_id') String? id,
    ProvisionsFilter? filterType,
    String? provisionName,
    ProvisionStatus? provisionStatus,
    ClientModel? supplier,
    List<ProvisionItemModel>? provisionItems,
    List<ProvisionContractItemModel>? contractItems,
    // List<ProvisionMaterialModel>? materials,
    String? contractNumber,
    DateTime? contractDate,
    DateTime? contractStartDate,
    DateTime? contractEndDate,
    List<DateTime>? contractDeliveryDates,
    double? contractPrice,
    FileModel? document,
    double? lotPrice,
    List<ProvisionPaymentDetailsModel>? paymentDetails,
    List<DeliveryModel>? deliveries,
    List<DeliveryGroupModel>? deliveryGroups,
  }) = _ProvisionLotModel;

  factory ProvisionLotModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionLotModelFromJson(json);
}

@freezed
class ProvisionItemModel with _$ProvisionItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionItemModel({
    ProductModel? product,
    NomenclatureModel? material,
    String? materialGroupId,
    String? uniqueId,
    String? provisionId,
    // String? id,
    @JsonKey(name: '_id') String? id,
    // String? materialRquirements,
    ParametersFeatureType? featureType,
    double? cooperationQuantity,
    double? deliveryQuantity,
    double? totalQuantity,
    double? multipliedQuantity,
    List<AdjustmentModel>? adjustments,
    List<ProvisionPaymentDetailsModel>? paymentDetails,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ProvisionItem;

  factory ProvisionItemModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionItemModelFromJson(json);
}

@freezed
class ProvisionContractItemModel with _$ProvisionContractItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionContractItemModel({
    String? materialId,
    String? materialRequirements,
    String? productId,
    ParametersFeatureType? featureType,
    bool? needMaterial,
    double? price,
    double? quantity,
  }) = _ProvisionContractItemModel;

  factory ProvisionContractItemModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionContractItemModelFromJson(json);
}

@freezed
class ProvisionPaymentDetailsModel with _$ProvisionPaymentDetailsModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionPaymentDetailsModel({
    DateTime? paymentDate,
    double? amount,
    String? description,
  }) = _ProvisionPaymentDetails;

  factory ProvisionPaymentDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionPaymentDetailsModelFromJson(json);
}

// all materials
@freezed
class ProvisionsListModel with _$ProvisionsListModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionsListModel({
    List<ProvisionLotModel>? lots,
    List<ProvisionItemModel>? unallocatedItems,
  }) = _ProvisionsListModel;

  factory ProvisionsListModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionsListModelFromJson(json);
}

// adjustments
@freezed
class AdjustmentModel with _$AdjustmentModel {
  @JsonSerializable(includeIfNull: false)
  const factory AdjustmentModel({
    AdjustmentType? type,
    DateTime? date,
    AdjustmentDetailsModel? details,
  }) = _AdjustmentModel;

  factory AdjustmentModel.fromJson(Map<String, dynamic> json) =>
      _$AdjustmentModelFromJson(json);
}

@freezed
class AdjustmentDetailsModel with _$AdjustmentDetailsModel {
  @JsonSerializable(includeIfNull: false)
  const factory AdjustmentDetailsModel({
    String? description,
    String? productId,
    String? productName,
    double? oldQuantity,
    double? newQuantity,
    UnitType? unitType,
    String? oldMaterialId,
    String? oldMaterialName,
    String? newMaterialId,
    String? newMaterialName,
    String? assemblyId,
    String? assemblyName,
    double? oldAssemblyQuantity,
    double? newAssemblyQuantity,
    double? quantityAdjusted,
    bool? removedFromLot,
  }) = _AdjustmentDetailsModel;

  factory AdjustmentDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$AdjustmentDetailsModelFromJson(json);
}

enum AdjustmentType {
  @JsonValue('quantity_decreased')
  quantityDecreased,
  @JsonValue('quantity_depleted')
  quantityDepleted,
  @JsonValue('product_deleted')
  productDeleted,
  @JsonValue('material_changed')
  materialChanged,
  @JsonValue('assembly_quantity_changed')
  assemblyQuantityChanged;

  String getName() {
    switch (this) {
      case AdjustmentType.quantityDecreased:
        return 'Количество уменьшено';
      case AdjustmentType.quantityDepleted:
        return 'Количество истощено';
      case AdjustmentType.productDeleted:
        return 'Продукт удалён';
      case AdjustmentType.materialChanged:
        return 'Материал изменён';
      case AdjustmentType.assemblyQuantityChanged:
        return 'Изменено количество сборок';
    }
  }
}

// material status
enum ProvisionStatus {
  @JsonValue('new')
  newStatus,
  direction,
  contracted,
  @JsonValue('needs_otk')
  needsOtk,
  stored;

  String getName() {
    switch (this) {
      case ProvisionStatus.newStatus:
        return 'Новый';
      case ProvisionStatus.direction:
        return 'Направлен на конкурсную закупку';
      case ProvisionStatus.contracted:
        return 'Законтрактовано';
      case ProvisionStatus.needsOtk:
        return 'Необходмо ОТК';
      case ProvisionStatus.stored:
        return 'На хранении';
    }
  }
}
