// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'supply.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SupplyModel _$SupplyModelFromJson(Map<String, dynamic> json) {
  return _SupplyModel.fromJson(json);
}

/// @nodoc
mixin _$SupplyModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  List<SupplyInputModel>? get forInputs => throw _privateConstructorUsedError;
  SupplyState? get state => throw _privateConstructorUsedError;
  DateTime? get supplyDate => throw _privateConstructorUsedError;
  List<SupplyPaymentModel>? get payments => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;

  /// Serializes this SupplyModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SupplyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SupplyModelCopyWith<SupplyModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SupplyModelCopyWith<$Res> {
  factory $SupplyModelCopyWith(
          SupplyModel value, $Res Function(SupplyModel) then) =
      _$SupplyModelCopyWithImpl<$Res, SupplyModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      List<SupplyInputModel>? forInputs,
      SupplyState? state,
      DateTime? supplyDate,
      List<SupplyPaymentModel>? payments,
      double? quantity});
}

/// @nodoc
class _$SupplyModelCopyWithImpl<$Res, $Val extends SupplyModel>
    implements $SupplyModelCopyWith<$Res> {
  _$SupplyModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SupplyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? forInputs = freezed,
    Object? state = freezed,
    Object? supplyDate = freezed,
    Object? payments = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      forInputs: freezed == forInputs
          ? _value.forInputs
          : forInputs // ignore: cast_nullable_to_non_nullable
              as List<SupplyInputModel>?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as SupplyState?,
      supplyDate: freezed == supplyDate
          ? _value.supplyDate
          : supplyDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      payments: freezed == payments
          ? _value.payments
          : payments // ignore: cast_nullable_to_non_nullable
              as List<SupplyPaymentModel>?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SupplyModelImplCopyWith<$Res>
    implements $SupplyModelCopyWith<$Res> {
  factory _$$SupplyModelImplCopyWith(
          _$SupplyModelImpl value, $Res Function(_$SupplyModelImpl) then) =
      __$$SupplyModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      List<SupplyInputModel>? forInputs,
      SupplyState? state,
      DateTime? supplyDate,
      List<SupplyPaymentModel>? payments,
      double? quantity});
}

/// @nodoc
class __$$SupplyModelImplCopyWithImpl<$Res>
    extends _$SupplyModelCopyWithImpl<$Res, _$SupplyModelImpl>
    implements _$$SupplyModelImplCopyWith<$Res> {
  __$$SupplyModelImplCopyWithImpl(
      _$SupplyModelImpl _value, $Res Function(_$SupplyModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SupplyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? forInputs = freezed,
    Object? state = freezed,
    Object? supplyDate = freezed,
    Object? payments = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_$SupplyModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      forInputs: freezed == forInputs
          ? _value._forInputs
          : forInputs // ignore: cast_nullable_to_non_nullable
              as List<SupplyInputModel>?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as SupplyState?,
      supplyDate: freezed == supplyDate
          ? _value.supplyDate
          : supplyDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      payments: freezed == payments
          ? _value._payments
          : payments // ignore: cast_nullable_to_non_nullable
              as List<SupplyPaymentModel>?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SupplyModelImpl extends _SupplyModel {
  const _$SupplyModelImpl(
      {@JsonKey(name: '_id') this.id,
      final List<SupplyInputModel>? forInputs,
      this.state,
      this.supplyDate,
      final List<SupplyPaymentModel>? payments,
      this.quantity})
      : _forInputs = forInputs,
        _payments = payments,
        super._();

  factory _$SupplyModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SupplyModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  final List<SupplyInputModel>? _forInputs;
  @override
  List<SupplyInputModel>? get forInputs {
    final value = _forInputs;
    if (value == null) return null;
    if (_forInputs is EqualUnmodifiableListView) return _forInputs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final SupplyState? state;
  @override
  final DateTime? supplyDate;
  final List<SupplyPaymentModel>? _payments;
  @override
  List<SupplyPaymentModel>? get payments {
    final value = _payments;
    if (value == null) return null;
    if (_payments is EqualUnmodifiableListView) return _payments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? quantity;

  @override
  String toString() {
    return 'SupplyModel(id: $id, forInputs: $forInputs, state: $state, supplyDate: $supplyDate, payments: $payments, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SupplyModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other._forInputs, _forInputs) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.supplyDate, supplyDate) ||
                other.supplyDate == supplyDate) &&
            const DeepCollectionEquality().equals(other._payments, _payments) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      const DeepCollectionEquality().hash(_forInputs),
      state,
      supplyDate,
      const DeepCollectionEquality().hash(_payments),
      quantity);

  /// Create a copy of SupplyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SupplyModelImplCopyWith<_$SupplyModelImpl> get copyWith =>
      __$$SupplyModelImplCopyWithImpl<_$SupplyModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SupplyModelImplToJson(
      this,
    );
  }
}

abstract class _SupplyModel extends SupplyModel {
  const factory _SupplyModel(
      {@JsonKey(name: '_id') final String? id,
      final List<SupplyInputModel>? forInputs,
      final SupplyState? state,
      final DateTime? supplyDate,
      final List<SupplyPaymentModel>? payments,
      final double? quantity}) = _$SupplyModelImpl;
  const _SupplyModel._() : super._();

  factory _SupplyModel.fromJson(Map<String, dynamic> json) =
      _$SupplyModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  List<SupplyInputModel>? get forInputs;
  @override
  SupplyState? get state;
  @override
  DateTime? get supplyDate;
  @override
  List<SupplyPaymentModel>? get payments;
  @override
  double? get quantity;

  /// Create a copy of SupplyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SupplyModelImplCopyWith<_$SupplyModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SupplyPaymentModel _$SupplyPaymentModelFromJson(Map<String, dynamic> json) {
  return _SupplyPaymentModel.fromJson(json);
}

/// @nodoc
mixin _$SupplyPaymentModel {
  DateTime? get date => throw _privateConstructorUsedError;
  double? get cost => throw _privateConstructorUsedError;
  SupplyPaymentState? get state => throw _privateConstructorUsedError;

  /// Serializes this SupplyPaymentModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SupplyPaymentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SupplyPaymentModelCopyWith<SupplyPaymentModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SupplyPaymentModelCopyWith<$Res> {
  factory $SupplyPaymentModelCopyWith(
          SupplyPaymentModel value, $Res Function(SupplyPaymentModel) then) =
      _$SupplyPaymentModelCopyWithImpl<$Res, SupplyPaymentModel>;
  @useResult
  $Res call({DateTime? date, double? cost, SupplyPaymentState? state});
}

/// @nodoc
class _$SupplyPaymentModelCopyWithImpl<$Res, $Val extends SupplyPaymentModel>
    implements $SupplyPaymentModelCopyWith<$Res> {
  _$SupplyPaymentModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SupplyPaymentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? cost = freezed,
    Object? state = freezed,
  }) {
    return _then(_value.copyWith(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as double?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as SupplyPaymentState?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SupplyPaymentModelImplCopyWith<$Res>
    implements $SupplyPaymentModelCopyWith<$Res> {
  factory _$$SupplyPaymentModelImplCopyWith(_$SupplyPaymentModelImpl value,
          $Res Function(_$SupplyPaymentModelImpl) then) =
      __$$SupplyPaymentModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime? date, double? cost, SupplyPaymentState? state});
}

/// @nodoc
class __$$SupplyPaymentModelImplCopyWithImpl<$Res>
    extends _$SupplyPaymentModelCopyWithImpl<$Res, _$SupplyPaymentModelImpl>
    implements _$$SupplyPaymentModelImplCopyWith<$Res> {
  __$$SupplyPaymentModelImplCopyWithImpl(_$SupplyPaymentModelImpl _value,
      $Res Function(_$SupplyPaymentModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SupplyPaymentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? cost = freezed,
    Object? state = freezed,
  }) {
    return _then(_$SupplyPaymentModelImpl(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cost: freezed == cost
          ? _value.cost
          : cost // ignore: cast_nullable_to_non_nullable
              as double?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as SupplyPaymentState?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SupplyPaymentModelImpl extends _SupplyPaymentModel {
  const _$SupplyPaymentModelImpl({this.date, this.cost, this.state})
      : super._();

  factory _$SupplyPaymentModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SupplyPaymentModelImplFromJson(json);

  @override
  final DateTime? date;
  @override
  final double? cost;
  @override
  final SupplyPaymentState? state;

  @override
  String toString() {
    return 'SupplyPaymentModel(date: $date, cost: $cost, state: $state)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SupplyPaymentModelImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.cost, cost) || other.cost == cost) &&
            (identical(other.state, state) || other.state == state));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, date, cost, state);

  /// Create a copy of SupplyPaymentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SupplyPaymentModelImplCopyWith<_$SupplyPaymentModelImpl> get copyWith =>
      __$$SupplyPaymentModelImplCopyWithImpl<_$SupplyPaymentModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SupplyPaymentModelImplToJson(
      this,
    );
  }
}

abstract class _SupplyPaymentModel extends SupplyPaymentModel {
  const factory _SupplyPaymentModel(
      {final DateTime? date,
      final double? cost,
      final SupplyPaymentState? state}) = _$SupplyPaymentModelImpl;
  const _SupplyPaymentModel._() : super._();

  factory _SupplyPaymentModel.fromJson(Map<String, dynamic> json) =
      _$SupplyPaymentModelImpl.fromJson;

  @override
  DateTime? get date;
  @override
  double? get cost;
  @override
  SupplyPaymentState? get state;

  /// Create a copy of SupplyPaymentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SupplyPaymentModelImplCopyWith<_$SupplyPaymentModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SupplyInputModel _$SupplyInputModelFromJson(Map<String, dynamic> json) {
  return _SupplyInputModel.fromJson(json);
}

/// @nodoc
mixin _$SupplyInputModel {
  String? get provisionItemId => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;

  /// Serializes this SupplyInputModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SupplyInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SupplyInputModelCopyWith<SupplyInputModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SupplyInputModelCopyWith<$Res> {
  factory $SupplyInputModelCopyWith(
          SupplyInputModel value, $Res Function(SupplyInputModel) then) =
      _$SupplyInputModelCopyWithImpl<$Res, SupplyInputModel>;
  @useResult
  $Res call({String? provisionItemId, double? quantity});
}

/// @nodoc
class _$SupplyInputModelCopyWithImpl<$Res, $Val extends SupplyInputModel>
    implements $SupplyInputModelCopyWith<$Res> {
  _$SupplyInputModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SupplyInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionItemId = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_value.copyWith(
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SupplyInputModelImplCopyWith<$Res>
    implements $SupplyInputModelCopyWith<$Res> {
  factory _$$SupplyInputModelImplCopyWith(_$SupplyInputModelImpl value,
          $Res Function(_$SupplyInputModelImpl) then) =
      __$$SupplyInputModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? provisionItemId, double? quantity});
}

/// @nodoc
class __$$SupplyInputModelImplCopyWithImpl<$Res>
    extends _$SupplyInputModelCopyWithImpl<$Res, _$SupplyInputModelImpl>
    implements _$$SupplyInputModelImplCopyWith<$Res> {
  __$$SupplyInputModelImplCopyWithImpl(_$SupplyInputModelImpl _value,
      $Res Function(_$SupplyInputModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SupplyInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionItemId = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_$SupplyInputModelImpl(
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SupplyInputModelImpl extends _SupplyInputModel {
  const _$SupplyInputModelImpl({this.provisionItemId, this.quantity})
      : super._();

  factory _$SupplyInputModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SupplyInputModelImplFromJson(json);

  @override
  final String? provisionItemId;
  @override
  final double? quantity;

  @override
  String toString() {
    return 'SupplyInputModel(provisionItemId: $provisionItemId, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SupplyInputModelImpl &&
            (identical(other.provisionItemId, provisionItemId) ||
                other.provisionItemId == provisionItemId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, provisionItemId, quantity);

  /// Create a copy of SupplyInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SupplyInputModelImplCopyWith<_$SupplyInputModelImpl> get copyWith =>
      __$$SupplyInputModelImplCopyWithImpl<_$SupplyInputModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SupplyInputModelImplToJson(
      this,
    );
  }
}

abstract class _SupplyInputModel extends SupplyInputModel {
  const factory _SupplyInputModel(
      {final String? provisionItemId,
      final double? quantity}) = _$SupplyInputModelImpl;
  const _SupplyInputModel._() : super._();

  factory _SupplyInputModel.fromJson(Map<String, dynamic> json) =
      _$SupplyInputModelImpl.fromJson;

  @override
  String? get provisionItemId;
  @override
  double? get quantity;

  /// Create a copy of SupplyInputModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SupplyInputModelImplCopyWith<_$SupplyInputModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
