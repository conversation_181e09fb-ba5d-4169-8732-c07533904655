// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'provision.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProvisionProductModel _$ProvisionProductModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionProductModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionProductModel {
// String? provisionItemId,
  String? get uniqueId => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  ProductModel? get product => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  NomenclatureModel? get material => throw _privateConstructorUsedError;
  ProductModel? get parentProduct =>
      throw _privateConstructorUsedError; // UnitType? unitType,
  double? get quantity => throw _privateConstructorUsedError;
  double? get totalMass => throw _privateConstructorUsedError;
  double? get totalQuantity => throw _privateConstructorUsedError;
  List<ParametersFeatureType>? get features =>
      throw _privateConstructorUsedError;
  UserModel? get responsibleUser => throw _privateConstructorUsedError;
  String? get responsibleUserName => throw _privateConstructorUsedError;
  SupplyStatus? get status => throw _privateConstructorUsedError;
  ContractModel? get contract => throw _privateConstructorUsedError;
  List<LotModel>? get lots => throw _privateConstructorUsedError;
  ClientModel? get supplier => throw _privateConstructorUsedError;
  DateTime? get deliveryDate => throw _privateConstructorUsedError;
  double? get unitCost => throw _privateConstructorUsedError;
  double? get totalCost => throw _privateConstructorUsedError;
  ProductModel? get parent => throw _privateConstructorUsedError;
  ProvisionsFilter? get filterType => throw _privateConstructorUsedError;
  TaskProgressModel? get task => throw _privateConstructorUsedError;

  /// Serializes this ProvisionProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionProductModelCopyWith<ProvisionProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionProductModelCopyWith<$Res> {
  factory $ProvisionProductModelCopyWith(ProvisionProductModel value,
          $Res Function(ProvisionProductModel) then) =
      _$ProvisionProductModelCopyWithImpl<$Res, ProvisionProductModel>;
  @useResult
  $Res call(
      {String? uniqueId,
      String? productId,
      ProductModel? product,
      String? productName,
      NomenclatureModel? material,
      ProductModel? parentProduct,
      double? quantity,
      double? totalMass,
      double? totalQuantity,
      List<ParametersFeatureType>? features,
      UserModel? responsibleUser,
      String? responsibleUserName,
      SupplyStatus? status,
      ContractModel? contract,
      List<LotModel>? lots,
      ClientModel? supplier,
      DateTime? deliveryDate,
      double? unitCost,
      double? totalCost,
      ProductModel? parent,
      ProvisionsFilter? filterType,
      TaskProgressModel? task});

  $ProductModelCopyWith<$Res>? get product;
  $ProductModelCopyWith<$Res>? get parentProduct;
  $ContractModelCopyWith<$Res>? get contract;
  $ProductModelCopyWith<$Res>? get parent;
  $TaskProgressModelCopyWith<$Res>? get task;
}

/// @nodoc
class _$ProvisionProductModelCopyWithImpl<$Res,
        $Val extends ProvisionProductModel>
    implements $ProvisionProductModelCopyWith<$Res> {
  _$ProvisionProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uniqueId = freezed,
    Object? productId = freezed,
    Object? product = freezed,
    Object? productName = freezed,
    Object? material = freezed,
    Object? parentProduct = freezed,
    Object? quantity = freezed,
    Object? totalMass = freezed,
    Object? totalQuantity = freezed,
    Object? features = freezed,
    Object? responsibleUser = freezed,
    Object? responsibleUserName = freezed,
    Object? status = freezed,
    Object? contract = freezed,
    Object? lots = freezed,
    Object? supplier = freezed,
    Object? deliveryDate = freezed,
    Object? unitCost = freezed,
    Object? totalCost = freezed,
    Object? parent = freezed,
    Object? filterType = freezed,
    Object? task = freezed,
  }) {
    return _then(_value.copyWith(
      uniqueId: freezed == uniqueId
          ? _value.uniqueId
          : uniqueId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      parentProduct: freezed == parentProduct
          ? _value.parentProduct
          : parentProduct // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalMass: freezed == totalMass
          ? _value.totalMass
          : totalMass // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      features: freezed == features
          ? _value.features
          : features // ignore: cast_nullable_to_non_nullable
              as List<ParametersFeatureType>?,
      responsibleUser: freezed == responsibleUser
          ? _value.responsibleUser
          : responsibleUser // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      responsibleUserName: freezed == responsibleUserName
          ? _value.responsibleUserName
          : responsibleUserName // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SupplyStatus?,
      contract: freezed == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as ContractModel?,
      lots: freezed == lots
          ? _value.lots
          : lots // ignore: cast_nullable_to_non_nullable
              as List<LotModel>?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      deliveryDate: freezed == deliveryDate
          ? _value.deliveryDate
          : deliveryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      unitCost: freezed == unitCost
          ? _value.unitCost
          : unitCost // ignore: cast_nullable_to_non_nullable
              as double?,
      totalCost: freezed == totalCost
          ? _value.totalCost
          : totalCost // ignore: cast_nullable_to_non_nullable
              as double?,
      parent: freezed == parent
          ? _value.parent
          : parent // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      task: freezed == task
          ? _value.task
          : task // ignore: cast_nullable_to_non_nullable
              as TaskProgressModel?,
    ) as $Val);
  }

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get product {
    if (_value.product == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.product!, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get parentProduct {
    if (_value.parentProduct == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.parentProduct!, (value) {
      return _then(_value.copyWith(parentProduct: value) as $Val);
    });
  }

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContractModelCopyWith<$Res>? get contract {
    if (_value.contract == null) {
      return null;
    }

    return $ContractModelCopyWith<$Res>(_value.contract!, (value) {
      return _then(_value.copyWith(contract: value) as $Val);
    });
  }

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get parent {
    if (_value.parent == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.parent!, (value) {
      return _then(_value.copyWith(parent: value) as $Val);
    });
  }

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TaskProgressModelCopyWith<$Res>? get task {
    if (_value.task == null) {
      return null;
    }

    return $TaskProgressModelCopyWith<$Res>(_value.task!, (value) {
      return _then(_value.copyWith(task: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProvisionProductModelImplCopyWith<$Res>
    implements $ProvisionProductModelCopyWith<$Res> {
  factory _$$ProvisionProductModelImplCopyWith(
          _$ProvisionProductModelImpl value,
          $Res Function(_$ProvisionProductModelImpl) then) =
      __$$ProvisionProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? uniqueId,
      String? productId,
      ProductModel? product,
      String? productName,
      NomenclatureModel? material,
      ProductModel? parentProduct,
      double? quantity,
      double? totalMass,
      double? totalQuantity,
      List<ParametersFeatureType>? features,
      UserModel? responsibleUser,
      String? responsibleUserName,
      SupplyStatus? status,
      ContractModel? contract,
      List<LotModel>? lots,
      ClientModel? supplier,
      DateTime? deliveryDate,
      double? unitCost,
      double? totalCost,
      ProductModel? parent,
      ProvisionsFilter? filterType,
      TaskProgressModel? task});

  @override
  $ProductModelCopyWith<$Res>? get product;
  @override
  $ProductModelCopyWith<$Res>? get parentProduct;
  @override
  $ContractModelCopyWith<$Res>? get contract;
  @override
  $ProductModelCopyWith<$Res>? get parent;
  @override
  $TaskProgressModelCopyWith<$Res>? get task;
}

/// @nodoc
class __$$ProvisionProductModelImplCopyWithImpl<$Res>
    extends _$ProvisionProductModelCopyWithImpl<$Res,
        _$ProvisionProductModelImpl>
    implements _$$ProvisionProductModelImplCopyWith<$Res> {
  __$$ProvisionProductModelImplCopyWithImpl(_$ProvisionProductModelImpl _value,
      $Res Function(_$ProvisionProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uniqueId = freezed,
    Object? productId = freezed,
    Object? product = freezed,
    Object? productName = freezed,
    Object? material = freezed,
    Object? parentProduct = freezed,
    Object? quantity = freezed,
    Object? totalMass = freezed,
    Object? totalQuantity = freezed,
    Object? features = freezed,
    Object? responsibleUser = freezed,
    Object? responsibleUserName = freezed,
    Object? status = freezed,
    Object? contract = freezed,
    Object? lots = freezed,
    Object? supplier = freezed,
    Object? deliveryDate = freezed,
    Object? unitCost = freezed,
    Object? totalCost = freezed,
    Object? parent = freezed,
    Object? filterType = freezed,
    Object? task = freezed,
  }) {
    return _then(_$ProvisionProductModelImpl(
      uniqueId: freezed == uniqueId
          ? _value.uniqueId
          : uniqueId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      parentProduct: freezed == parentProduct
          ? _value.parentProduct
          : parentProduct // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalMass: freezed == totalMass
          ? _value.totalMass
          : totalMass // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      features: freezed == features
          ? _value._features
          : features // ignore: cast_nullable_to_non_nullable
              as List<ParametersFeatureType>?,
      responsibleUser: freezed == responsibleUser
          ? _value.responsibleUser
          : responsibleUser // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      responsibleUserName: freezed == responsibleUserName
          ? _value.responsibleUserName
          : responsibleUserName // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SupplyStatus?,
      contract: freezed == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as ContractModel?,
      lots: freezed == lots
          ? _value._lots
          : lots // ignore: cast_nullable_to_non_nullable
              as List<LotModel>?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      deliveryDate: freezed == deliveryDate
          ? _value.deliveryDate
          : deliveryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      unitCost: freezed == unitCost
          ? _value.unitCost
          : unitCost // ignore: cast_nullable_to_non_nullable
              as double?,
      totalCost: freezed == totalCost
          ? _value.totalCost
          : totalCost // ignore: cast_nullable_to_non_nullable
              as double?,
      parent: freezed == parent
          ? _value.parent
          : parent // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      task: freezed == task
          ? _value.task
          : task // ignore: cast_nullable_to_non_nullable
              as TaskProgressModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionProductModelImpl implements _ProvisionProductModel {
  const _$ProvisionProductModelImpl(
      {this.uniqueId,
      this.productId,
      this.product,
      this.productName,
      this.material,
      this.parentProduct,
      this.quantity,
      this.totalMass,
      this.totalQuantity,
      final List<ParametersFeatureType>? features,
      this.responsibleUser,
      this.responsibleUserName,
      this.status,
      this.contract,
      final List<LotModel>? lots,
      this.supplier,
      this.deliveryDate,
      this.unitCost,
      this.totalCost,
      this.parent,
      this.filterType,
      this.task})
      : _features = features,
        _lots = lots;

  factory _$ProvisionProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionProductModelImplFromJson(json);

// String? provisionItemId,
  @override
  final String? uniqueId;
  @override
  final String? productId;
  @override
  final ProductModel? product;
  @override
  final String? productName;
  @override
  final NomenclatureModel? material;
  @override
  final ProductModel? parentProduct;
// UnitType? unitType,
  @override
  final double? quantity;
  @override
  final double? totalMass;
  @override
  final double? totalQuantity;
  final List<ParametersFeatureType>? _features;
  @override
  List<ParametersFeatureType>? get features {
    final value = _features;
    if (value == null) return null;
    if (_features is EqualUnmodifiableListView) return _features;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final UserModel? responsibleUser;
  @override
  final String? responsibleUserName;
  @override
  final SupplyStatus? status;
  @override
  final ContractModel? contract;
  final List<LotModel>? _lots;
  @override
  List<LotModel>? get lots {
    final value = _lots;
    if (value == null) return null;
    if (_lots is EqualUnmodifiableListView) return _lots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ClientModel? supplier;
  @override
  final DateTime? deliveryDate;
  @override
  final double? unitCost;
  @override
  final double? totalCost;
  @override
  final ProductModel? parent;
  @override
  final ProvisionsFilter? filterType;
  @override
  final TaskProgressModel? task;

  @override
  String toString() {
    return 'ProvisionProductModel(uniqueId: $uniqueId, productId: $productId, product: $product, productName: $productName, material: $material, parentProduct: $parentProduct, quantity: $quantity, totalMass: $totalMass, totalQuantity: $totalQuantity, features: $features, responsibleUser: $responsibleUser, responsibleUserName: $responsibleUserName, status: $status, contract: $contract, lots: $lots, supplier: $supplier, deliveryDate: $deliveryDate, unitCost: $unitCost, totalCost: $totalCost, parent: $parent, filterType: $filterType, task: $task)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionProductModelImpl &&
            (identical(other.uniqueId, uniqueId) ||
                other.uniqueId == uniqueId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.material, material) ||
                other.material == material) &&
            (identical(other.parentProduct, parentProduct) ||
                other.parentProduct == parentProduct) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.totalMass, totalMass) ||
                other.totalMass == totalMass) &&
            (identical(other.totalQuantity, totalQuantity) ||
                other.totalQuantity == totalQuantity) &&
            const DeepCollectionEquality().equals(other._features, _features) &&
            (identical(other.responsibleUser, responsibleUser) ||
                other.responsibleUser == responsibleUser) &&
            (identical(other.responsibleUserName, responsibleUserName) ||
                other.responsibleUserName == responsibleUserName) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.contract, contract) ||
                other.contract == contract) &&
            const DeepCollectionEquality().equals(other._lots, _lots) &&
            (identical(other.supplier, supplier) ||
                other.supplier == supplier) &&
            (identical(other.deliveryDate, deliveryDate) ||
                other.deliveryDate == deliveryDate) &&
            (identical(other.unitCost, unitCost) ||
                other.unitCost == unitCost) &&
            (identical(other.totalCost, totalCost) ||
                other.totalCost == totalCost) &&
            (identical(other.parent, parent) || other.parent == parent) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.task, task) || other.task == task));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        uniqueId,
        productId,
        product,
        productName,
        material,
        parentProduct,
        quantity,
        totalMass,
        totalQuantity,
        const DeepCollectionEquality().hash(_features),
        responsibleUser,
        responsibleUserName,
        status,
        contract,
        const DeepCollectionEquality().hash(_lots),
        supplier,
        deliveryDate,
        unitCost,
        totalCost,
        parent,
        filterType,
        task
      ]);

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionProductModelImplCopyWith<_$ProvisionProductModelImpl>
      get copyWith => __$$ProvisionProductModelImplCopyWithImpl<
          _$ProvisionProductModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionProductModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionProductModel implements ProvisionProductModel {
  const factory _ProvisionProductModel(
      {final String? uniqueId,
      final String? productId,
      final ProductModel? product,
      final String? productName,
      final NomenclatureModel? material,
      final ProductModel? parentProduct,
      final double? quantity,
      final double? totalMass,
      final double? totalQuantity,
      final List<ParametersFeatureType>? features,
      final UserModel? responsibleUser,
      final String? responsibleUserName,
      final SupplyStatus? status,
      final ContractModel? contract,
      final List<LotModel>? lots,
      final ClientModel? supplier,
      final DateTime? deliveryDate,
      final double? unitCost,
      final double? totalCost,
      final ProductModel? parent,
      final ProvisionsFilter? filterType,
      final TaskProgressModel? task}) = _$ProvisionProductModelImpl;

  factory _ProvisionProductModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionProductModelImpl.fromJson;

// String? provisionItemId,
  @override
  String? get uniqueId;
  @override
  String? get productId;
  @override
  ProductModel? get product;
  @override
  String? get productName;
  @override
  NomenclatureModel? get material;
  @override
  ProductModel? get parentProduct; // UnitType? unitType,
  @override
  double? get quantity;
  @override
  double? get totalMass;
  @override
  double? get totalQuantity;
  @override
  List<ParametersFeatureType>? get features;
  @override
  UserModel? get responsibleUser;
  @override
  String? get responsibleUserName;
  @override
  SupplyStatus? get status;
  @override
  ContractModel? get contract;
  @override
  List<LotModel>? get lots;
  @override
  ClientModel? get supplier;
  @override
  DateTime? get deliveryDate;
  @override
  double? get unitCost;
  @override
  double? get totalCost;
  @override
  ProductModel? get parent;
  @override
  ProvisionsFilter? get filterType;
  @override
  TaskProgressModel? get task;

  /// Create a copy of ProvisionProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionProductModelImplCopyWith<_$ProvisionProductModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ContractModel _$ContractModelFromJson(Map<String, dynamic> json) {
  return _ContractModel.fromJson(json);
}

/// @nodoc
mixin _$ContractModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  DateTime? get contractDate => throw _privateConstructorUsedError;
  DateTime? get plannedDeliveryDate => throw _privateConstructorUsedError;
  String? get supplierId => throw _privateConstructorUsedError;
  ClientModel? get supplier => throw _privateConstructorUsedError;
  List<String>? get productIds => throw _privateConstructorUsedError;
  double? get contractPrice => throw _privateConstructorUsedError;
  dynamic get productDetails => throw _privateConstructorUsedError;
  List<String>? get documentIds => throw _privateConstructorUsedError;
  SupplyStatus? get status => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  String? get updatedBy => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ContractModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContractModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContractModelCopyWith<ContractModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContractModelCopyWith<$Res> {
  factory $ContractModelCopyWith(
          ContractModel value, $Res Function(ContractModel) then) =
      _$ContractModelCopyWithImpl<$Res, ContractModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projectId,
      String? contractNumber,
      DateTime? contractDate,
      DateTime? plannedDeliveryDate,
      String? supplierId,
      ClientModel? supplier,
      List<String>? productIds,
      double? contractPrice,
      dynamic productDetails,
      List<String>? documentIds,
      SupplyStatus? status,
      String? notes,
      String? createdBy,
      String? updatedBy,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$ContractModelCopyWithImpl<$Res, $Val extends ContractModel>
    implements $ContractModelCopyWith<$Res> {
  _$ContractModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContractModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projectId = freezed,
    Object? contractNumber = freezed,
    Object? contractDate = freezed,
    Object? plannedDeliveryDate = freezed,
    Object? supplierId = freezed,
    Object? supplier = freezed,
    Object? productIds = freezed,
    Object? contractPrice = freezed,
    Object? productDetails = freezed,
    Object? documentIds = freezed,
    Object? status = freezed,
    Object? notes = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractDate: freezed == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      plannedDeliveryDate: freezed == plannedDeliveryDate
          ? _value.plannedDeliveryDate
          : plannedDeliveryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      productIds: freezed == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractPrice: freezed == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      productDetails: freezed == productDetails
          ? _value.productDetails
          : productDetails // ignore: cast_nullable_to_non_nullable
              as dynamic,
      documentIds: freezed == documentIds
          ? _value.documentIds
          : documentIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SupplyStatus?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContractModelImplCopyWith<$Res>
    implements $ContractModelCopyWith<$Res> {
  factory _$$ContractModelImplCopyWith(
          _$ContractModelImpl value, $Res Function(_$ContractModelImpl) then) =
      __$$ContractModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projectId,
      String? contractNumber,
      DateTime? contractDate,
      DateTime? plannedDeliveryDate,
      String? supplierId,
      ClientModel? supplier,
      List<String>? productIds,
      double? contractPrice,
      dynamic productDetails,
      List<String>? documentIds,
      SupplyStatus? status,
      String? notes,
      String? createdBy,
      String? updatedBy,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$ContractModelImplCopyWithImpl<$Res>
    extends _$ContractModelCopyWithImpl<$Res, _$ContractModelImpl>
    implements _$$ContractModelImplCopyWith<$Res> {
  __$$ContractModelImplCopyWithImpl(
      _$ContractModelImpl _value, $Res Function(_$ContractModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContractModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projectId = freezed,
    Object? contractNumber = freezed,
    Object? contractDate = freezed,
    Object? plannedDeliveryDate = freezed,
    Object? supplierId = freezed,
    Object? supplier = freezed,
    Object? productIds = freezed,
    Object? contractPrice = freezed,
    Object? productDetails = freezed,
    Object? documentIds = freezed,
    Object? status = freezed,
    Object? notes = freezed,
    Object? createdBy = freezed,
    Object? updatedBy = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ContractModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractDate: freezed == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      plannedDeliveryDate: freezed == plannedDeliveryDate
          ? _value.plannedDeliveryDate
          : plannedDeliveryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      supplierId: freezed == supplierId
          ? _value.supplierId
          : supplierId // ignore: cast_nullable_to_non_nullable
              as String?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      productIds: freezed == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractPrice: freezed == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      productDetails: freezed == productDetails
          ? _value.productDetails
          : productDetails // ignore: cast_nullable_to_non_nullable
              as dynamic,
      documentIds: freezed == documentIds
          ? _value._documentIds
          : documentIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SupplyStatus?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedBy: freezed == updatedBy
          ? _value.updatedBy
          : updatedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ContractModelImpl implements _ContractModel {
  _$ContractModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.projectId,
      this.contractNumber,
      this.contractDate,
      this.plannedDeliveryDate,
      this.supplierId,
      this.supplier,
      final List<String>? productIds,
      this.contractPrice,
      this.productDetails,
      final List<String>? documentIds,
      this.status,
      this.notes,
      this.createdBy,
      this.updatedBy,
      this.createdAt,
      this.updatedAt})
      : _productIds = productIds,
        _documentIds = documentIds;

  factory _$ContractModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContractModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? projectId;
  @override
  final String? contractNumber;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? plannedDeliveryDate;
  @override
  final String? supplierId;
  @override
  final ClientModel? supplier;
  final List<String>? _productIds;
  @override
  List<String>? get productIds {
    final value = _productIds;
    if (value == null) return null;
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? contractPrice;
  @override
  final dynamic productDetails;
  final List<String>? _documentIds;
  @override
  List<String>? get documentIds {
    final value = _documentIds;
    if (value == null) return null;
    if (_documentIds is EqualUnmodifiableListView) return _documentIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final SupplyStatus? status;
  @override
  final String? notes;
  @override
  final String? createdBy;
  @override
  final String? updatedBy;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ContractModel(id: $id, projectId: $projectId, contractNumber: $contractNumber, contractDate: $contractDate, plannedDeliveryDate: $plannedDeliveryDate, supplierId: $supplierId, supplier: $supplier, productIds: $productIds, contractPrice: $contractPrice, productDetails: $productDetails, documentIds: $documentIds, status: $status, notes: $notes, createdBy: $createdBy, updatedBy: $updatedBy, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContractModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.contractDate, contractDate) ||
                other.contractDate == contractDate) &&
            (identical(other.plannedDeliveryDate, plannedDeliveryDate) ||
                other.plannedDeliveryDate == plannedDeliveryDate) &&
            (identical(other.supplierId, supplierId) ||
                other.supplierId == supplierId) &&
            (identical(other.supplier, supplier) ||
                other.supplier == supplier) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            (identical(other.contractPrice, contractPrice) ||
                other.contractPrice == contractPrice) &&
            const DeepCollectionEquality()
                .equals(other.productDetails, productDetails) &&
            const DeepCollectionEquality()
                .equals(other._documentIds, _documentIds) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.updatedBy, updatedBy) ||
                other.updatedBy == updatedBy) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      projectId,
      contractNumber,
      contractDate,
      plannedDeliveryDate,
      supplierId,
      supplier,
      const DeepCollectionEquality().hash(_productIds),
      contractPrice,
      const DeepCollectionEquality().hash(productDetails),
      const DeepCollectionEquality().hash(_documentIds),
      status,
      notes,
      createdBy,
      updatedBy,
      createdAt,
      updatedAt);

  /// Create a copy of ContractModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContractModelImplCopyWith<_$ContractModelImpl> get copyWith =>
      __$$ContractModelImplCopyWithImpl<_$ContractModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContractModelImplToJson(
      this,
    );
  }
}

abstract class _ContractModel implements ContractModel {
  factory _ContractModel(
      {@JsonKey(name: '_id') final String? id,
      final String? projectId,
      final String? contractNumber,
      final DateTime? contractDate,
      final DateTime? plannedDeliveryDate,
      final String? supplierId,
      final ClientModel? supplier,
      final List<String>? productIds,
      final double? contractPrice,
      final dynamic productDetails,
      final List<String>? documentIds,
      final SupplyStatus? status,
      final String? notes,
      final String? createdBy,
      final String? updatedBy,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ContractModelImpl;

  factory _ContractModel.fromJson(Map<String, dynamic> json) =
      _$ContractModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get projectId;
  @override
  String? get contractNumber;
  @override
  DateTime? get contractDate;
  @override
  DateTime? get plannedDeliveryDate;
  @override
  String? get supplierId;
  @override
  ClientModel? get supplier;
  @override
  List<String>? get productIds;
  @override
  double? get contractPrice;
  @override
  dynamic get productDetails;
  @override
  List<String>? get documentIds;
  @override
  SupplyStatus? get status;
  @override
  String? get notes;
  @override
  String? get createdBy;
  @override
  String? get updatedBy;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ContractModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContractModelImplCopyWith<_$ContractModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LotModel _$LotModelFromJson(Map<String, dynamic> json) {
  return _LotModel.fromJson(json);
}

/// @nodoc
mixin _$LotModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  int? get lotNumber => throw _privateConstructorUsedError;
  String? get lotName => throw _privateConstructorUsedError;
  List<String>? get productIds => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  DateTime? get plannedTenderCompletionDate =>
      throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this LotModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LotModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LotModelCopyWith<LotModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LotModelCopyWith<$Res> {
  factory $LotModelCopyWith(LotModel value, $Res Function(LotModel) then) =
      _$LotModelCopyWithImpl<$Res, LotModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projectId,
      int? lotNumber,
      String? lotName,
      List<String>? productIds,
      String? createdBy,
      DateTime? plannedTenderCompletionDate,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$LotModelCopyWithImpl<$Res, $Val extends LotModel>
    implements $LotModelCopyWith<$Res> {
  _$LotModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LotModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projectId = freezed,
    Object? lotNumber = freezed,
    Object? lotName = freezed,
    Object? productIds = freezed,
    Object? createdBy = freezed,
    Object? plannedTenderCompletionDate = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      lotNumber: freezed == lotNumber
          ? _value.lotNumber
          : lotNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      lotName: freezed == lotName
          ? _value.lotName
          : lotName // ignore: cast_nullable_to_non_nullable
              as String?,
      productIds: freezed == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      plannedTenderCompletionDate: freezed == plannedTenderCompletionDate
          ? _value.plannedTenderCompletionDate
          : plannedTenderCompletionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LotModelImplCopyWith<$Res>
    implements $LotModelCopyWith<$Res> {
  factory _$$LotModelImplCopyWith(
          _$LotModelImpl value, $Res Function(_$LotModelImpl) then) =
      __$$LotModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projectId,
      int? lotNumber,
      String? lotName,
      List<String>? productIds,
      String? createdBy,
      DateTime? plannedTenderCompletionDate,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$LotModelImplCopyWithImpl<$Res>
    extends _$LotModelCopyWithImpl<$Res, _$LotModelImpl>
    implements _$$LotModelImplCopyWith<$Res> {
  __$$LotModelImplCopyWithImpl(
      _$LotModelImpl _value, $Res Function(_$LotModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of LotModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projectId = freezed,
    Object? lotNumber = freezed,
    Object? lotName = freezed,
    Object? productIds = freezed,
    Object? createdBy = freezed,
    Object? plannedTenderCompletionDate = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$LotModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      lotNumber: freezed == lotNumber
          ? _value.lotNumber
          : lotNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      lotName: freezed == lotName
          ? _value.lotName
          : lotName // ignore: cast_nullable_to_non_nullable
              as String?,
      productIds: freezed == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      plannedTenderCompletionDate: freezed == plannedTenderCompletionDate
          ? _value.plannedTenderCompletionDate
          : plannedTenderCompletionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$LotModelImpl implements _LotModel {
  _$LotModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.projectId,
      this.lotNumber,
      this.lotName,
      final List<String>? productIds,
      this.createdBy,
      this.plannedTenderCompletionDate,
      this.createdAt,
      this.updatedAt})
      : _productIds = productIds;

  factory _$LotModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LotModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? projectId;
  @override
  final int? lotNumber;
  @override
  final String? lotName;
  final List<String>? _productIds;
  @override
  List<String>? get productIds {
    final value = _productIds;
    if (value == null) return null;
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? createdBy;
  @override
  final DateTime? plannedTenderCompletionDate;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'LotModel(id: $id, projectId: $projectId, lotNumber: $lotNumber, lotName: $lotName, productIds: $productIds, createdBy: $createdBy, plannedTenderCompletionDate: $plannedTenderCompletionDate, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LotModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.lotNumber, lotNumber) ||
                other.lotNumber == lotNumber) &&
            (identical(other.lotName, lotName) || other.lotName == lotName) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.plannedTenderCompletionDate,
                    plannedTenderCompletionDate) ||
                other.plannedTenderCompletionDate ==
                    plannedTenderCompletionDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      projectId,
      lotNumber,
      lotName,
      const DeepCollectionEquality().hash(_productIds),
      createdBy,
      plannedTenderCompletionDate,
      createdAt,
      updatedAt);

  /// Create a copy of LotModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LotModelImplCopyWith<_$LotModelImpl> get copyWith =>
      __$$LotModelImplCopyWithImpl<_$LotModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LotModelImplToJson(
      this,
    );
  }
}

abstract class _LotModel implements LotModel {
  factory _LotModel(
      {@JsonKey(name: '_id') final String? id,
      final String? projectId,
      final int? lotNumber,
      final String? lotName,
      final List<String>? productIds,
      final String? createdBy,
      final DateTime? plannedTenderCompletionDate,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$LotModelImpl;

  factory _LotModel.fromJson(Map<String, dynamic> json) =
      _$LotModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get projectId;
  @override
  int? get lotNumber;
  @override
  String? get lotName;
  @override
  List<String>? get productIds;
  @override
  String? get createdBy;
  @override
  DateTime? get plannedTenderCompletionDate;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of LotModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LotModelImplCopyWith<_$LotModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProvisionMaterialModel _$ProvisionMaterialModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionMaterialModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionMaterialModel {
  String? get materialId => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;
  UnitType? get unitType => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  String? get materialRequirements => throw _privateConstructorUsedError;
  double? get totalQuantity => throw _privateConstructorUsedError;
  List<ProvisionItemModel>? get products =>
      throw _privateConstructorUsedError; // other
  double? get storageQuantity => throw _privateConstructorUsedError;
  double? get totalPrice => throw _privateConstructorUsedError;
  List<String>? get operations => throw _privateConstructorUsedError;
  List<ProvisionDeliveryModel>? get deliveries =>
      throw _privateConstructorUsedError;

  /// Serializes this ProvisionMaterialModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionMaterialModelCopyWith<ProvisionMaterialModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionMaterialModelCopyWith<$Res> {
  factory $ProvisionMaterialModelCopyWith(ProvisionMaterialModel value,
          $Res Function(ProvisionMaterialModel) then) =
      _$ProvisionMaterialModelCopyWithImpl<$Res, ProvisionMaterialModel>;
  @useResult
  $Res call(
      {String? materialId,
      String? materialName,
      UnitType? unitType,
      double? quantity,
      String? materialRequirements,
      double? totalQuantity,
      List<ProvisionItemModel>? products,
      double? storageQuantity,
      double? totalPrice,
      List<String>? operations,
      List<ProvisionDeliveryModel>? deliveries});
}

/// @nodoc
class _$ProvisionMaterialModelCopyWithImpl<$Res,
        $Val extends ProvisionMaterialModel>
    implements $ProvisionMaterialModelCopyWith<$Res> {
  _$ProvisionMaterialModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? unitType = freezed,
    Object? quantity = freezed,
    Object? materialRequirements = freezed,
    Object? totalQuantity = freezed,
    Object? products = freezed,
    Object? storageQuantity = freezed,
    Object? totalPrice = freezed,
    Object? operations = freezed,
    Object? deliveries = freezed,
  }) {
    return _then(_value.copyWith(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      products: freezed == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProvisionItemModel>?,
      storageQuantity: freezed == storageQuantity
          ? _value.storageQuantity
          : storageQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPrice: freezed == totalPrice
          ? _value.totalPrice
          : totalPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      operations: freezed == operations
          ? _value.operations
          : operations // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      deliveries: freezed == deliveries
          ? _value.deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<ProvisionDeliveryModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionMaterialModelImplCopyWith<$Res>
    implements $ProvisionMaterialModelCopyWith<$Res> {
  factory _$$ProvisionMaterialModelImplCopyWith(
          _$ProvisionMaterialModelImpl value,
          $Res Function(_$ProvisionMaterialModelImpl) then) =
      __$$ProvisionMaterialModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? materialId,
      String? materialName,
      UnitType? unitType,
      double? quantity,
      String? materialRequirements,
      double? totalQuantity,
      List<ProvisionItemModel>? products,
      double? storageQuantity,
      double? totalPrice,
      List<String>? operations,
      List<ProvisionDeliveryModel>? deliveries});
}

/// @nodoc
class __$$ProvisionMaterialModelImplCopyWithImpl<$Res>
    extends _$ProvisionMaterialModelCopyWithImpl<$Res,
        _$ProvisionMaterialModelImpl>
    implements _$$ProvisionMaterialModelImplCopyWith<$Res> {
  __$$ProvisionMaterialModelImplCopyWithImpl(
      _$ProvisionMaterialModelImpl _value,
      $Res Function(_$ProvisionMaterialModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? unitType = freezed,
    Object? quantity = freezed,
    Object? materialRequirements = freezed,
    Object? totalQuantity = freezed,
    Object? products = freezed,
    Object? storageQuantity = freezed,
    Object? totalPrice = freezed,
    Object? operations = freezed,
    Object? deliveries = freezed,
  }) {
    return _then(_$ProvisionMaterialModelImpl(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      products: freezed == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProvisionItemModel>?,
      storageQuantity: freezed == storageQuantity
          ? _value.storageQuantity
          : storageQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPrice: freezed == totalPrice
          ? _value.totalPrice
          : totalPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      operations: freezed == operations
          ? _value._operations
          : operations // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      deliveries: freezed == deliveries
          ? _value._deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<ProvisionDeliveryModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionMaterialModelImpl implements _ProvisionMaterialModel {
  const _$ProvisionMaterialModelImpl(
      {this.materialId,
      this.materialName,
      this.unitType,
      this.quantity,
      this.materialRequirements,
      this.totalQuantity,
      final List<ProvisionItemModel>? products,
      this.storageQuantity,
      this.totalPrice,
      final List<String>? operations,
      final List<ProvisionDeliveryModel>? deliveries})
      : _products = products,
        _operations = operations,
        _deliveries = deliveries;

  factory _$ProvisionMaterialModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionMaterialModelImplFromJson(json);

  @override
  final String? materialId;
  @override
  final String? materialName;
  @override
  final UnitType? unitType;
  @override
  final double? quantity;
  @override
  final String? materialRequirements;
  @override
  final double? totalQuantity;
  final List<ProvisionItemModel>? _products;
  @override
  List<ProvisionItemModel>? get products {
    final value = _products;
    if (value == null) return null;
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// other
  @override
  final double? storageQuantity;
  @override
  final double? totalPrice;
  final List<String>? _operations;
  @override
  List<String>? get operations {
    final value = _operations;
    if (value == null) return null;
    if (_operations is EqualUnmodifiableListView) return _operations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProvisionDeliveryModel>? _deliveries;
  @override
  List<ProvisionDeliveryModel>? get deliveries {
    final value = _deliveries;
    if (value == null) return null;
    if (_deliveries is EqualUnmodifiableListView) return _deliveries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProvisionMaterialModel(materialId: $materialId, materialName: $materialName, unitType: $unitType, quantity: $quantity, materialRequirements: $materialRequirements, totalQuantity: $totalQuantity, products: $products, storageQuantity: $storageQuantity, totalPrice: $totalPrice, operations: $operations, deliveries: $deliveries)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionMaterialModelImpl &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.unitType, unitType) ||
                other.unitType == unitType) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.materialRequirements, materialRequirements) ||
                other.materialRequirements == materialRequirements) &&
            (identical(other.totalQuantity, totalQuantity) ||
                other.totalQuantity == totalQuantity) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            (identical(other.storageQuantity, storageQuantity) ||
                other.storageQuantity == storageQuantity) &&
            (identical(other.totalPrice, totalPrice) ||
                other.totalPrice == totalPrice) &&
            const DeepCollectionEquality()
                .equals(other._operations, _operations) &&
            const DeepCollectionEquality()
                .equals(other._deliveries, _deliveries));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      materialId,
      materialName,
      unitType,
      quantity,
      materialRequirements,
      totalQuantity,
      const DeepCollectionEquality().hash(_products),
      storageQuantity,
      totalPrice,
      const DeepCollectionEquality().hash(_operations),
      const DeepCollectionEquality().hash(_deliveries));

  /// Create a copy of ProvisionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionMaterialModelImplCopyWith<_$ProvisionMaterialModelImpl>
      get copyWith => __$$ProvisionMaterialModelImplCopyWithImpl<
          _$ProvisionMaterialModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionMaterialModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionMaterialModel implements ProvisionMaterialModel {
  const factory _ProvisionMaterialModel(
          {final String? materialId,
          final String? materialName,
          final UnitType? unitType,
          final double? quantity,
          final String? materialRequirements,
          final double? totalQuantity,
          final List<ProvisionItemModel>? products,
          final double? storageQuantity,
          final double? totalPrice,
          final List<String>? operations,
          final List<ProvisionDeliveryModel>? deliveries}) =
      _$ProvisionMaterialModelImpl;

  factory _ProvisionMaterialModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionMaterialModelImpl.fromJson;

  @override
  String? get materialId;
  @override
  String? get materialName;
  @override
  UnitType? get unitType;
  @override
  double? get quantity;
  @override
  String? get materialRequirements;
  @override
  double? get totalQuantity;
  @override
  List<ProvisionItemModel>? get products; // other
  @override
  double? get storageQuantity;
  @override
  double? get totalPrice;
  @override
  List<String>? get operations;
  @override
  List<ProvisionDeliveryModel>? get deliveries;

  /// Create a copy of ProvisionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionMaterialModelImplCopyWith<_$ProvisionMaterialModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionDeliveryModel _$ProvisionDeliveryModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionDeliviresModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionDeliveryModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError; // SupplyState? status,
  DateTime? get expectedDate => throw _privateConstructorUsedError;
  List<DateTime>? get deliveryDates => throw _privateConstructorUsedError;
  List<SupplyInputModel>? get links => throw _privateConstructorUsedError;

  /// Serializes this ProvisionDeliveryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionDeliveryModelCopyWith<ProvisionDeliveryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionDeliveryModelCopyWith<$Res> {
  factory $ProvisionDeliveryModelCopyWith(ProvisionDeliveryModel value,
          $Res Function(ProvisionDeliveryModel) then) =
      _$ProvisionDeliveryModelCopyWithImpl<$Res, ProvisionDeliveryModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      DateTime? expectedDate,
      List<DateTime>? deliveryDates,
      List<SupplyInputModel>? links});
}

/// @nodoc
class _$ProvisionDeliveryModelCopyWithImpl<$Res,
        $Val extends ProvisionDeliveryModel>
    implements $ProvisionDeliveryModelCopyWith<$Res> {
  _$ProvisionDeliveryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? expectedDate = freezed,
    Object? deliveryDates = freezed,
    Object? links = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      expectedDate: freezed == expectedDate
          ? _value.expectedDate
          : expectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDates: freezed == deliveryDates
          ? _value.deliveryDates
          : deliveryDates // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      links: freezed == links
          ? _value.links
          : links // ignore: cast_nullable_to_non_nullable
              as List<SupplyInputModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionDeliviresModelImplCopyWith<$Res>
    implements $ProvisionDeliveryModelCopyWith<$Res> {
  factory _$$ProvisionDeliviresModelImplCopyWith(
          _$ProvisionDeliviresModelImpl value,
          $Res Function(_$ProvisionDeliviresModelImpl) then) =
      __$$ProvisionDeliviresModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      DateTime? expectedDate,
      List<DateTime>? deliveryDates,
      List<SupplyInputModel>? links});
}

/// @nodoc
class __$$ProvisionDeliviresModelImplCopyWithImpl<$Res>
    extends _$ProvisionDeliveryModelCopyWithImpl<$Res,
        _$ProvisionDeliviresModelImpl>
    implements _$$ProvisionDeliviresModelImplCopyWith<$Res> {
  __$$ProvisionDeliviresModelImplCopyWithImpl(
      _$ProvisionDeliviresModelImpl _value,
      $Res Function(_$ProvisionDeliviresModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? expectedDate = freezed,
    Object? deliveryDates = freezed,
    Object? links = freezed,
  }) {
    return _then(_$ProvisionDeliviresModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      expectedDate: freezed == expectedDate
          ? _value.expectedDate
          : expectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDates: freezed == deliveryDates
          ? _value._deliveryDates
          : deliveryDates // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      links: freezed == links
          ? _value._links
          : links // ignore: cast_nullable_to_non_nullable
              as List<SupplyInputModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionDeliviresModelImpl implements _ProvisionDeliviresModel {
  const _$ProvisionDeliviresModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.expectedDate,
      final List<DateTime>? deliveryDates,
      final List<SupplyInputModel>? links})
      : _deliveryDates = deliveryDates,
        _links = links;

  factory _$ProvisionDeliviresModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionDeliviresModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
// SupplyState? status,
  @override
  final DateTime? expectedDate;
  final List<DateTime>? _deliveryDates;
  @override
  List<DateTime>? get deliveryDates {
    final value = _deliveryDates;
    if (value == null) return null;
    if (_deliveryDates is EqualUnmodifiableListView) return _deliveryDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<SupplyInputModel>? _links;
  @override
  List<SupplyInputModel>? get links {
    final value = _links;
    if (value == null) return null;
    if (_links is EqualUnmodifiableListView) return _links;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProvisionDeliveryModel(id: $id, expectedDate: $expectedDate, deliveryDates: $deliveryDates, links: $links)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionDeliviresModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.expectedDate, expectedDate) ||
                other.expectedDate == expectedDate) &&
            const DeepCollectionEquality()
                .equals(other._deliveryDates, _deliveryDates) &&
            const DeepCollectionEquality().equals(other._links, _links));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      expectedDate,
      const DeepCollectionEquality().hash(_deliveryDates),
      const DeepCollectionEquality().hash(_links));

  /// Create a copy of ProvisionDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionDeliviresModelImplCopyWith<_$ProvisionDeliviresModelImpl>
      get copyWith => __$$ProvisionDeliviresModelImplCopyWithImpl<
          _$ProvisionDeliviresModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionDeliviresModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionDeliviresModel implements ProvisionDeliveryModel {
  const factory _ProvisionDeliviresModel(
      {@JsonKey(name: '_id') final String? id,
      final DateTime? expectedDate,
      final List<DateTime>? deliveryDates,
      final List<SupplyInputModel>? links}) = _$ProvisionDeliviresModelImpl;

  factory _ProvisionDeliviresModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionDeliviresModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id; // SupplyState? status,
  @override
  DateTime? get expectedDate;
  @override
  List<DateTime>? get deliveryDates;
  @override
  List<SupplyInputModel>? get links;

  /// Create a copy of ProvisionDeliveryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionDeliviresModelImplCopyWith<_$ProvisionDeliviresModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionDeliveryLinkModel _$ProvisionDeliveryLinkModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionDeliveryLinkModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionDeliveryLinkModel {
  String? get provisionItemId => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;

  /// Serializes this ProvisionDeliveryLinkModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionDeliveryLinkModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionDeliveryLinkModelCopyWith<ProvisionDeliveryLinkModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionDeliveryLinkModelCopyWith<$Res> {
  factory $ProvisionDeliveryLinkModelCopyWith(ProvisionDeliveryLinkModel value,
          $Res Function(ProvisionDeliveryLinkModel) then) =
      _$ProvisionDeliveryLinkModelCopyWithImpl<$Res,
          ProvisionDeliveryLinkModel>;
  @useResult
  $Res call({String? provisionItemId, double? quantity});
}

/// @nodoc
class _$ProvisionDeliveryLinkModelCopyWithImpl<$Res,
        $Val extends ProvisionDeliveryLinkModel>
    implements $ProvisionDeliveryLinkModelCopyWith<$Res> {
  _$ProvisionDeliveryLinkModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionDeliveryLinkModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionItemId = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_value.copyWith(
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionDeliveryLinkModelImplCopyWith<$Res>
    implements $ProvisionDeliveryLinkModelCopyWith<$Res> {
  factory _$$ProvisionDeliveryLinkModelImplCopyWith(
          _$ProvisionDeliveryLinkModelImpl value,
          $Res Function(_$ProvisionDeliveryLinkModelImpl) then) =
      __$$ProvisionDeliveryLinkModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? provisionItemId, double? quantity});
}

/// @nodoc
class __$$ProvisionDeliveryLinkModelImplCopyWithImpl<$Res>
    extends _$ProvisionDeliveryLinkModelCopyWithImpl<$Res,
        _$ProvisionDeliveryLinkModelImpl>
    implements _$$ProvisionDeliveryLinkModelImplCopyWith<$Res> {
  __$$ProvisionDeliveryLinkModelImplCopyWithImpl(
      _$ProvisionDeliveryLinkModelImpl _value,
      $Res Function(_$ProvisionDeliveryLinkModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionDeliveryLinkModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? provisionItemId = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_$ProvisionDeliveryLinkModelImpl(
      provisionItemId: freezed == provisionItemId
          ? _value.provisionItemId
          : provisionItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionDeliveryLinkModelImpl implements _ProvisionDeliveryLinkModel {
  const _$ProvisionDeliveryLinkModelImpl({this.provisionItemId, this.quantity});

  factory _$ProvisionDeliveryLinkModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProvisionDeliveryLinkModelImplFromJson(json);

  @override
  final String? provisionItemId;
  @override
  final double? quantity;

  @override
  String toString() {
    return 'ProvisionDeliveryLinkModel(provisionItemId: $provisionItemId, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionDeliveryLinkModelImpl &&
            (identical(other.provisionItemId, provisionItemId) ||
                other.provisionItemId == provisionItemId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, provisionItemId, quantity);

  /// Create a copy of ProvisionDeliveryLinkModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionDeliveryLinkModelImplCopyWith<_$ProvisionDeliveryLinkModelImpl>
      get copyWith => __$$ProvisionDeliveryLinkModelImplCopyWithImpl<
          _$ProvisionDeliveryLinkModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionDeliveryLinkModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionDeliveryLinkModel
    implements ProvisionDeliveryLinkModel {
  const factory _ProvisionDeliveryLinkModel(
      {final String? provisionItemId,
      final double? quantity}) = _$ProvisionDeliveryLinkModelImpl;

  factory _ProvisionDeliveryLinkModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionDeliveryLinkModelImpl.fromJson;

  @override
  String? get provisionItemId;
  @override
  double? get quantity;

  /// Create a copy of ProvisionDeliveryLinkModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionDeliveryLinkModelImplCopyWith<_$ProvisionDeliveryLinkModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionLotModel _$ProvisionLotModelFromJson(Map<String, dynamic> json) {
  return _ProvisionLotModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionLotModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  ProvisionsFilter? get filterType => throw _privateConstructorUsedError;
  String? get provisionName => throw _privateConstructorUsedError;
  ProvisionStatus? get provisionStatus => throw _privateConstructorUsedError;
  ClientModel? get supplier => throw _privateConstructorUsedError;
  List<ProvisionItemModel>? get provisionItems =>
      throw _privateConstructorUsedError;
  List<ProvisionContractItemModel>? get contractItems =>
      throw _privateConstructorUsedError; // List<ProvisionMaterialModel>? materials,
  String? get contractNumber => throw _privateConstructorUsedError;
  DateTime? get contractDate => throw _privateConstructorUsedError;
  DateTime? get contractStartDate => throw _privateConstructorUsedError;
  DateTime? get contractEndDate => throw _privateConstructorUsedError;
  List<DateTime>? get contractDeliveryDates =>
      throw _privateConstructorUsedError;
  double? get contractPrice => throw _privateConstructorUsedError;
  FileModel? get document => throw _privateConstructorUsedError;
  double? get lotPrice => throw _privateConstructorUsedError;
  List<ProvisionPaymentDetailsModel>? get paymentDetails =>
      throw _privateConstructorUsedError;
  List<DeliveryModel>? get deliveries => throw _privateConstructorUsedError;
  List<DeliveryGroupModel>? get deliveryGroups =>
      throw _privateConstructorUsedError;

  /// Serializes this ProvisionLotModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionLotModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionLotModelCopyWith<ProvisionLotModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionLotModelCopyWith<$Res> {
  factory $ProvisionLotModelCopyWith(
          ProvisionLotModel value, $Res Function(ProvisionLotModel) then) =
      _$ProvisionLotModelCopyWithImpl<$Res, ProvisionLotModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      ProvisionsFilter? filterType,
      String? provisionName,
      ProvisionStatus? provisionStatus,
      ClientModel? supplier,
      List<ProvisionItemModel>? provisionItems,
      List<ProvisionContractItemModel>? contractItems,
      String? contractNumber,
      DateTime? contractDate,
      DateTime? contractStartDate,
      DateTime? contractEndDate,
      List<DateTime>? contractDeliveryDates,
      double? contractPrice,
      FileModel? document,
      double? lotPrice,
      List<ProvisionPaymentDetailsModel>? paymentDetails,
      List<DeliveryModel>? deliveries,
      List<DeliveryGroupModel>? deliveryGroups});
}

/// @nodoc
class _$ProvisionLotModelCopyWithImpl<$Res, $Val extends ProvisionLotModel>
    implements $ProvisionLotModelCopyWith<$Res> {
  _$ProvisionLotModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionLotModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? filterType = freezed,
    Object? provisionName = freezed,
    Object? provisionStatus = freezed,
    Object? supplier = freezed,
    Object? provisionItems = freezed,
    Object? contractItems = freezed,
    Object? contractNumber = freezed,
    Object? contractDate = freezed,
    Object? contractStartDate = freezed,
    Object? contractEndDate = freezed,
    Object? contractDeliveryDates = freezed,
    Object? contractPrice = freezed,
    Object? document = freezed,
    Object? lotPrice = freezed,
    Object? paymentDetails = freezed,
    Object? deliveries = freezed,
    Object? deliveryGroups = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      provisionName: freezed == provisionName
          ? _value.provisionName
          : provisionName // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionStatus: freezed == provisionStatus
          ? _value.provisionStatus
          : provisionStatus // ignore: cast_nullable_to_non_nullable
              as ProvisionStatus?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      provisionItems: freezed == provisionItems
          ? _value.provisionItems
          : provisionItems // ignore: cast_nullable_to_non_nullable
              as List<ProvisionItemModel>?,
      contractItems: freezed == contractItems
          ? _value.contractItems
          : contractItems // ignore: cast_nullable_to_non_nullable
              as List<ProvisionContractItemModel>?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractDate: freezed == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contractStartDate: freezed == contractStartDate
          ? _value.contractStartDate
          : contractStartDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contractEndDate: freezed == contractEndDate
          ? _value.contractEndDate
          : contractEndDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contractDeliveryDates: freezed == contractDeliveryDates
          ? _value.contractDeliveryDates
          : contractDeliveryDates // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      contractPrice: freezed == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      document: freezed == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as FileModel?,
      lotPrice: freezed == lotPrice
          ? _value.lotPrice
          : lotPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      paymentDetails: freezed == paymentDetails
          ? _value.paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<ProvisionPaymentDetailsModel>?,
      deliveries: freezed == deliveries
          ? _value.deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<DeliveryModel>?,
      deliveryGroups: freezed == deliveryGroups
          ? _value.deliveryGroups
          : deliveryGroups // ignore: cast_nullable_to_non_nullable
              as List<DeliveryGroupModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionLotModelImplCopyWith<$Res>
    implements $ProvisionLotModelCopyWith<$Res> {
  factory _$$ProvisionLotModelImplCopyWith(_$ProvisionLotModelImpl value,
          $Res Function(_$ProvisionLotModelImpl) then) =
      __$$ProvisionLotModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      ProvisionsFilter? filterType,
      String? provisionName,
      ProvisionStatus? provisionStatus,
      ClientModel? supplier,
      List<ProvisionItemModel>? provisionItems,
      List<ProvisionContractItemModel>? contractItems,
      String? contractNumber,
      DateTime? contractDate,
      DateTime? contractStartDate,
      DateTime? contractEndDate,
      List<DateTime>? contractDeliveryDates,
      double? contractPrice,
      FileModel? document,
      double? lotPrice,
      List<ProvisionPaymentDetailsModel>? paymentDetails,
      List<DeliveryModel>? deliveries,
      List<DeliveryGroupModel>? deliveryGroups});
}

/// @nodoc
class __$$ProvisionLotModelImplCopyWithImpl<$Res>
    extends _$ProvisionLotModelCopyWithImpl<$Res, _$ProvisionLotModelImpl>
    implements _$$ProvisionLotModelImplCopyWith<$Res> {
  __$$ProvisionLotModelImplCopyWithImpl(_$ProvisionLotModelImpl _value,
      $Res Function(_$ProvisionLotModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionLotModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? filterType = freezed,
    Object? provisionName = freezed,
    Object? provisionStatus = freezed,
    Object? supplier = freezed,
    Object? provisionItems = freezed,
    Object? contractItems = freezed,
    Object? contractNumber = freezed,
    Object? contractDate = freezed,
    Object? contractStartDate = freezed,
    Object? contractEndDate = freezed,
    Object? contractDeliveryDates = freezed,
    Object? contractPrice = freezed,
    Object? document = freezed,
    Object? lotPrice = freezed,
    Object? paymentDetails = freezed,
    Object? deliveries = freezed,
    Object? deliveryGroups = freezed,
  }) {
    return _then(_$ProvisionLotModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      provisionName: freezed == provisionName
          ? _value.provisionName
          : provisionName // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionStatus: freezed == provisionStatus
          ? _value.provisionStatus
          : provisionStatus // ignore: cast_nullable_to_non_nullable
              as ProvisionStatus?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as ClientModel?,
      provisionItems: freezed == provisionItems
          ? _value._provisionItems
          : provisionItems // ignore: cast_nullable_to_non_nullable
              as List<ProvisionItemModel>?,
      contractItems: freezed == contractItems
          ? _value._contractItems
          : contractItems // ignore: cast_nullable_to_non_nullable
              as List<ProvisionContractItemModel>?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractDate: freezed == contractDate
          ? _value.contractDate
          : contractDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contractStartDate: freezed == contractStartDate
          ? _value.contractStartDate
          : contractStartDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contractEndDate: freezed == contractEndDate
          ? _value.contractEndDate
          : contractEndDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      contractDeliveryDates: freezed == contractDeliveryDates
          ? _value._contractDeliveryDates
          : contractDeliveryDates // ignore: cast_nullable_to_non_nullable
              as List<DateTime>?,
      contractPrice: freezed == contractPrice
          ? _value.contractPrice
          : contractPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      document: freezed == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as FileModel?,
      lotPrice: freezed == lotPrice
          ? _value.lotPrice
          : lotPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      paymentDetails: freezed == paymentDetails
          ? _value._paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<ProvisionPaymentDetailsModel>?,
      deliveries: freezed == deliveries
          ? _value._deliveries
          : deliveries // ignore: cast_nullable_to_non_nullable
              as List<DeliveryModel>?,
      deliveryGroups: freezed == deliveryGroups
          ? _value._deliveryGroups
          : deliveryGroups // ignore: cast_nullable_to_non_nullable
              as List<DeliveryGroupModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionLotModelImpl implements _ProvisionLotModel {
  const _$ProvisionLotModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.filterType,
      this.provisionName,
      this.provisionStatus,
      this.supplier,
      final List<ProvisionItemModel>? provisionItems,
      final List<ProvisionContractItemModel>? contractItems,
      this.contractNumber,
      this.contractDate,
      this.contractStartDate,
      this.contractEndDate,
      final List<DateTime>? contractDeliveryDates,
      this.contractPrice,
      this.document,
      this.lotPrice,
      final List<ProvisionPaymentDetailsModel>? paymentDetails,
      final List<DeliveryModel>? deliveries,
      final List<DeliveryGroupModel>? deliveryGroups})
      : _provisionItems = provisionItems,
        _contractItems = contractItems,
        _contractDeliveryDates = contractDeliveryDates,
        _paymentDetails = paymentDetails,
        _deliveries = deliveries,
        _deliveryGroups = deliveryGroups;

  factory _$ProvisionLotModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionLotModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final ProvisionsFilter? filterType;
  @override
  final String? provisionName;
  @override
  final ProvisionStatus? provisionStatus;
  @override
  final ClientModel? supplier;
  final List<ProvisionItemModel>? _provisionItems;
  @override
  List<ProvisionItemModel>? get provisionItems {
    final value = _provisionItems;
    if (value == null) return null;
    if (_provisionItems is EqualUnmodifiableListView) return _provisionItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProvisionContractItemModel>? _contractItems;
  @override
  List<ProvisionContractItemModel>? get contractItems {
    final value = _contractItems;
    if (value == null) return null;
    if (_contractItems is EqualUnmodifiableListView) return _contractItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// List<ProvisionMaterialModel>? materials,
  @override
  final String? contractNumber;
  @override
  final DateTime? contractDate;
  @override
  final DateTime? contractStartDate;
  @override
  final DateTime? contractEndDate;
  final List<DateTime>? _contractDeliveryDates;
  @override
  List<DateTime>? get contractDeliveryDates {
    final value = _contractDeliveryDates;
    if (value == null) return null;
    if (_contractDeliveryDates is EqualUnmodifiableListView)
      return _contractDeliveryDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? contractPrice;
  @override
  final FileModel? document;
  @override
  final double? lotPrice;
  final List<ProvisionPaymentDetailsModel>? _paymentDetails;
  @override
  List<ProvisionPaymentDetailsModel>? get paymentDetails {
    final value = _paymentDetails;
    if (value == null) return null;
    if (_paymentDetails is EqualUnmodifiableListView) return _paymentDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<DeliveryModel>? _deliveries;
  @override
  List<DeliveryModel>? get deliveries {
    final value = _deliveries;
    if (value == null) return null;
    if (_deliveries is EqualUnmodifiableListView) return _deliveries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<DeliveryGroupModel>? _deliveryGroups;
  @override
  List<DeliveryGroupModel>? get deliveryGroups {
    final value = _deliveryGroups;
    if (value == null) return null;
    if (_deliveryGroups is EqualUnmodifiableListView) return _deliveryGroups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProvisionLotModel(id: $id, filterType: $filterType, provisionName: $provisionName, provisionStatus: $provisionStatus, supplier: $supplier, provisionItems: $provisionItems, contractItems: $contractItems, contractNumber: $contractNumber, contractDate: $contractDate, contractStartDate: $contractStartDate, contractEndDate: $contractEndDate, contractDeliveryDates: $contractDeliveryDates, contractPrice: $contractPrice, document: $document, lotPrice: $lotPrice, paymentDetails: $paymentDetails, deliveries: $deliveries, deliveryGroups: $deliveryGroups)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionLotModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.provisionName, provisionName) ||
                other.provisionName == provisionName) &&
            (identical(other.provisionStatus, provisionStatus) ||
                other.provisionStatus == provisionStatus) &&
            (identical(other.supplier, supplier) ||
                other.supplier == supplier) &&
            const DeepCollectionEquality()
                .equals(other._provisionItems, _provisionItems) &&
            const DeepCollectionEquality()
                .equals(other._contractItems, _contractItems) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.contractDate, contractDate) ||
                other.contractDate == contractDate) &&
            (identical(other.contractStartDate, contractStartDate) ||
                other.contractStartDate == contractStartDate) &&
            (identical(other.contractEndDate, contractEndDate) ||
                other.contractEndDate == contractEndDate) &&
            const DeepCollectionEquality()
                .equals(other._contractDeliveryDates, _contractDeliveryDates) &&
            (identical(other.contractPrice, contractPrice) ||
                other.contractPrice == contractPrice) &&
            (identical(other.document, document) ||
                other.document == document) &&
            (identical(other.lotPrice, lotPrice) ||
                other.lotPrice == lotPrice) &&
            const DeepCollectionEquality()
                .equals(other._paymentDetails, _paymentDetails) &&
            const DeepCollectionEquality()
                .equals(other._deliveries, _deliveries) &&
            const DeepCollectionEquality()
                .equals(other._deliveryGroups, _deliveryGroups));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      filterType,
      provisionName,
      provisionStatus,
      supplier,
      const DeepCollectionEquality().hash(_provisionItems),
      const DeepCollectionEquality().hash(_contractItems),
      contractNumber,
      contractDate,
      contractStartDate,
      contractEndDate,
      const DeepCollectionEquality().hash(_contractDeliveryDates),
      contractPrice,
      document,
      lotPrice,
      const DeepCollectionEquality().hash(_paymentDetails),
      const DeepCollectionEquality().hash(_deliveries),
      const DeepCollectionEquality().hash(_deliveryGroups));

  /// Create a copy of ProvisionLotModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionLotModelImplCopyWith<_$ProvisionLotModelImpl> get copyWith =>
      __$$ProvisionLotModelImplCopyWithImpl<_$ProvisionLotModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionLotModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionLotModel implements ProvisionLotModel {
  const factory _ProvisionLotModel(
          {@JsonKey(name: '_id') final String? id,
          final ProvisionsFilter? filterType,
          final String? provisionName,
          final ProvisionStatus? provisionStatus,
          final ClientModel? supplier,
          final List<ProvisionItemModel>? provisionItems,
          final List<ProvisionContractItemModel>? contractItems,
          final String? contractNumber,
          final DateTime? contractDate,
          final DateTime? contractStartDate,
          final DateTime? contractEndDate,
          final List<DateTime>? contractDeliveryDates,
          final double? contractPrice,
          final FileModel? document,
          final double? lotPrice,
          final List<ProvisionPaymentDetailsModel>? paymentDetails,
          final List<DeliveryModel>? deliveries,
          final List<DeliveryGroupModel>? deliveryGroups}) =
      _$ProvisionLotModelImpl;

  factory _ProvisionLotModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionLotModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  ProvisionsFilter? get filterType;
  @override
  String? get provisionName;
  @override
  ProvisionStatus? get provisionStatus;
  @override
  ClientModel? get supplier;
  @override
  List<ProvisionItemModel>? get provisionItems;
  @override
  List<ProvisionContractItemModel>?
      get contractItems; // List<ProvisionMaterialModel>? materials,
  @override
  String? get contractNumber;
  @override
  DateTime? get contractDate;
  @override
  DateTime? get contractStartDate;
  @override
  DateTime? get contractEndDate;
  @override
  List<DateTime>? get contractDeliveryDates;
  @override
  double? get contractPrice;
  @override
  FileModel? get document;
  @override
  double? get lotPrice;
  @override
  List<ProvisionPaymentDetailsModel>? get paymentDetails;
  @override
  List<DeliveryModel>? get deliveries;
  @override
  List<DeliveryGroupModel>? get deliveryGroups;

  /// Create a copy of ProvisionLotModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionLotModelImplCopyWith<_$ProvisionLotModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProvisionItemModel _$ProvisionItemModelFromJson(Map<String, dynamic> json) {
  return _ProvisionItem.fromJson(json);
}

/// @nodoc
mixin _$ProvisionItemModel {
  ProductModel? get product => throw _privateConstructorUsedError;
  NomenclatureModel? get material => throw _privateConstructorUsedError;
  String? get materialGroupId => throw _privateConstructorUsedError;
  String? get uniqueId => throw _privateConstructorUsedError;
  String? get provisionId => throw _privateConstructorUsedError; // String? id,
  @JsonKey(name: '_id')
  String? get id =>
      throw _privateConstructorUsedError; // String? materialRquirements,
  ParametersFeatureType? get featureType => throw _privateConstructorUsedError;
  double? get cooperationQuantity => throw _privateConstructorUsedError;
  double? get deliveryQuantity => throw _privateConstructorUsedError;
  double? get totalQuantity => throw _privateConstructorUsedError;
  double? get multipliedQuantity => throw _privateConstructorUsedError;
  List<AdjustmentModel>? get adjustments => throw _privateConstructorUsedError;
  List<ProvisionPaymentDetailsModel>? get paymentDetails =>
      throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ProvisionItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionItemModelCopyWith<ProvisionItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionItemModelCopyWith<$Res> {
  factory $ProvisionItemModelCopyWith(
          ProvisionItemModel value, $Res Function(ProvisionItemModel) then) =
      _$ProvisionItemModelCopyWithImpl<$Res, ProvisionItemModel>;
  @useResult
  $Res call(
      {ProductModel? product,
      NomenclatureModel? material,
      String? materialGroupId,
      String? uniqueId,
      String? provisionId,
      @JsonKey(name: '_id') String? id,
      ParametersFeatureType? featureType,
      double? cooperationQuantity,
      double? deliveryQuantity,
      double? totalQuantity,
      double? multipliedQuantity,
      List<AdjustmentModel>? adjustments,
      List<ProvisionPaymentDetailsModel>? paymentDetails,
      DateTime? createdAt,
      DateTime? updatedAt});

  $ProductModelCopyWith<$Res>? get product;
}

/// @nodoc
class _$ProvisionItemModelCopyWithImpl<$Res, $Val extends ProvisionItemModel>
    implements $ProvisionItemModelCopyWith<$Res> {
  _$ProvisionItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? product = freezed,
    Object? material = freezed,
    Object? materialGroupId = freezed,
    Object? uniqueId = freezed,
    Object? provisionId = freezed,
    Object? id = freezed,
    Object? featureType = freezed,
    Object? cooperationQuantity = freezed,
    Object? deliveryQuantity = freezed,
    Object? totalQuantity = freezed,
    Object? multipliedQuantity = freezed,
    Object? adjustments = freezed,
    Object? paymentDetails = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      materialGroupId: freezed == materialGroupId
          ? _value.materialGroupId
          : materialGroupId // ignore: cast_nullable_to_non_nullable
              as String?,
      uniqueId: freezed == uniqueId
          ? _value.uniqueId
          : uniqueId // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      cooperationQuantity: freezed == cooperationQuantity
          ? _value.cooperationQuantity
          : cooperationQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      deliveryQuantity: freezed == deliveryQuantity
          ? _value.deliveryQuantity
          : deliveryQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      multipliedQuantity: freezed == multipliedQuantity
          ? _value.multipliedQuantity
          : multipliedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      adjustments: freezed == adjustments
          ? _value.adjustments
          : adjustments // ignore: cast_nullable_to_non_nullable
              as List<AdjustmentModel>?,
      paymentDetails: freezed == paymentDetails
          ? _value.paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<ProvisionPaymentDetailsModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of ProvisionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get product {
    if (_value.product == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.product!, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProvisionItemImplCopyWith<$Res>
    implements $ProvisionItemModelCopyWith<$Res> {
  factory _$$ProvisionItemImplCopyWith(
          _$ProvisionItemImpl value, $Res Function(_$ProvisionItemImpl) then) =
      __$$ProvisionItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ProductModel? product,
      NomenclatureModel? material,
      String? materialGroupId,
      String? uniqueId,
      String? provisionId,
      @JsonKey(name: '_id') String? id,
      ParametersFeatureType? featureType,
      double? cooperationQuantity,
      double? deliveryQuantity,
      double? totalQuantity,
      double? multipliedQuantity,
      List<AdjustmentModel>? adjustments,
      List<ProvisionPaymentDetailsModel>? paymentDetails,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $ProductModelCopyWith<$Res>? get product;
}

/// @nodoc
class __$$ProvisionItemImplCopyWithImpl<$Res>
    extends _$ProvisionItemModelCopyWithImpl<$Res, _$ProvisionItemImpl>
    implements _$$ProvisionItemImplCopyWith<$Res> {
  __$$ProvisionItemImplCopyWithImpl(
      _$ProvisionItemImpl _value, $Res Function(_$ProvisionItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? product = freezed,
    Object? material = freezed,
    Object? materialGroupId = freezed,
    Object? uniqueId = freezed,
    Object? provisionId = freezed,
    Object? id = freezed,
    Object? featureType = freezed,
    Object? cooperationQuantity = freezed,
    Object? deliveryQuantity = freezed,
    Object? totalQuantity = freezed,
    Object? multipliedQuantity = freezed,
    Object? adjustments = freezed,
    Object? paymentDetails = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ProvisionItemImpl(
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      materialGroupId: freezed == materialGroupId
          ? _value.materialGroupId
          : materialGroupId // ignore: cast_nullable_to_non_nullable
              as String?,
      uniqueId: freezed == uniqueId
          ? _value.uniqueId
          : uniqueId // ignore: cast_nullable_to_non_nullable
              as String?,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      cooperationQuantity: freezed == cooperationQuantity
          ? _value.cooperationQuantity
          : cooperationQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      deliveryQuantity: freezed == deliveryQuantity
          ? _value.deliveryQuantity
          : deliveryQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      multipliedQuantity: freezed == multipliedQuantity
          ? _value.multipliedQuantity
          : multipliedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      adjustments: freezed == adjustments
          ? _value._adjustments
          : adjustments // ignore: cast_nullable_to_non_nullable
              as List<AdjustmentModel>?,
      paymentDetails: freezed == paymentDetails
          ? _value._paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as List<ProvisionPaymentDetailsModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionItemImpl implements _ProvisionItem {
  const _$ProvisionItemImpl(
      {this.product,
      this.material,
      this.materialGroupId,
      this.uniqueId,
      this.provisionId,
      @JsonKey(name: '_id') this.id,
      this.featureType,
      this.cooperationQuantity,
      this.deliveryQuantity,
      this.totalQuantity,
      this.multipliedQuantity,
      final List<AdjustmentModel>? adjustments,
      final List<ProvisionPaymentDetailsModel>? paymentDetails,
      this.createdAt,
      this.updatedAt})
      : _adjustments = adjustments,
        _paymentDetails = paymentDetails;

  factory _$ProvisionItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionItemImplFromJson(json);

  @override
  final ProductModel? product;
  @override
  final NomenclatureModel? material;
  @override
  final String? materialGroupId;
  @override
  final String? uniqueId;
  @override
  final String? provisionId;
// String? id,
  @override
  @JsonKey(name: '_id')
  final String? id;
// String? materialRquirements,
  @override
  final ParametersFeatureType? featureType;
  @override
  final double? cooperationQuantity;
  @override
  final double? deliveryQuantity;
  @override
  final double? totalQuantity;
  @override
  final double? multipliedQuantity;
  final List<AdjustmentModel>? _adjustments;
  @override
  List<AdjustmentModel>? get adjustments {
    final value = _adjustments;
    if (value == null) return null;
    if (_adjustments is EqualUnmodifiableListView) return _adjustments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProvisionPaymentDetailsModel>? _paymentDetails;
  @override
  List<ProvisionPaymentDetailsModel>? get paymentDetails {
    final value = _paymentDetails;
    if (value == null) return null;
    if (_paymentDetails is EqualUnmodifiableListView) return _paymentDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ProvisionItemModel(product: $product, material: $material, materialGroupId: $materialGroupId, uniqueId: $uniqueId, provisionId: $provisionId, id: $id, featureType: $featureType, cooperationQuantity: $cooperationQuantity, deliveryQuantity: $deliveryQuantity, totalQuantity: $totalQuantity, multipliedQuantity: $multipliedQuantity, adjustments: $adjustments, paymentDetails: $paymentDetails, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionItemImpl &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.material, material) ||
                other.material == material) &&
            (identical(other.materialGroupId, materialGroupId) ||
                other.materialGroupId == materialGroupId) &&
            (identical(other.uniqueId, uniqueId) ||
                other.uniqueId == uniqueId) &&
            (identical(other.provisionId, provisionId) ||
                other.provisionId == provisionId) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.featureType, featureType) ||
                other.featureType == featureType) &&
            (identical(other.cooperationQuantity, cooperationQuantity) ||
                other.cooperationQuantity == cooperationQuantity) &&
            (identical(other.deliveryQuantity, deliveryQuantity) ||
                other.deliveryQuantity == deliveryQuantity) &&
            (identical(other.totalQuantity, totalQuantity) ||
                other.totalQuantity == totalQuantity) &&
            (identical(other.multipliedQuantity, multipliedQuantity) ||
                other.multipliedQuantity == multipliedQuantity) &&
            const DeepCollectionEquality()
                .equals(other._adjustments, _adjustments) &&
            const DeepCollectionEquality()
                .equals(other._paymentDetails, _paymentDetails) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      product,
      material,
      materialGroupId,
      uniqueId,
      provisionId,
      id,
      featureType,
      cooperationQuantity,
      deliveryQuantity,
      totalQuantity,
      multipliedQuantity,
      const DeepCollectionEquality().hash(_adjustments),
      const DeepCollectionEquality().hash(_paymentDetails),
      createdAt,
      updatedAt);

  /// Create a copy of ProvisionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionItemImplCopyWith<_$ProvisionItemImpl> get copyWith =>
      __$$ProvisionItemImplCopyWithImpl<_$ProvisionItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionItemImplToJson(
      this,
    );
  }
}

abstract class _ProvisionItem implements ProvisionItemModel {
  const factory _ProvisionItem(
      {final ProductModel? product,
      final NomenclatureModel? material,
      final String? materialGroupId,
      final String? uniqueId,
      final String? provisionId,
      @JsonKey(name: '_id') final String? id,
      final ParametersFeatureType? featureType,
      final double? cooperationQuantity,
      final double? deliveryQuantity,
      final double? totalQuantity,
      final double? multipliedQuantity,
      final List<AdjustmentModel>? adjustments,
      final List<ProvisionPaymentDetailsModel>? paymentDetails,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ProvisionItemImpl;

  factory _ProvisionItem.fromJson(Map<String, dynamic> json) =
      _$ProvisionItemImpl.fromJson;

  @override
  ProductModel? get product;
  @override
  NomenclatureModel? get material;
  @override
  String? get materialGroupId;
  @override
  String? get uniqueId;
  @override
  String? get provisionId; // String? id,
  @override
  @JsonKey(name: '_id')
  String? get id; // String? materialRquirements,
  @override
  ParametersFeatureType? get featureType;
  @override
  double? get cooperationQuantity;
  @override
  double? get deliveryQuantity;
  @override
  double? get totalQuantity;
  @override
  double? get multipliedQuantity;
  @override
  List<AdjustmentModel>? get adjustments;
  @override
  List<ProvisionPaymentDetailsModel>? get paymentDetails;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ProvisionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionItemImplCopyWith<_$ProvisionItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProvisionContractItemModel _$ProvisionContractItemModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionContractItemModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionContractItemModel {
  String? get materialId => throw _privateConstructorUsedError;
  String? get materialRequirements => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  ParametersFeatureType? get featureType => throw _privateConstructorUsedError;
  bool? get needMaterial => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;

  /// Serializes this ProvisionContractItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionContractItemModelCopyWith<ProvisionContractItemModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionContractItemModelCopyWith<$Res> {
  factory $ProvisionContractItemModelCopyWith(ProvisionContractItemModel value,
          $Res Function(ProvisionContractItemModel) then) =
      _$ProvisionContractItemModelCopyWithImpl<$Res,
          ProvisionContractItemModel>;
  @useResult
  $Res call(
      {String? materialId,
      String? materialRequirements,
      String? productId,
      ParametersFeatureType? featureType,
      bool? needMaterial,
      double? price,
      double? quantity});
}

/// @nodoc
class _$ProvisionContractItemModelCopyWithImpl<$Res,
        $Val extends ProvisionContractItemModel>
    implements $ProvisionContractItemModelCopyWith<$Res> {
  _$ProvisionContractItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? materialRequirements = freezed,
    Object? productId = freezed,
    Object? featureType = freezed,
    Object? needMaterial = freezed,
    Object? price = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_value.copyWith(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      needMaterial: freezed == needMaterial
          ? _value.needMaterial
          : needMaterial // ignore: cast_nullable_to_non_nullable
              as bool?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionContractItemModelImplCopyWith<$Res>
    implements $ProvisionContractItemModelCopyWith<$Res> {
  factory _$$ProvisionContractItemModelImplCopyWith(
          _$ProvisionContractItemModelImpl value,
          $Res Function(_$ProvisionContractItemModelImpl) then) =
      __$$ProvisionContractItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? materialId,
      String? materialRequirements,
      String? productId,
      ParametersFeatureType? featureType,
      bool? needMaterial,
      double? price,
      double? quantity});
}

/// @nodoc
class __$$ProvisionContractItemModelImplCopyWithImpl<$Res>
    extends _$ProvisionContractItemModelCopyWithImpl<$Res,
        _$ProvisionContractItemModelImpl>
    implements _$$ProvisionContractItemModelImplCopyWith<$Res> {
  __$$ProvisionContractItemModelImplCopyWithImpl(
      _$ProvisionContractItemModelImpl _value,
      $Res Function(_$ProvisionContractItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? materialRequirements = freezed,
    Object? productId = freezed,
    Object? featureType = freezed,
    Object? needMaterial = freezed,
    Object? price = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_$ProvisionContractItemModelImpl(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      needMaterial: freezed == needMaterial
          ? _value.needMaterial
          : needMaterial // ignore: cast_nullable_to_non_nullable
              as bool?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionContractItemModelImpl implements _ProvisionContractItemModel {
  const _$ProvisionContractItemModelImpl(
      {this.materialId,
      this.materialRequirements,
      this.productId,
      this.featureType,
      this.needMaterial,
      this.price,
      this.quantity});

  factory _$ProvisionContractItemModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProvisionContractItemModelImplFromJson(json);

  @override
  final String? materialId;
  @override
  final String? materialRequirements;
  @override
  final String? productId;
  @override
  final ParametersFeatureType? featureType;
  @override
  final bool? needMaterial;
  @override
  final double? price;
  @override
  final double? quantity;

  @override
  String toString() {
    return 'ProvisionContractItemModel(materialId: $materialId, materialRequirements: $materialRequirements, productId: $productId, featureType: $featureType, needMaterial: $needMaterial, price: $price, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionContractItemModelImpl &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.materialRequirements, materialRequirements) ||
                other.materialRequirements == materialRequirements) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.featureType, featureType) ||
                other.featureType == featureType) &&
            (identical(other.needMaterial, needMaterial) ||
                other.needMaterial == needMaterial) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, materialId, materialRequirements,
      productId, featureType, needMaterial, price, quantity);

  /// Create a copy of ProvisionContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionContractItemModelImplCopyWith<_$ProvisionContractItemModelImpl>
      get copyWith => __$$ProvisionContractItemModelImplCopyWithImpl<
          _$ProvisionContractItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionContractItemModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionContractItemModel
    implements ProvisionContractItemModel {
  const factory _ProvisionContractItemModel(
      {final String? materialId,
      final String? materialRequirements,
      final String? productId,
      final ParametersFeatureType? featureType,
      final bool? needMaterial,
      final double? price,
      final double? quantity}) = _$ProvisionContractItemModelImpl;

  factory _ProvisionContractItemModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionContractItemModelImpl.fromJson;

  @override
  String? get materialId;
  @override
  String? get materialRequirements;
  @override
  String? get productId;
  @override
  ParametersFeatureType? get featureType;
  @override
  bool? get needMaterial;
  @override
  double? get price;
  @override
  double? get quantity;

  /// Create a copy of ProvisionContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionContractItemModelImplCopyWith<_$ProvisionContractItemModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionPaymentDetailsModel _$ProvisionPaymentDetailsModelFromJson(
    Map<String, dynamic> json) {
  return _ProvisionPaymentDetails.fromJson(json);
}

/// @nodoc
mixin _$ProvisionPaymentDetailsModel {
  DateTime? get paymentDate => throw _privateConstructorUsedError;
  double? get amount => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;

  /// Serializes this ProvisionPaymentDetailsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionPaymentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionPaymentDetailsModelCopyWith<ProvisionPaymentDetailsModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionPaymentDetailsModelCopyWith<$Res> {
  factory $ProvisionPaymentDetailsModelCopyWith(
          ProvisionPaymentDetailsModel value,
          $Res Function(ProvisionPaymentDetailsModel) then) =
      _$ProvisionPaymentDetailsModelCopyWithImpl<$Res,
          ProvisionPaymentDetailsModel>;
  @useResult
  $Res call({DateTime? paymentDate, double? amount, String? description});
}

/// @nodoc
class _$ProvisionPaymentDetailsModelCopyWithImpl<$Res,
        $Val extends ProvisionPaymentDetailsModel>
    implements $ProvisionPaymentDetailsModelCopyWith<$Res> {
  _$ProvisionPaymentDetailsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionPaymentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentDate = freezed,
    Object? amount = freezed,
    Object? description = freezed,
  }) {
    return _then(_value.copyWith(
      paymentDate: freezed == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionPaymentDetailsImplCopyWith<$Res>
    implements $ProvisionPaymentDetailsModelCopyWith<$Res> {
  factory _$$ProvisionPaymentDetailsImplCopyWith(
          _$ProvisionPaymentDetailsImpl value,
          $Res Function(_$ProvisionPaymentDetailsImpl) then) =
      __$$ProvisionPaymentDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime? paymentDate, double? amount, String? description});
}

/// @nodoc
class __$$ProvisionPaymentDetailsImplCopyWithImpl<$Res>
    extends _$ProvisionPaymentDetailsModelCopyWithImpl<$Res,
        _$ProvisionPaymentDetailsImpl>
    implements _$$ProvisionPaymentDetailsImplCopyWith<$Res> {
  __$$ProvisionPaymentDetailsImplCopyWithImpl(
      _$ProvisionPaymentDetailsImpl _value,
      $Res Function(_$ProvisionPaymentDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionPaymentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentDate = freezed,
    Object? amount = freezed,
    Object? description = freezed,
  }) {
    return _then(_$ProvisionPaymentDetailsImpl(
      paymentDate: freezed == paymentDate
          ? _value.paymentDate
          : paymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionPaymentDetailsImpl implements _ProvisionPaymentDetails {
  const _$ProvisionPaymentDetailsImpl(
      {this.paymentDate, this.amount, this.description});

  factory _$ProvisionPaymentDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionPaymentDetailsImplFromJson(json);

  @override
  final DateTime? paymentDate;
  @override
  final double? amount;
  @override
  final String? description;

  @override
  String toString() {
    return 'ProvisionPaymentDetailsModel(paymentDate: $paymentDate, amount: $amount, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionPaymentDetailsImpl &&
            (identical(other.paymentDate, paymentDate) ||
                other.paymentDate == paymentDate) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, paymentDate, amount, description);

  /// Create a copy of ProvisionPaymentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionPaymentDetailsImplCopyWith<_$ProvisionPaymentDetailsImpl>
      get copyWith => __$$ProvisionPaymentDetailsImplCopyWithImpl<
          _$ProvisionPaymentDetailsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionPaymentDetailsImplToJson(
      this,
    );
  }
}

abstract class _ProvisionPaymentDetails
    implements ProvisionPaymentDetailsModel {
  const factory _ProvisionPaymentDetails(
      {final DateTime? paymentDate,
      final double? amount,
      final String? description}) = _$ProvisionPaymentDetailsImpl;

  factory _ProvisionPaymentDetails.fromJson(Map<String, dynamic> json) =
      _$ProvisionPaymentDetailsImpl.fromJson;

  @override
  DateTime? get paymentDate;
  @override
  double? get amount;
  @override
  String? get description;

  /// Create a copy of ProvisionPaymentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionPaymentDetailsImplCopyWith<_$ProvisionPaymentDetailsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProvisionsListModel _$ProvisionsListModelFromJson(Map<String, dynamic> json) {
  return _ProvisionsListModel.fromJson(json);
}

/// @nodoc
mixin _$ProvisionsListModel {
  List<ProvisionLotModel>? get lots => throw _privateConstructorUsedError;
  List<ProvisionItemModel>? get unallocatedItems =>
      throw _privateConstructorUsedError;

  /// Serializes this ProvisionsListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionsListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionsListModelCopyWith<ProvisionsListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionsListModelCopyWith<$Res> {
  factory $ProvisionsListModelCopyWith(
          ProvisionsListModel value, $Res Function(ProvisionsListModel) then) =
      _$ProvisionsListModelCopyWithImpl<$Res, ProvisionsListModel>;
  @useResult
  $Res call(
      {List<ProvisionLotModel>? lots,
      List<ProvisionItemModel>? unallocatedItems});
}

/// @nodoc
class _$ProvisionsListModelCopyWithImpl<$Res, $Val extends ProvisionsListModel>
    implements $ProvisionsListModelCopyWith<$Res> {
  _$ProvisionsListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionsListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lots = freezed,
    Object? unallocatedItems = freezed,
  }) {
    return _then(_value.copyWith(
      lots: freezed == lots
          ? _value.lots
          : lots // ignore: cast_nullable_to_non_nullable
              as List<ProvisionLotModel>?,
      unallocatedItems: freezed == unallocatedItems
          ? _value.unallocatedItems
          : unallocatedItems // ignore: cast_nullable_to_non_nullable
              as List<ProvisionItemModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionsListModelImplCopyWith<$Res>
    implements $ProvisionsListModelCopyWith<$Res> {
  factory _$$ProvisionsListModelImplCopyWith(_$ProvisionsListModelImpl value,
          $Res Function(_$ProvisionsListModelImpl) then) =
      __$$ProvisionsListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ProvisionLotModel>? lots,
      List<ProvisionItemModel>? unallocatedItems});
}

/// @nodoc
class __$$ProvisionsListModelImplCopyWithImpl<$Res>
    extends _$ProvisionsListModelCopyWithImpl<$Res, _$ProvisionsListModelImpl>
    implements _$$ProvisionsListModelImplCopyWith<$Res> {
  __$$ProvisionsListModelImplCopyWithImpl(_$ProvisionsListModelImpl _value,
      $Res Function(_$ProvisionsListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionsListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lots = freezed,
    Object? unallocatedItems = freezed,
  }) {
    return _then(_$ProvisionsListModelImpl(
      lots: freezed == lots
          ? _value._lots
          : lots // ignore: cast_nullable_to_non_nullable
              as List<ProvisionLotModel>?,
      unallocatedItems: freezed == unallocatedItems
          ? _value._unallocatedItems
          : unallocatedItems // ignore: cast_nullable_to_non_nullable
              as List<ProvisionItemModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProvisionsListModelImpl implements _ProvisionsListModel {
  const _$ProvisionsListModelImpl(
      {final List<ProvisionLotModel>? lots,
      final List<ProvisionItemModel>? unallocatedItems})
      : _lots = lots,
        _unallocatedItems = unallocatedItems;

  factory _$ProvisionsListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionsListModelImplFromJson(json);

  final List<ProvisionLotModel>? _lots;
  @override
  List<ProvisionLotModel>? get lots {
    final value = _lots;
    if (value == null) return null;
    if (_lots is EqualUnmodifiableListView) return _lots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProvisionItemModel>? _unallocatedItems;
  @override
  List<ProvisionItemModel>? get unallocatedItems {
    final value = _unallocatedItems;
    if (value == null) return null;
    if (_unallocatedItems is EqualUnmodifiableListView)
      return _unallocatedItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProvisionsListModel(lots: $lots, unallocatedItems: $unallocatedItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionsListModelImpl &&
            const DeepCollectionEquality().equals(other._lots, _lots) &&
            const DeepCollectionEquality()
                .equals(other._unallocatedItems, _unallocatedItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_lots),
      const DeepCollectionEquality().hash(_unallocatedItems));

  /// Create a copy of ProvisionsListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionsListModelImplCopyWith<_$ProvisionsListModelImpl> get copyWith =>
      __$$ProvisionsListModelImplCopyWithImpl<_$ProvisionsListModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionsListModelImplToJson(
      this,
    );
  }
}

abstract class _ProvisionsListModel implements ProvisionsListModel {
  const factory _ProvisionsListModel(
          {final List<ProvisionLotModel>? lots,
          final List<ProvisionItemModel>? unallocatedItems}) =
      _$ProvisionsListModelImpl;

  factory _ProvisionsListModel.fromJson(Map<String, dynamic> json) =
      _$ProvisionsListModelImpl.fromJson;

  @override
  List<ProvisionLotModel>? get lots;
  @override
  List<ProvisionItemModel>? get unallocatedItems;

  /// Create a copy of ProvisionsListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionsListModelImplCopyWith<_$ProvisionsListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AdjustmentModel _$AdjustmentModelFromJson(Map<String, dynamic> json) {
  return _AdjustmentModel.fromJson(json);
}

/// @nodoc
mixin _$AdjustmentModel {
  AdjustmentType? get type => throw _privateConstructorUsedError;
  DateTime? get date => throw _privateConstructorUsedError;
  AdjustmentDetailsModel? get details => throw _privateConstructorUsedError;

  /// Serializes this AdjustmentModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdjustmentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdjustmentModelCopyWith<AdjustmentModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdjustmentModelCopyWith<$Res> {
  factory $AdjustmentModelCopyWith(
          AdjustmentModel value, $Res Function(AdjustmentModel) then) =
      _$AdjustmentModelCopyWithImpl<$Res, AdjustmentModel>;
  @useResult
  $Res call(
      {AdjustmentType? type, DateTime? date, AdjustmentDetailsModel? details});

  $AdjustmentDetailsModelCopyWith<$Res>? get details;
}

/// @nodoc
class _$AdjustmentModelCopyWithImpl<$Res, $Val extends AdjustmentModel>
    implements $AdjustmentModelCopyWith<$Res> {
  _$AdjustmentModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdjustmentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? date = freezed,
    Object? details = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AdjustmentType?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as AdjustmentDetailsModel?,
    ) as $Val);
  }

  /// Create a copy of AdjustmentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AdjustmentDetailsModelCopyWith<$Res>? get details {
    if (_value.details == null) {
      return null;
    }

    return $AdjustmentDetailsModelCopyWith<$Res>(_value.details!, (value) {
      return _then(_value.copyWith(details: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AdjustmentModelImplCopyWith<$Res>
    implements $AdjustmentModelCopyWith<$Res> {
  factory _$$AdjustmentModelImplCopyWith(_$AdjustmentModelImpl value,
          $Res Function(_$AdjustmentModelImpl) then) =
      __$$AdjustmentModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AdjustmentType? type, DateTime? date, AdjustmentDetailsModel? details});

  @override
  $AdjustmentDetailsModelCopyWith<$Res>? get details;
}

/// @nodoc
class __$$AdjustmentModelImplCopyWithImpl<$Res>
    extends _$AdjustmentModelCopyWithImpl<$Res, _$AdjustmentModelImpl>
    implements _$$AdjustmentModelImplCopyWith<$Res> {
  __$$AdjustmentModelImplCopyWithImpl(
      _$AdjustmentModelImpl _value, $Res Function(_$AdjustmentModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdjustmentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? date = freezed,
    Object? details = freezed,
  }) {
    return _then(_$AdjustmentModelImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AdjustmentType?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as AdjustmentDetailsModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$AdjustmentModelImpl implements _AdjustmentModel {
  const _$AdjustmentModelImpl({this.type, this.date, this.details});

  factory _$AdjustmentModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdjustmentModelImplFromJson(json);

  @override
  final AdjustmentType? type;
  @override
  final DateTime? date;
  @override
  final AdjustmentDetailsModel? details;

  @override
  String toString() {
    return 'AdjustmentModel(type: $type, date: $date, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdjustmentModelImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.details, details) || other.details == details));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, type, date, details);

  /// Create a copy of AdjustmentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdjustmentModelImplCopyWith<_$AdjustmentModelImpl> get copyWith =>
      __$$AdjustmentModelImplCopyWithImpl<_$AdjustmentModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdjustmentModelImplToJson(
      this,
    );
  }
}

abstract class _AdjustmentModel implements AdjustmentModel {
  const factory _AdjustmentModel(
      {final AdjustmentType? type,
      final DateTime? date,
      final AdjustmentDetailsModel? details}) = _$AdjustmentModelImpl;

  factory _AdjustmentModel.fromJson(Map<String, dynamic> json) =
      _$AdjustmentModelImpl.fromJson;

  @override
  AdjustmentType? get type;
  @override
  DateTime? get date;
  @override
  AdjustmentDetailsModel? get details;

  /// Create a copy of AdjustmentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdjustmentModelImplCopyWith<_$AdjustmentModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AdjustmentDetailsModel _$AdjustmentDetailsModelFromJson(
    Map<String, dynamic> json) {
  return _AdjustmentDetailsModel.fromJson(json);
}

/// @nodoc
mixin _$AdjustmentDetailsModel {
  String? get description => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  double? get oldQuantity => throw _privateConstructorUsedError;
  double? get newQuantity => throw _privateConstructorUsedError;
  UnitType? get unitType => throw _privateConstructorUsedError;
  String? get oldMaterialId => throw _privateConstructorUsedError;
  String? get oldMaterialName => throw _privateConstructorUsedError;
  String? get newMaterialId => throw _privateConstructorUsedError;
  String? get newMaterialName => throw _privateConstructorUsedError;
  String? get assemblyId => throw _privateConstructorUsedError;
  String? get assemblyName => throw _privateConstructorUsedError;
  double? get oldAssemblyQuantity => throw _privateConstructorUsedError;
  double? get newAssemblyQuantity => throw _privateConstructorUsedError;
  double? get quantityAdjusted => throw _privateConstructorUsedError;
  bool? get removedFromLot => throw _privateConstructorUsedError;

  /// Serializes this AdjustmentDetailsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdjustmentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdjustmentDetailsModelCopyWith<AdjustmentDetailsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdjustmentDetailsModelCopyWith<$Res> {
  factory $AdjustmentDetailsModelCopyWith(AdjustmentDetailsModel value,
          $Res Function(AdjustmentDetailsModel) then) =
      _$AdjustmentDetailsModelCopyWithImpl<$Res, AdjustmentDetailsModel>;
  @useResult
  $Res call(
      {String? description,
      String? productId,
      String? productName,
      double? oldQuantity,
      double? newQuantity,
      UnitType? unitType,
      String? oldMaterialId,
      String? oldMaterialName,
      String? newMaterialId,
      String? newMaterialName,
      String? assemblyId,
      String? assemblyName,
      double? oldAssemblyQuantity,
      double? newAssemblyQuantity,
      double? quantityAdjusted,
      bool? removedFromLot});
}

/// @nodoc
class _$AdjustmentDetailsModelCopyWithImpl<$Res,
        $Val extends AdjustmentDetailsModel>
    implements $AdjustmentDetailsModelCopyWith<$Res> {
  _$AdjustmentDetailsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdjustmentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? productId = freezed,
    Object? productName = freezed,
    Object? oldQuantity = freezed,
    Object? newQuantity = freezed,
    Object? unitType = freezed,
    Object? oldMaterialId = freezed,
    Object? oldMaterialName = freezed,
    Object? newMaterialId = freezed,
    Object? newMaterialName = freezed,
    Object? assemblyId = freezed,
    Object? assemblyName = freezed,
    Object? oldAssemblyQuantity = freezed,
    Object? newAssemblyQuantity = freezed,
    Object? quantityAdjusted = freezed,
    Object? removedFromLot = freezed,
  }) {
    return _then(_value.copyWith(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      oldQuantity: freezed == oldQuantity
          ? _value.oldQuantity
          : oldQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      newQuantity: freezed == newQuantity
          ? _value.newQuantity
          : newQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      oldMaterialId: freezed == oldMaterialId
          ? _value.oldMaterialId
          : oldMaterialId // ignore: cast_nullable_to_non_nullable
              as String?,
      oldMaterialName: freezed == oldMaterialName
          ? _value.oldMaterialName
          : oldMaterialName // ignore: cast_nullable_to_non_nullable
              as String?,
      newMaterialId: freezed == newMaterialId
          ? _value.newMaterialId
          : newMaterialId // ignore: cast_nullable_to_non_nullable
              as String?,
      newMaterialName: freezed == newMaterialName
          ? _value.newMaterialName
          : newMaterialName // ignore: cast_nullable_to_non_nullable
              as String?,
      assemblyId: freezed == assemblyId
          ? _value.assemblyId
          : assemblyId // ignore: cast_nullable_to_non_nullable
              as String?,
      assemblyName: freezed == assemblyName
          ? _value.assemblyName
          : assemblyName // ignore: cast_nullable_to_non_nullable
              as String?,
      oldAssemblyQuantity: freezed == oldAssemblyQuantity
          ? _value.oldAssemblyQuantity
          : oldAssemblyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      newAssemblyQuantity: freezed == newAssemblyQuantity
          ? _value.newAssemblyQuantity
          : newAssemblyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityAdjusted: freezed == quantityAdjusted
          ? _value.quantityAdjusted
          : quantityAdjusted // ignore: cast_nullable_to_non_nullable
              as double?,
      removedFromLot: freezed == removedFromLot
          ? _value.removedFromLot
          : removedFromLot // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdjustmentDetailsModelImplCopyWith<$Res>
    implements $AdjustmentDetailsModelCopyWith<$Res> {
  factory _$$AdjustmentDetailsModelImplCopyWith(
          _$AdjustmentDetailsModelImpl value,
          $Res Function(_$AdjustmentDetailsModelImpl) then) =
      __$$AdjustmentDetailsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? description,
      String? productId,
      String? productName,
      double? oldQuantity,
      double? newQuantity,
      UnitType? unitType,
      String? oldMaterialId,
      String? oldMaterialName,
      String? newMaterialId,
      String? newMaterialName,
      String? assemblyId,
      String? assemblyName,
      double? oldAssemblyQuantity,
      double? newAssemblyQuantity,
      double? quantityAdjusted,
      bool? removedFromLot});
}

/// @nodoc
class __$$AdjustmentDetailsModelImplCopyWithImpl<$Res>
    extends _$AdjustmentDetailsModelCopyWithImpl<$Res,
        _$AdjustmentDetailsModelImpl>
    implements _$$AdjustmentDetailsModelImplCopyWith<$Res> {
  __$$AdjustmentDetailsModelImplCopyWithImpl(
      _$AdjustmentDetailsModelImpl _value,
      $Res Function(_$AdjustmentDetailsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdjustmentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? description = freezed,
    Object? productId = freezed,
    Object? productName = freezed,
    Object? oldQuantity = freezed,
    Object? newQuantity = freezed,
    Object? unitType = freezed,
    Object? oldMaterialId = freezed,
    Object? oldMaterialName = freezed,
    Object? newMaterialId = freezed,
    Object? newMaterialName = freezed,
    Object? assemblyId = freezed,
    Object? assemblyName = freezed,
    Object? oldAssemblyQuantity = freezed,
    Object? newAssemblyQuantity = freezed,
    Object? quantityAdjusted = freezed,
    Object? removedFromLot = freezed,
  }) {
    return _then(_$AdjustmentDetailsModelImpl(
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      oldQuantity: freezed == oldQuantity
          ? _value.oldQuantity
          : oldQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      newQuantity: freezed == newQuantity
          ? _value.newQuantity
          : newQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      oldMaterialId: freezed == oldMaterialId
          ? _value.oldMaterialId
          : oldMaterialId // ignore: cast_nullable_to_non_nullable
              as String?,
      oldMaterialName: freezed == oldMaterialName
          ? _value.oldMaterialName
          : oldMaterialName // ignore: cast_nullable_to_non_nullable
              as String?,
      newMaterialId: freezed == newMaterialId
          ? _value.newMaterialId
          : newMaterialId // ignore: cast_nullable_to_non_nullable
              as String?,
      newMaterialName: freezed == newMaterialName
          ? _value.newMaterialName
          : newMaterialName // ignore: cast_nullable_to_non_nullable
              as String?,
      assemblyId: freezed == assemblyId
          ? _value.assemblyId
          : assemblyId // ignore: cast_nullable_to_non_nullable
              as String?,
      assemblyName: freezed == assemblyName
          ? _value.assemblyName
          : assemblyName // ignore: cast_nullable_to_non_nullable
              as String?,
      oldAssemblyQuantity: freezed == oldAssemblyQuantity
          ? _value.oldAssemblyQuantity
          : oldAssemblyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      newAssemblyQuantity: freezed == newAssemblyQuantity
          ? _value.newAssemblyQuantity
          : newAssemblyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityAdjusted: freezed == quantityAdjusted
          ? _value.quantityAdjusted
          : quantityAdjusted // ignore: cast_nullable_to_non_nullable
              as double?,
      removedFromLot: freezed == removedFromLot
          ? _value.removedFromLot
          : removedFromLot // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$AdjustmentDetailsModelImpl implements _AdjustmentDetailsModel {
  const _$AdjustmentDetailsModelImpl(
      {this.description,
      this.productId,
      this.productName,
      this.oldQuantity,
      this.newQuantity,
      this.unitType,
      this.oldMaterialId,
      this.oldMaterialName,
      this.newMaterialId,
      this.newMaterialName,
      this.assemblyId,
      this.assemblyName,
      this.oldAssemblyQuantity,
      this.newAssemblyQuantity,
      this.quantityAdjusted,
      this.removedFromLot});

  factory _$AdjustmentDetailsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdjustmentDetailsModelImplFromJson(json);

  @override
  final String? description;
  @override
  final String? productId;
  @override
  final String? productName;
  @override
  final double? oldQuantity;
  @override
  final double? newQuantity;
  @override
  final UnitType? unitType;
  @override
  final String? oldMaterialId;
  @override
  final String? oldMaterialName;
  @override
  final String? newMaterialId;
  @override
  final String? newMaterialName;
  @override
  final String? assemblyId;
  @override
  final String? assemblyName;
  @override
  final double? oldAssemblyQuantity;
  @override
  final double? newAssemblyQuantity;
  @override
  final double? quantityAdjusted;
  @override
  final bool? removedFromLot;

  @override
  String toString() {
    return 'AdjustmentDetailsModel(description: $description, productId: $productId, productName: $productName, oldQuantity: $oldQuantity, newQuantity: $newQuantity, unitType: $unitType, oldMaterialId: $oldMaterialId, oldMaterialName: $oldMaterialName, newMaterialId: $newMaterialId, newMaterialName: $newMaterialName, assemblyId: $assemblyId, assemblyName: $assemblyName, oldAssemblyQuantity: $oldAssemblyQuantity, newAssemblyQuantity: $newAssemblyQuantity, quantityAdjusted: $quantityAdjusted, removedFromLot: $removedFromLot)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdjustmentDetailsModelImpl &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.oldQuantity, oldQuantity) ||
                other.oldQuantity == oldQuantity) &&
            (identical(other.newQuantity, newQuantity) ||
                other.newQuantity == newQuantity) &&
            (identical(other.unitType, unitType) ||
                other.unitType == unitType) &&
            (identical(other.oldMaterialId, oldMaterialId) ||
                other.oldMaterialId == oldMaterialId) &&
            (identical(other.oldMaterialName, oldMaterialName) ||
                other.oldMaterialName == oldMaterialName) &&
            (identical(other.newMaterialId, newMaterialId) ||
                other.newMaterialId == newMaterialId) &&
            (identical(other.newMaterialName, newMaterialName) ||
                other.newMaterialName == newMaterialName) &&
            (identical(other.assemblyId, assemblyId) ||
                other.assemblyId == assemblyId) &&
            (identical(other.assemblyName, assemblyName) ||
                other.assemblyName == assemblyName) &&
            (identical(other.oldAssemblyQuantity, oldAssemblyQuantity) ||
                other.oldAssemblyQuantity == oldAssemblyQuantity) &&
            (identical(other.newAssemblyQuantity, newAssemblyQuantity) ||
                other.newAssemblyQuantity == newAssemblyQuantity) &&
            (identical(other.quantityAdjusted, quantityAdjusted) ||
                other.quantityAdjusted == quantityAdjusted) &&
            (identical(other.removedFromLot, removedFromLot) ||
                other.removedFromLot == removedFromLot));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      description,
      productId,
      productName,
      oldQuantity,
      newQuantity,
      unitType,
      oldMaterialId,
      oldMaterialName,
      newMaterialId,
      newMaterialName,
      assemblyId,
      assemblyName,
      oldAssemblyQuantity,
      newAssemblyQuantity,
      quantityAdjusted,
      removedFromLot);

  /// Create a copy of AdjustmentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdjustmentDetailsModelImplCopyWith<_$AdjustmentDetailsModelImpl>
      get copyWith => __$$AdjustmentDetailsModelImplCopyWithImpl<
          _$AdjustmentDetailsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdjustmentDetailsModelImplToJson(
      this,
    );
  }
}

abstract class _AdjustmentDetailsModel implements AdjustmentDetailsModel {
  const factory _AdjustmentDetailsModel(
      {final String? description,
      final String? productId,
      final String? productName,
      final double? oldQuantity,
      final double? newQuantity,
      final UnitType? unitType,
      final String? oldMaterialId,
      final String? oldMaterialName,
      final String? newMaterialId,
      final String? newMaterialName,
      final String? assemblyId,
      final String? assemblyName,
      final double? oldAssemblyQuantity,
      final double? newAssemblyQuantity,
      final double? quantityAdjusted,
      final bool? removedFromLot}) = _$AdjustmentDetailsModelImpl;

  factory _AdjustmentDetailsModel.fromJson(Map<String, dynamic> json) =
      _$AdjustmentDetailsModelImpl.fromJson;

  @override
  String? get description;
  @override
  String? get productId;
  @override
  String? get productName;
  @override
  double? get oldQuantity;
  @override
  double? get newQuantity;
  @override
  UnitType? get unitType;
  @override
  String? get oldMaterialId;
  @override
  String? get oldMaterialName;
  @override
  String? get newMaterialId;
  @override
  String? get newMaterialName;
  @override
  String? get assemblyId;
  @override
  String? get assemblyName;
  @override
  double? get oldAssemblyQuantity;
  @override
  double? get newAssemblyQuantity;
  @override
  double? get quantityAdjusted;
  @override
  bool? get removedFromLot;

  /// Create a copy of AdjustmentDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdjustmentDetailsModelImplCopyWith<_$AdjustmentDetailsModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
