// ignore_for_file: invalid_annotation_target

import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/shared/styles/colors.dart';

part 'supply.freezed.dart';
part 'supply.g.dart';

@freezed
class SupplyModel with _$SupplyModel {
  const SupplyModel._();

  @JsonSerializable(includeIfNull: false)
  const factory SupplyModel({
    @JsonKey(name: '_id') String? id,
    List<SupplyInputModel>? forInputs,
    SupplyState? state,
    DateTime? supplyDate,
    List<SupplyPaymentModel>? payments,
    double? quantity,
  }) = _SupplyModel;

  factory SupplyModel.fromJson(Map<String, dynamic> json) =>
      _$SupplyModelFromJson(json);
}

@freezed
class SupplyPaymentModel with _$SupplyPaymentModel {
  const SupplyPaymentModel._();

  @JsonSerializable(includeIfNull: false)
  const factory SupplyPaymentModel({
    DateTime? date,
    double? cost,
    SupplyPaymentState? state,
  }) = _SupplyPaymentModel;

  factory SupplyPaymentModel.fromJson(Map<String, dynamic> json) =>
      _$SupplyPaymentModelFromJson(json);
}

@freezed
class SupplyInputModel with _$SupplyInputModel {
  const SupplyInputModel._();

  @JsonSerializable(includeIfNull: false)
  const factory SupplyInputModel({
    String? provisionItemId,
    double? quantity,
  }) = _SupplyInputModel;

  factory SupplyInputModel.fromJson(Map<String, dynamic> json) =>
      _$SupplyInputModelFromJson(json);
}

enum SupplyPaymentState {
  @JsonValue('ready')
  ready,
  @JsonValue('inProcess')
  inProcess;

  String getName() {
    switch (this) {
      case SupplyPaymentState.ready:
        return 'Готово';
      case SupplyPaymentState.inProcess:
        return 'В процессе';
    }
  }
}

enum SupplyPaymentStateColor {
  green,
  purple;

  Color get({bool isDarkTheme = false}) {
    switch (this) {
      case SupplyPaymentStateColor.green:
        return isDarkTheme ? AppColors.darkSuccess : AppColors.lightSuccess;
      case SupplyPaymentStateColor.purple:
        return isDarkTheme ? AppColors.darkPurple : AppColors.lightPurple;
    }
  }
}

enum SupplyState {
  @JsonValue('ready')
  storage,
  @JsonValue('ready')
  ready,
  @JsonValue('almostReady')
  almostReady,
  @JsonValue('partial')
  partial,
  @JsonValue('wrong')
  wrong,
  @JsonValue('inProcess')
  inProcess,
  @JsonValue('error')
  error;

  String getName() {
    switch (this) {
      case SupplyState.storage:
        return 'Склад';
      case SupplyState.ready:
        return 'Готово';
      case SupplyState.almostReady:
        return 'Почти готово';
      case SupplyState.partial:
        return 'Частично';
      case SupplyState.wrong:
        return 'Что-то неправильно';
      case SupplyState.inProcess:
        return 'В процессе';
      case SupplyState.error:
        return 'Ошибка';
    }
  }

  SupplyStateColor getSupplyStateColor() {
    switch (this) {
      case SupplyState.storage:
        return SupplyStateColor.gray;
      case SupplyState.ready:
        return SupplyStateColor.green;
      case SupplyState.almostReady:
        return SupplyStateColor.blue;
      case SupplyState.partial:
        return SupplyStateColor.orange;
      case SupplyState.wrong:
        return SupplyStateColor.orange;
      case SupplyState.inProcess:
        return SupplyStateColor.purple;
      case SupplyState.error:
        return SupplyStateColor.red;
    }
  }
}

enum SupplyStateColor {
  gray,
  green,
  blue,
  orange,
  purple,
  red;

  Color get({bool isDarkTheme = false}) {
    switch (this) {
      case SupplyStateColor.gray:
        return isDarkTheme ? AppColors.medium : AppColors.medium;
      case SupplyStateColor.green:
        return isDarkTheme ? AppColors.darkSuccess : AppColors.lightSuccess;
      case SupplyStateColor.blue:
        return isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary;
      case SupplyStateColor.orange:
        return isDarkTheme ? AppColors.darkWarning : AppColors.lightWarning;
      case SupplyStateColor.purple:
        return isDarkTheme ? AppColors.darkPurple : AppColors.lightPurple;
      case SupplyStateColor.red:
        return isDarkTheme ? AppColors.darkError : AppColors.lightError;
    }
  }
}
