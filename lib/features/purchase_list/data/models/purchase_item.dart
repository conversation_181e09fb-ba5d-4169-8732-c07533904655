// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/purchase_list/data/models/supply.dart';

part 'purchase_item.freezed.dart';
part 'purchase_item.g.dart';

@freezed
class PurchaseItemModel with _$PurchaseItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory PurchaseItemModel({
    @JsonKey(name: '_id') String? id,
    String? groupId,
    String? name,
    String? type,
    String? contractor,
    List<PurchaseInputModel>? inputs,
    bool? withPreferences,
    bool? withChange,
    double? quantity,
    double? totalQuantity,
    double? storageQuantity,
    UnitType? unitType,
    List<SupplyModel>? supplies,
  }) = _PurchaseItemModel;

  factory PurchaseItemModel.fromJson(Map<String, dynamic> json) =>
      _$PurchaseItemModelFromJson(json);
}

@freezed
class PurchaseInputModel with _$PurchaseInputModel {
  @JsonSerializable(includeIfNull: false)
  const factory PurchaseInputModel({
    @JsonKey(name: '_id') String? id,
    String? name,
    int? priority,
    double? quantity,
    double? totalQuantity,
  }) = _PurchaseInputModel;

  factory PurchaseInputModel.fromJson(Map<String, dynamic> json) =>
      _$PurchaseInputModelFromJson(json);
}

@freezed
class PurchaseItemsModel with _$PurchaseItemsModel {
  @JsonSerializable(includeIfNull: false)
  const factory PurchaseItemsModel({
    List<PurchaseItemModel>? items,
  }) = _PurchaseItemsModel;

  factory PurchaseItemsModel.fromJson(Map<String, dynamic> json) =>
      _$PurchaseItemsModelFromJson(json);
}
