// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'purchase_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PurchaseItemModel _$PurchaseItemModelFromJson(Map<String, dynamic> json) {
  return _PurchaseItemModel.fromJson(json);
}

/// @nodoc
mixin _$PurchaseItemModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get groupId => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get contractor => throw _privateConstructorUsedError;
  List<PurchaseInputModel>? get inputs => throw _privateConstructorUsedError;
  bool? get withPreferences => throw _privateConstructorUsedError;
  bool? get withChange => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  double? get totalQuantity => throw _privateConstructorUsedError;
  double? get storageQuantity => throw _privateConstructorUsedError;
  UnitType? get unitType => throw _privateConstructorUsedError;
  List<SupplyModel>? get supplies => throw _privateConstructorUsedError;

  /// Serializes this PurchaseItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PurchaseItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PurchaseItemModelCopyWith<PurchaseItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseItemModelCopyWith<$Res> {
  factory $PurchaseItemModelCopyWith(
          PurchaseItemModel value, $Res Function(PurchaseItemModel) then) =
      _$PurchaseItemModelCopyWithImpl<$Res, PurchaseItemModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? groupId,
      String? name,
      String? type,
      String? contractor,
      List<PurchaseInputModel>? inputs,
      bool? withPreferences,
      bool? withChange,
      double? quantity,
      double? totalQuantity,
      double? storageQuantity,
      UnitType? unitType,
      List<SupplyModel>? supplies});
}

/// @nodoc
class _$PurchaseItemModelCopyWithImpl<$Res, $Val extends PurchaseItemModel>
    implements $PurchaseItemModelCopyWith<$Res> {
  _$PurchaseItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PurchaseItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? groupId = freezed,
    Object? name = freezed,
    Object? type = freezed,
    Object? contractor = freezed,
    Object? inputs = freezed,
    Object? withPreferences = freezed,
    Object? withChange = freezed,
    Object? quantity = freezed,
    Object? totalQuantity = freezed,
    Object? storageQuantity = freezed,
    Object? unitType = freezed,
    Object? supplies = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      contractor: freezed == contractor
          ? _value.contractor
          : contractor // ignore: cast_nullable_to_non_nullable
              as String?,
      inputs: freezed == inputs
          ? _value.inputs
          : inputs // ignore: cast_nullable_to_non_nullable
              as List<PurchaseInputModel>?,
      withPreferences: freezed == withPreferences
          ? _value.withPreferences
          : withPreferences // ignore: cast_nullable_to_non_nullable
              as bool?,
      withChange: freezed == withChange
          ? _value.withChange
          : withChange // ignore: cast_nullable_to_non_nullable
              as bool?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      storageQuantity: freezed == storageQuantity
          ? _value.storageQuantity
          : storageQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      supplies: freezed == supplies
          ? _value.supplies
          : supplies // ignore: cast_nullable_to_non_nullable
              as List<SupplyModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseItemModelImplCopyWith<$Res>
    implements $PurchaseItemModelCopyWith<$Res> {
  factory _$$PurchaseItemModelImplCopyWith(_$PurchaseItemModelImpl value,
          $Res Function(_$PurchaseItemModelImpl) then) =
      __$$PurchaseItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? groupId,
      String? name,
      String? type,
      String? contractor,
      List<PurchaseInputModel>? inputs,
      bool? withPreferences,
      bool? withChange,
      double? quantity,
      double? totalQuantity,
      double? storageQuantity,
      UnitType? unitType,
      List<SupplyModel>? supplies});
}

/// @nodoc
class __$$PurchaseItemModelImplCopyWithImpl<$Res>
    extends _$PurchaseItemModelCopyWithImpl<$Res, _$PurchaseItemModelImpl>
    implements _$$PurchaseItemModelImplCopyWith<$Res> {
  __$$PurchaseItemModelImplCopyWithImpl(_$PurchaseItemModelImpl _value,
      $Res Function(_$PurchaseItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PurchaseItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? groupId = freezed,
    Object? name = freezed,
    Object? type = freezed,
    Object? contractor = freezed,
    Object? inputs = freezed,
    Object? withPreferences = freezed,
    Object? withChange = freezed,
    Object? quantity = freezed,
    Object? totalQuantity = freezed,
    Object? storageQuantity = freezed,
    Object? unitType = freezed,
    Object? supplies = freezed,
  }) {
    return _then(_$PurchaseItemModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      groupId: freezed == groupId
          ? _value.groupId
          : groupId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      contractor: freezed == contractor
          ? _value.contractor
          : contractor // ignore: cast_nullable_to_non_nullable
              as String?,
      inputs: freezed == inputs
          ? _value._inputs
          : inputs // ignore: cast_nullable_to_non_nullable
              as List<PurchaseInputModel>?,
      withPreferences: freezed == withPreferences
          ? _value.withPreferences
          : withPreferences // ignore: cast_nullable_to_non_nullable
              as bool?,
      withChange: freezed == withChange
          ? _value.withChange
          : withChange // ignore: cast_nullable_to_non_nullable
              as bool?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      storageQuantity: freezed == storageQuantity
          ? _value.storageQuantity
          : storageQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      supplies: freezed == supplies
          ? _value._supplies
          : supplies // ignore: cast_nullable_to_non_nullable
              as List<SupplyModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$PurchaseItemModelImpl implements _PurchaseItemModel {
  const _$PurchaseItemModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.groupId,
      this.name,
      this.type,
      this.contractor,
      final List<PurchaseInputModel>? inputs,
      this.withPreferences,
      this.withChange,
      this.quantity,
      this.totalQuantity,
      this.storageQuantity,
      this.unitType,
      final List<SupplyModel>? supplies})
      : _inputs = inputs,
        _supplies = supplies;

  factory _$PurchaseItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseItemModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? groupId;
  @override
  final String? name;
  @override
  final String? type;
  @override
  final String? contractor;
  final List<PurchaseInputModel>? _inputs;
  @override
  List<PurchaseInputModel>? get inputs {
    final value = _inputs;
    if (value == null) return null;
    if (_inputs is EqualUnmodifiableListView) return _inputs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? withPreferences;
  @override
  final bool? withChange;
  @override
  final double? quantity;
  @override
  final double? totalQuantity;
  @override
  final double? storageQuantity;
  @override
  final UnitType? unitType;
  final List<SupplyModel>? _supplies;
  @override
  List<SupplyModel>? get supplies {
    final value = _supplies;
    if (value == null) return null;
    if (_supplies is EqualUnmodifiableListView) return _supplies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PurchaseItemModel(id: $id, groupId: $groupId, name: $name, type: $type, contractor: $contractor, inputs: $inputs, withPreferences: $withPreferences, withChange: $withChange, quantity: $quantity, totalQuantity: $totalQuantity, storageQuantity: $storageQuantity, unitType: $unitType, supplies: $supplies)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseItemModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.groupId, groupId) || other.groupId == groupId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.contractor, contractor) ||
                other.contractor == contractor) &&
            const DeepCollectionEquality().equals(other._inputs, _inputs) &&
            (identical(other.withPreferences, withPreferences) ||
                other.withPreferences == withPreferences) &&
            (identical(other.withChange, withChange) ||
                other.withChange == withChange) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.totalQuantity, totalQuantity) ||
                other.totalQuantity == totalQuantity) &&
            (identical(other.storageQuantity, storageQuantity) ||
                other.storageQuantity == storageQuantity) &&
            (identical(other.unitType, unitType) ||
                other.unitType == unitType) &&
            const DeepCollectionEquality().equals(other._supplies, _supplies));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      groupId,
      name,
      type,
      contractor,
      const DeepCollectionEquality().hash(_inputs),
      withPreferences,
      withChange,
      quantity,
      totalQuantity,
      storageQuantity,
      unitType,
      const DeepCollectionEquality().hash(_supplies));

  /// Create a copy of PurchaseItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseItemModelImplCopyWith<_$PurchaseItemModelImpl> get copyWith =>
      __$$PurchaseItemModelImplCopyWithImpl<_$PurchaseItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseItemModelImplToJson(
      this,
    );
  }
}

abstract class _PurchaseItemModel implements PurchaseItemModel {
  const factory _PurchaseItemModel(
      {@JsonKey(name: '_id') final String? id,
      final String? groupId,
      final String? name,
      final String? type,
      final String? contractor,
      final List<PurchaseInputModel>? inputs,
      final bool? withPreferences,
      final bool? withChange,
      final double? quantity,
      final double? totalQuantity,
      final double? storageQuantity,
      final UnitType? unitType,
      final List<SupplyModel>? supplies}) = _$PurchaseItemModelImpl;

  factory _PurchaseItemModel.fromJson(Map<String, dynamic> json) =
      _$PurchaseItemModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get groupId;
  @override
  String? get name;
  @override
  String? get type;
  @override
  String? get contractor;
  @override
  List<PurchaseInputModel>? get inputs;
  @override
  bool? get withPreferences;
  @override
  bool? get withChange;
  @override
  double? get quantity;
  @override
  double? get totalQuantity;
  @override
  double? get storageQuantity;
  @override
  UnitType? get unitType;
  @override
  List<SupplyModel>? get supplies;

  /// Create a copy of PurchaseItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PurchaseItemModelImplCopyWith<_$PurchaseItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PurchaseInputModel _$PurchaseInputModelFromJson(Map<String, dynamic> json) {
  return _PurchaseInputModel.fromJson(json);
}

/// @nodoc
mixin _$PurchaseInputModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get priority => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  double? get totalQuantity => throw _privateConstructorUsedError;

  /// Serializes this PurchaseInputModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PurchaseInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PurchaseInputModelCopyWith<PurchaseInputModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseInputModelCopyWith<$Res> {
  factory $PurchaseInputModelCopyWith(
          PurchaseInputModel value, $Res Function(PurchaseInputModel) then) =
      _$PurchaseInputModelCopyWithImpl<$Res, PurchaseInputModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      int? priority,
      double? quantity,
      double? totalQuantity});
}

/// @nodoc
class _$PurchaseInputModelCopyWithImpl<$Res, $Val extends PurchaseInputModel>
    implements $PurchaseInputModelCopyWith<$Res> {
  _$PurchaseInputModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PurchaseInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? priority = freezed,
    Object? quantity = freezed,
    Object? totalQuantity = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseInputModelImplCopyWith<$Res>
    implements $PurchaseInputModelCopyWith<$Res> {
  factory _$$PurchaseInputModelImplCopyWith(_$PurchaseInputModelImpl value,
          $Res Function(_$PurchaseInputModelImpl) then) =
      __$$PurchaseInputModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      int? priority,
      double? quantity,
      double? totalQuantity});
}

/// @nodoc
class __$$PurchaseInputModelImplCopyWithImpl<$Res>
    extends _$PurchaseInputModelCopyWithImpl<$Res, _$PurchaseInputModelImpl>
    implements _$$PurchaseInputModelImplCopyWith<$Res> {
  __$$PurchaseInputModelImplCopyWithImpl(_$PurchaseInputModelImpl _value,
      $Res Function(_$PurchaseInputModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PurchaseInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? priority = freezed,
    Object? quantity = freezed,
    Object? totalQuantity = freezed,
  }) {
    return _then(_$PurchaseInputModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      totalQuantity: freezed == totalQuantity
          ? _value.totalQuantity
          : totalQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$PurchaseInputModelImpl implements _PurchaseInputModel {
  const _$PurchaseInputModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.name,
      this.priority,
      this.quantity,
      this.totalQuantity});

  factory _$PurchaseInputModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseInputModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? name;
  @override
  final int? priority;
  @override
  final double? quantity;
  @override
  final double? totalQuantity;

  @override
  String toString() {
    return 'PurchaseInputModel(id: $id, name: $name, priority: $priority, quantity: $quantity, totalQuantity: $totalQuantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseInputModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.totalQuantity, totalQuantity) ||
                other.totalQuantity == totalQuantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, priority, quantity, totalQuantity);

  /// Create a copy of PurchaseInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseInputModelImplCopyWith<_$PurchaseInputModelImpl> get copyWith =>
      __$$PurchaseInputModelImplCopyWithImpl<_$PurchaseInputModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseInputModelImplToJson(
      this,
    );
  }
}

abstract class _PurchaseInputModel implements PurchaseInputModel {
  const factory _PurchaseInputModel(
      {@JsonKey(name: '_id') final String? id,
      final String? name,
      final int? priority,
      final double? quantity,
      final double? totalQuantity}) = _$PurchaseInputModelImpl;

  factory _PurchaseInputModel.fromJson(Map<String, dynamic> json) =
      _$PurchaseInputModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get name;
  @override
  int? get priority;
  @override
  double? get quantity;
  @override
  double? get totalQuantity;

  /// Create a copy of PurchaseInputModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PurchaseInputModelImplCopyWith<_$PurchaseInputModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PurchaseItemsModel _$PurchaseItemsModelFromJson(Map<String, dynamic> json) {
  return _PurchaseItemsModel.fromJson(json);
}

/// @nodoc
mixin _$PurchaseItemsModel {
  List<PurchaseItemModel>? get items => throw _privateConstructorUsedError;

  /// Serializes this PurchaseItemsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PurchaseItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PurchaseItemsModelCopyWith<PurchaseItemsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseItemsModelCopyWith<$Res> {
  factory $PurchaseItemsModelCopyWith(
          PurchaseItemsModel value, $Res Function(PurchaseItemsModel) then) =
      _$PurchaseItemsModelCopyWithImpl<$Res, PurchaseItemsModel>;
  @useResult
  $Res call({List<PurchaseItemModel>? items});
}

/// @nodoc
class _$PurchaseItemsModelCopyWithImpl<$Res, $Val extends PurchaseItemsModel>
    implements $PurchaseItemsModelCopyWith<$Res> {
  _$PurchaseItemsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PurchaseItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PurchaseItemModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseItemsModelImplCopyWith<$Res>
    implements $PurchaseItemsModelCopyWith<$Res> {
  factory _$$PurchaseItemsModelImplCopyWith(_$PurchaseItemsModelImpl value,
          $Res Function(_$PurchaseItemsModelImpl) then) =
      __$$PurchaseItemsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PurchaseItemModel>? items});
}

/// @nodoc
class __$$PurchaseItemsModelImplCopyWithImpl<$Res>
    extends _$PurchaseItemsModelCopyWithImpl<$Res, _$PurchaseItemsModelImpl>
    implements _$$PurchaseItemsModelImplCopyWith<$Res> {
  __$$PurchaseItemsModelImplCopyWithImpl(_$PurchaseItemsModelImpl _value,
      $Res Function(_$PurchaseItemsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PurchaseItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
  }) {
    return _then(_$PurchaseItemsModelImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<PurchaseItemModel>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$PurchaseItemsModelImpl implements _PurchaseItemsModel {
  const _$PurchaseItemsModelImpl({final List<PurchaseItemModel>? items})
      : _items = items;

  factory _$PurchaseItemsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseItemsModelImplFromJson(json);

  final List<PurchaseItemModel>? _items;
  @override
  List<PurchaseItemModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PurchaseItemsModel(items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseItemsModelImpl &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_items));

  /// Create a copy of PurchaseItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseItemsModelImplCopyWith<_$PurchaseItemsModelImpl> get copyWith =>
      __$$PurchaseItemsModelImplCopyWithImpl<_$PurchaseItemsModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseItemsModelImplToJson(
      this,
    );
  }
}

abstract class _PurchaseItemsModel implements PurchaseItemsModel {
  const factory _PurchaseItemsModel({final List<PurchaseItemModel>? items}) =
      _$PurchaseItemsModelImpl;

  factory _PurchaseItemsModel.fromJson(Map<String, dynamic> json) =
      _$PurchaseItemsModelImpl.fromJson;

  @override
  List<PurchaseItemModel>? get items;

  /// Create a copy of PurchaseItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PurchaseItemsModelImplCopyWith<_$PurchaseItemsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
