// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provision.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProvisionProductModelImpl _$$ProvisionProductModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionProductModelImpl(
      uniqueId: json['uniqueId'] as String?,
      productId: json['productId'] as String?,
      product: json['product'] == null
          ? null
          : ProductModel.fromJson(json['product'] as Map<String, dynamic>),
      productName: json['productName'] as String?,
      material: json['material'] == null
          ? null
          : NomenclatureModel.fromJson(
              json['material'] as Map<String, dynamic>),
      parentProduct: json['parentProduct'] == null
          ? null
          : ProductModel.fromJson(
              json['parentProduct'] as Map<String, dynamic>),
      quantity: (json['quantity'] as num?)?.toDouble(),
      totalMass: (json['totalMass'] as num?)?.toDouble(),
      totalQuantity: (json['totalQuantity'] as num?)?.toDouble(),
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ParametersFeatureTypeEnumMap, e))
          .toList(),
      responsibleUser: json['responsibleUser'] == null
          ? null
          : UserModel.fromJson(json['responsibleUser'] as Map<String, dynamic>),
      responsibleUserName: json['responsibleUserName'] as String?,
      status: $enumDecodeNullable(_$SupplyStatusEnumMap, json['status']),
      contract: json['contract'] == null
          ? null
          : ContractModel.fromJson(json['contract'] as Map<String, dynamic>),
      lots: (json['lots'] as List<dynamic>?)
          ?.map((e) => LotModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      supplier: json['supplier'] == null
          ? null
          : ClientModel.fromJson(json['supplier'] as Map<String, dynamic>),
      deliveryDate: json['deliveryDate'] == null
          ? null
          : DateTime.parse(json['deliveryDate'] as String),
      unitCost: (json['unitCost'] as num?)?.toDouble(),
      totalCost: (json['totalCost'] as num?)?.toDouble(),
      parent: json['parent'] == null
          ? null
          : ProductModel.fromJson(json['parent'] as Map<String, dynamic>),
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
      task: json['task'] == null
          ? null
          : TaskProgressModel.fromJson(json['task'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProvisionProductModelImplToJson(
        _$ProvisionProductModelImpl instance) =>
    <String, dynamic>{
      if (instance.uniqueId case final value?) 'uniqueId': value,
      if (instance.productId case final value?) 'productId': value,
      if (instance.product case final value?) 'product': value,
      if (instance.productName case final value?) 'productName': value,
      if (instance.material case final value?) 'material': value,
      if (instance.parentProduct case final value?) 'parentProduct': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.totalMass case final value?) 'totalMass': value,
      if (instance.totalQuantity case final value?) 'totalQuantity': value,
      if (instance.features
              ?.map((e) => _$ParametersFeatureTypeEnumMap[e]!)
              .toList()
          case final value?)
        'features': value,
      if (instance.responsibleUser case final value?) 'responsibleUser': value,
      if (instance.responsibleUserName case final value?)
        'responsibleUserName': value,
      if (_$SupplyStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.contract case final value?) 'contract': value,
      if (instance.lots case final value?) 'lots': value,
      if (instance.supplier case final value?) 'supplier': value,
      if (instance.deliveryDate?.toIso8601String() case final value?)
        'deliveryDate': value,
      if (instance.unitCost case final value?) 'unitCost': value,
      if (instance.totalCost case final value?) 'totalCost': value,
      if (instance.parent case final value?) 'parent': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'filterType': value,
      if (instance.task case final value?) 'task': value,
    };

const _$ParametersFeatureTypeEnumMap = {
  ParametersFeatureType.mechanics: 'MECHANICS',
  ParametersFeatureType.bending: 'BENDING',
  ParametersFeatureType.cutting: 'CUTTING',
  ParametersFeatureType.coating: 'COATING',
  ParametersFeatureType.heatTreatment: 'HEAT_TREATMENT',
  ParametersFeatureType.rolling: 'ROLLING',
  ParametersFeatureType.welding: 'WELDING',
};

const _$SupplyStatusEnumMap = {
  SupplyStatus.notStarted: 'not_started',
  SupplyStatus.confirmed: 'confirmed',
  SupplyStatus.delivered: 'delivered',
  SupplyStatus.qcPassed: 'qc_passed',
  SupplyStatus.pendingApproval: 'pending_approval',
  SupplyStatus.completed: 'completed',
};

const _$ProvisionsFilterEnumMap = {
  ProvisionsFilter.cooperation: 'cooperation',
  ProvisionsFilter.materials: 'materials',
  ProvisionsFilter.cooperationMaterials: 'cooperation_materials',
  ProvisionsFilter.assemblyMaterials: 'assembly_materials',
  ProvisionsFilter.purchased: 'purchased',
  ProvisionsFilter.assembly: 'assembly',
};

_$ContractModelImpl _$$ContractModelImplFromJson(Map<String, dynamic> json) =>
    _$ContractModelImpl(
      id: json['_id'] as String?,
      projectId: json['projectId'] as String?,
      contractNumber: json['contractNumber'] as String?,
      contractDate: json['contractDate'] == null
          ? null
          : DateTime.parse(json['contractDate'] as String),
      plannedDeliveryDate: json['plannedDeliveryDate'] == null
          ? null
          : DateTime.parse(json['plannedDeliveryDate'] as String),
      supplierId: json['supplierId'] as String?,
      supplier: json['supplier'] == null
          ? null
          : ClientModel.fromJson(json['supplier'] as Map<String, dynamic>),
      productIds: (json['productIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      contractPrice: (json['contractPrice'] as num?)?.toDouble(),
      productDetails: json['productDetails'],
      documentIds: (json['documentIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      status: $enumDecodeNullable(_$SupplyStatusEnumMap, json['status']),
      notes: json['notes'] as String?,
      createdBy: json['createdBy'] as String?,
      updatedBy: json['updatedBy'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ContractModelImplToJson(_$ContractModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.contractNumber case final value?) 'contractNumber': value,
      if (instance.contractDate?.toIso8601String() case final value?)
        'contractDate': value,
      if (instance.plannedDeliveryDate?.toIso8601String() case final value?)
        'plannedDeliveryDate': value,
      if (instance.supplierId case final value?) 'supplierId': value,
      if (instance.supplier case final value?) 'supplier': value,
      if (instance.productIds case final value?) 'productIds': value,
      if (instance.contractPrice case final value?) 'contractPrice': value,
      if (instance.productDetails case final value?) 'productDetails': value,
      if (instance.documentIds case final value?) 'documentIds': value,
      if (_$SupplyStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.updatedBy case final value?) 'updatedBy': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

_$LotModelImpl _$$LotModelImplFromJson(Map<String, dynamic> json) =>
    _$LotModelImpl(
      id: json['_id'] as String?,
      projectId: json['projectId'] as String?,
      lotNumber: (json['lotNumber'] as num?)?.toInt(),
      lotName: json['lotName'] as String?,
      productIds: (json['productIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdBy: json['createdBy'] as String?,
      plannedTenderCompletionDate: json['plannedTenderCompletionDate'] == null
          ? null
          : DateTime.parse(json['plannedTenderCompletionDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$LotModelImplToJson(_$LotModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.lotNumber case final value?) 'lotNumber': value,
      if (instance.lotName case final value?) 'lotName': value,
      if (instance.productIds case final value?) 'productIds': value,
      if (instance.createdBy case final value?) 'createdBy': value,
      if (instance.plannedTenderCompletionDate?.toIso8601String()
          case final value?)
        'plannedTenderCompletionDate': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

_$ProvisionMaterialModelImpl _$$ProvisionMaterialModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionMaterialModelImpl(
      materialId: json['materialId'] as String?,
      materialName: json['materialName'] as String?,
      unitType: $enumDecodeNullable(_$UnitTypeEnumMap, json['unitType']),
      quantity: (json['quantity'] as num?)?.toDouble(),
      materialRequirements: json['materialRequirements'] as String?,
      totalQuantity: (json['totalQuantity'] as num?)?.toDouble(),
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => ProvisionItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      storageQuantity: (json['storageQuantity'] as num?)?.toDouble(),
      totalPrice: (json['totalPrice'] as num?)?.toDouble(),
      operations: (json['operations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      deliveries: (json['deliveries'] as List<dynamic>?)
          ?.map(
              (e) => ProvisionDeliveryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProvisionMaterialModelImplToJson(
        _$ProvisionMaterialModelImpl instance) =>
    <String, dynamic>{
      if (instance.materialId case final value?) 'materialId': value,
      if (instance.materialName case final value?) 'materialName': value,
      if (_$UnitTypeEnumMap[instance.unitType] case final value?)
        'unitType': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.materialRequirements case final value?)
        'materialRequirements': value,
      if (instance.totalQuantity case final value?) 'totalQuantity': value,
      if (instance.products case final value?) 'products': value,
      if (instance.storageQuantity case final value?) 'storageQuantity': value,
      if (instance.totalPrice case final value?) 'totalPrice': value,
      if (instance.operations case final value?) 'operations': value,
      if (instance.deliveries case final value?) 'deliveries': value,
    };

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};

_$ProvisionDeliviresModelImpl _$$ProvisionDeliviresModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionDeliviresModelImpl(
      id: json['_id'] as String?,
      expectedDate: json['expectedDate'] == null
          ? null
          : DateTime.parse(json['expectedDate'] as String),
      deliveryDates: (json['deliveryDates'] as List<dynamic>?)
          ?.map((e) => DateTime.parse(e as String))
          .toList(),
      links: (json['links'] as List<dynamic>?)
          ?.map((e) => SupplyInputModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProvisionDeliviresModelImplToJson(
        _$ProvisionDeliviresModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.expectedDate?.toIso8601String() case final value?)
        'expectedDate': value,
      if (instance.deliveryDates?.map((e) => e.toIso8601String()).toList()
          case final value?)
        'deliveryDates': value,
      if (instance.links case final value?) 'links': value,
    };

_$ProvisionDeliveryLinkModelImpl _$$ProvisionDeliveryLinkModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionDeliveryLinkModelImpl(
      provisionItemId: json['provisionItemId'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ProvisionDeliveryLinkModelImplToJson(
        _$ProvisionDeliveryLinkModelImpl instance) =>
    <String, dynamic>{
      if (instance.provisionItemId case final value?) 'provisionItemId': value,
      if (instance.quantity case final value?) 'quantity': value,
    };

_$ProvisionLotModelImpl _$$ProvisionLotModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionLotModelImpl(
      id: json['_id'] as String?,
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
      provisionName: json['provisionName'] as String?,
      provisionStatus: $enumDecodeNullable(
          _$ProvisionStatusEnumMap, json['provisionStatus']),
      supplier: json['supplier'] == null
          ? null
          : ClientModel.fromJson(json['supplier'] as Map<String, dynamic>),
      provisionItems: (json['provisionItems'] as List<dynamic>?)
          ?.map((e) => ProvisionItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      contractItems: (json['contractItems'] as List<dynamic>?)
          ?.map((e) =>
              ProvisionContractItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      contractNumber: json['contractNumber'] as String?,
      contractDate: json['contractDate'] == null
          ? null
          : DateTime.parse(json['contractDate'] as String),
      contractStartDate: json['contractStartDate'] == null
          ? null
          : DateTime.parse(json['contractStartDate'] as String),
      contractEndDate: json['contractEndDate'] == null
          ? null
          : DateTime.parse(json['contractEndDate'] as String),
      contractDeliveryDates: (json['contractDeliveryDates'] as List<dynamic>?)
          ?.map((e) => DateTime.parse(e as String))
          .toList(),
      contractPrice: (json['contractPrice'] as num?)?.toDouble(),
      document: json['document'] == null
          ? null
          : FileModel.fromJson(json['document'] as Map<String, dynamic>),
      lotPrice: (json['lotPrice'] as num?)?.toDouble(),
      paymentDetails: (json['paymentDetails'] as List<dynamic>?)
          ?.map((e) =>
              ProvisionPaymentDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      deliveries: (json['deliveries'] as List<dynamic>?)
          ?.map((e) => DeliveryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      deliveryGroups: (json['deliveryGroups'] as List<dynamic>?)
          ?.map((e) => DeliveryGroupModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProvisionLotModelImplToJson(
        _$ProvisionLotModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'filterType': value,
      if (instance.provisionName case final value?) 'provisionName': value,
      if (_$ProvisionStatusEnumMap[instance.provisionStatus] case final value?)
        'provisionStatus': value,
      if (instance.supplier case final value?) 'supplier': value,
      if (instance.provisionItems case final value?) 'provisionItems': value,
      if (instance.contractItems case final value?) 'contractItems': value,
      if (instance.contractNumber case final value?) 'contractNumber': value,
      if (instance.contractDate?.toIso8601String() case final value?)
        'contractDate': value,
      if (instance.contractStartDate?.toIso8601String() case final value?)
        'contractStartDate': value,
      if (instance.contractEndDate?.toIso8601String() case final value?)
        'contractEndDate': value,
      if (instance.contractDeliveryDates
              ?.map((e) => e.toIso8601String())
              .toList()
          case final value?)
        'contractDeliveryDates': value,
      if (instance.contractPrice case final value?) 'contractPrice': value,
      if (instance.document case final value?) 'document': value,
      if (instance.lotPrice case final value?) 'lotPrice': value,
      if (instance.paymentDetails case final value?) 'paymentDetails': value,
      if (instance.deliveries case final value?) 'deliveries': value,
      if (instance.deliveryGroups case final value?) 'deliveryGroups': value,
    };

const _$ProvisionStatusEnumMap = {
  ProvisionStatus.newStatus: 'new',
  ProvisionStatus.direction: 'direction',
  ProvisionStatus.contracted: 'contracted',
  ProvisionStatus.needsOtk: 'needs_otk',
  ProvisionStatus.stored: 'stored',
};

_$ProvisionItemImpl _$$ProvisionItemImplFromJson(Map<String, dynamic> json) =>
    _$ProvisionItemImpl(
      product: json['product'] == null
          ? null
          : ProductModel.fromJson(json['product'] as Map<String, dynamic>),
      material: json['material'] == null
          ? null
          : NomenclatureModel.fromJson(
              json['material'] as Map<String, dynamic>),
      materialGroupId: json['materialGroupId'] as String?,
      uniqueId: json['uniqueId'] as String?,
      provisionId: json['provisionId'] as String?,
      id: json['_id'] as String?,
      featureType: $enumDecodeNullable(
          _$ParametersFeatureTypeEnumMap, json['featureType']),
      cooperationQuantity: (json['cooperationQuantity'] as num?)?.toDouble(),
      deliveryQuantity: (json['deliveryQuantity'] as num?)?.toDouble(),
      totalQuantity: (json['totalQuantity'] as num?)?.toDouble(),
      multipliedQuantity: (json['multipliedQuantity'] as num?)?.toDouble(),
      adjustments: (json['adjustments'] as List<dynamic>?)
          ?.map((e) => AdjustmentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      paymentDetails: (json['paymentDetails'] as List<dynamic>?)
          ?.map((e) =>
              ProvisionPaymentDetailsModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ProvisionItemImplToJson(_$ProvisionItemImpl instance) =>
    <String, dynamic>{
      if (instance.product case final value?) 'product': value,
      if (instance.material case final value?) 'material': value,
      if (instance.materialGroupId case final value?) 'materialGroupId': value,
      if (instance.uniqueId case final value?) 'uniqueId': value,
      if (instance.provisionId case final value?) 'provisionId': value,
      if (instance.id case final value?) '_id': value,
      if (_$ParametersFeatureTypeEnumMap[instance.featureType]
          case final value?)
        'featureType': value,
      if (instance.cooperationQuantity case final value?)
        'cooperationQuantity': value,
      if (instance.deliveryQuantity case final value?)
        'deliveryQuantity': value,
      if (instance.totalQuantity case final value?) 'totalQuantity': value,
      if (instance.multipliedQuantity case final value?)
        'multipliedQuantity': value,
      if (instance.adjustments case final value?) 'adjustments': value,
      if (instance.paymentDetails case final value?) 'paymentDetails': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

_$ProvisionContractItemModelImpl _$$ProvisionContractItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionContractItemModelImpl(
      materialId: json['materialId'] as String?,
      materialRequirements: json['materialRequirements'] as String?,
      productId: json['productId'] as String?,
      featureType: $enumDecodeNullable(
          _$ParametersFeatureTypeEnumMap, json['featureType']),
      needMaterial: json['needMaterial'] as bool?,
      price: (json['price'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ProvisionContractItemModelImplToJson(
        _$ProvisionContractItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.materialId case final value?) 'materialId': value,
      if (instance.materialRequirements case final value?)
        'materialRequirements': value,
      if (instance.productId case final value?) 'productId': value,
      if (_$ParametersFeatureTypeEnumMap[instance.featureType]
          case final value?)
        'featureType': value,
      if (instance.needMaterial case final value?) 'needMaterial': value,
      if (instance.price case final value?) 'price': value,
      if (instance.quantity case final value?) 'quantity': value,
    };

_$ProvisionPaymentDetailsImpl _$$ProvisionPaymentDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionPaymentDetailsImpl(
      paymentDate: json['paymentDate'] == null
          ? null
          : DateTime.parse(json['paymentDate'] as String),
      amount: (json['amount'] as num?)?.toDouble(),
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$ProvisionPaymentDetailsImplToJson(
        _$ProvisionPaymentDetailsImpl instance) =>
    <String, dynamic>{
      if (instance.paymentDate?.toIso8601String() case final value?)
        'paymentDate': value,
      if (instance.amount case final value?) 'amount': value,
      if (instance.description case final value?) 'description': value,
    };

_$ProvisionsListModelImpl _$$ProvisionsListModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionsListModelImpl(
      lots: (json['lots'] as List<dynamic>?)
          ?.map((e) => ProvisionLotModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      unallocatedItems: (json['unallocatedItems'] as List<dynamic>?)
          ?.map((e) => ProvisionItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProvisionsListModelImplToJson(
        _$ProvisionsListModelImpl instance) =>
    <String, dynamic>{
      if (instance.lots case final value?) 'lots': value,
      if (instance.unallocatedItems case final value?)
        'unallocatedItems': value,
    };

_$AdjustmentModelImpl _$$AdjustmentModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AdjustmentModelImpl(
      type: $enumDecodeNullable(_$AdjustmentTypeEnumMap, json['type']),
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      details: json['details'] == null
          ? null
          : AdjustmentDetailsModel.fromJson(
              json['details'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AdjustmentModelImplToJson(
        _$AdjustmentModelImpl instance) =>
    <String, dynamic>{
      if (_$AdjustmentTypeEnumMap[instance.type] case final value?)
        'type': value,
      if (instance.date?.toIso8601String() case final value?) 'date': value,
      if (instance.details case final value?) 'details': value,
    };

const _$AdjustmentTypeEnumMap = {
  AdjustmentType.quantityDecreased: 'quantity_decreased',
  AdjustmentType.quantityDepleted: 'quantity_depleted',
  AdjustmentType.productDeleted: 'product_deleted',
  AdjustmentType.materialChanged: 'material_changed',
  AdjustmentType.assemblyQuantityChanged: 'assembly_quantity_changed',
};

_$AdjustmentDetailsModelImpl _$$AdjustmentDetailsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$AdjustmentDetailsModelImpl(
      description: json['description'] as String?,
      productId: json['productId'] as String?,
      productName: json['productName'] as String?,
      oldQuantity: (json['oldQuantity'] as num?)?.toDouble(),
      newQuantity: (json['newQuantity'] as num?)?.toDouble(),
      unitType: $enumDecodeNullable(_$UnitTypeEnumMap, json['unitType']),
      oldMaterialId: json['oldMaterialId'] as String?,
      oldMaterialName: json['oldMaterialName'] as String?,
      newMaterialId: json['newMaterialId'] as String?,
      newMaterialName: json['newMaterialName'] as String?,
      assemblyId: json['assemblyId'] as String?,
      assemblyName: json['assemblyName'] as String?,
      oldAssemblyQuantity: (json['oldAssemblyQuantity'] as num?)?.toDouble(),
      newAssemblyQuantity: (json['newAssemblyQuantity'] as num?)?.toDouble(),
      quantityAdjusted: (json['quantityAdjusted'] as num?)?.toDouble(),
      removedFromLot: json['removedFromLot'] as bool?,
    );

Map<String, dynamic> _$$AdjustmentDetailsModelImplToJson(
        _$AdjustmentDetailsModelImpl instance) =>
    <String, dynamic>{
      if (instance.description case final value?) 'description': value,
      if (instance.productId case final value?) 'productId': value,
      if (instance.productName case final value?) 'productName': value,
      if (instance.oldQuantity case final value?) 'oldQuantity': value,
      if (instance.newQuantity case final value?) 'newQuantity': value,
      if (_$UnitTypeEnumMap[instance.unitType] case final value?)
        'unitType': value,
      if (instance.oldMaterialId case final value?) 'oldMaterialId': value,
      if (instance.oldMaterialName case final value?) 'oldMaterialName': value,
      if (instance.newMaterialId case final value?) 'newMaterialId': value,
      if (instance.newMaterialName case final value?) 'newMaterialName': value,
      if (instance.assemblyId case final value?) 'assemblyId': value,
      if (instance.assemblyName case final value?) 'assemblyName': value,
      if (instance.oldAssemblyQuantity case final value?)
        'oldAssemblyQuantity': value,
      if (instance.newAssemblyQuantity case final value?)
        'newAssemblyQuantity': value,
      if (instance.quantityAdjusted case final value?)
        'quantityAdjusted': value,
      if (instance.removedFromLot case final value?) 'removedFromLot': value,
    };
