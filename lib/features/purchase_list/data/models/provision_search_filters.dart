import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';

part 'provision_search_filters.freezed.dart';
part 'provision_search_filters.g.dart';

/// Фильтры для поиска потребностей в снабжении
@freezed
class ProvisionSearchFilters with _$ProvisionSearchFilters {
  const factory ProvisionSearchFilters({
    // Основные фильтры
    DateTime? dateFrom,
    DateTime? dateTo,
    String? drawingNumber,
    String? name,
    String? material,
    String? feature,
    ProvisionsFilter? filterType,

    // Числовые диапазоны
    double? massFrom,
    double? massTo,
    int? quantityFrom,
    int? quantityTo,

    // Текстовые поля
    String? requirements,
    String? materialRequirements,
    int? priority,

    // Ответственный
    String? responsiblePerson,
    DateTime? taskDateFrom,
    DateTime? taskDateTo,

    // Статус и контракт
    List<ContractStatus>? supplyStatus,
    DateTime? plannedContractDateFrom,
    DateTime? plannedContractDateTo,
    List<String>? lotNumbers,
    List<String>? lotNames,

    // Поставщик и финансы
    String? contractNumber,
    String? supplier,
    DateTime? deliveryDateFrom,
    DateTime? deliveryDateTo,
    double? costFrom,
    double? costTo,

    // ID фильтры
    required String projectId,
    List<String>? productIds,
    List<String>? lotIds,
    List<String>? contractIds,
  }) = _ProvisionSearchFilters;

  factory ProvisionSearchFilters.fromJson(Map<String, dynamic> json) =>
      _$ProvisionSearchFiltersFromJson(json);
}

/// Расширение для подсчета активных фильтров
extension ProvisionSearchFiltersX on ProvisionSearchFilters {
  /// Подсчитывает количество активных фильтров (исключая обязательные)
  int get activeFiltersCount {
    int count = 0;

    // Основные фильтры
    if (dateFrom != null) count++;
    if (dateTo != null) count++;
    if (drawingNumber != null && drawingNumber!.isNotEmpty) count++;
    if (name != null && name!.isNotEmpty) count++;
    if (material != null && material!.isNotEmpty) count++;
    if (feature != null && feature!.isNotEmpty) count++;
    if (filterType != null) count++;

    // Числовые диапазоны
    if (massFrom != null) count++;
    if (massTo != null) count++;
    if (quantityFrom != null) count++;
    if (quantityTo != null) count++;

    // Текстовые поля
    if (requirements != null && requirements!.isNotEmpty) count++;
    if (materialRequirements != null && materialRequirements!.isNotEmpty)
      count++;
    if (priority != null) count++;

    // Ответственный
    if (responsiblePerson != null && responsiblePerson!.isNotEmpty) count++;
    if (taskDateFrom != null) count++;
    if (taskDateTo != null) count++;

    // Статус и контракт
    if (supplyStatus != null && supplyStatus!.isNotEmpty) count++;
    if (plannedContractDateFrom != null) count++;
    if (plannedContractDateTo != null) count++;
    if (lotNumbers != null && lotNumbers!.isNotEmpty) count++;
    if (lotNames != null && lotNames!.isNotEmpty) count++;

    // Поставщик и финансы
    if (contractNumber != null && contractNumber!.isNotEmpty) count++;
    if (supplier != null && supplier!.isNotEmpty) count++;
    if (deliveryDateFrom != null) count++;
    if (deliveryDateTo != null) count++;
    if (costFrom != null) count++;
    if (costTo != null) count++;

    // ID фильтры (не считаем projectId, так как он обязательный)
    if (productIds != null && productIds!.isNotEmpty) count++;
    if (lotIds != null && lotIds!.isNotEmpty) count++;
    if (contractIds != null && contractIds!.isNotEmpty) count++;

    return count;
  }

  /// Возвращает список названий активных фильтров для отображения
  List<String> get activeFilterNames {
    final List<String> names = [];

    if (dateFrom != null || dateTo != null) names.add('Дата ввода');
    if (drawingNumber != null && drawingNumber!.isNotEmpty)
      names.add('Номер чертежа');
    if (name != null && name!.isNotEmpty) names.add('Название');
    if (material != null && material!.isNotEmpty) names.add('Материал');
    if (feature != null && feature!.isNotEmpty) names.add('Признак');
    if (filterType != null) names.add(filterType!.getName());

    if (massFrom != null || massTo != null) names.add('Масса');
    if (quantityFrom != null || quantityTo != null) names.add('Количество');

    if (requirements != null && requirements!.isNotEmpty)
      names.add('Примечание');
    if (materialRequirements != null && materialRequirements!.isNotEmpty)
      names.add('Доп. требования');
    if (priority != null) names.add('Приоритет');

    if (responsiblePerson != null && responsiblePerson!.isNotEmpty)
      names.add('Ответственный');
    if (taskDateFrom != null || taskDateTo != null) names.add('Дата задачи');

    if (supplyStatus != null && supplyStatus!.isNotEmpty)
      names.add('Статус закупа');
    if (plannedContractDateFrom != null || plannedContractDateTo != null)
      names.add('Дата контракта');
    if (lotNumbers != null && lotNumbers!.isNotEmpty) names.add('Номер лота');
    if (lotNames != null && lotNames!.isNotEmpty) names.add('Название лота');

    if (contractNumber != null && contractNumber!.isNotEmpty)
      names.add('Номер заказа');
    if (supplier != null && supplier!.isNotEmpty) names.add('Поставщик');
    if (deliveryDateFrom != null || deliveryDateTo != null)
      names.add('Дата поставки');
    if (costFrom != null || costTo != null) names.add('Стоимость');

    return names;
  }

  /// Проверяет, есть ли активные фильтры
  bool get hasActiveFilters => activeFiltersCount > 0;

  /// Создает копию с очищенными фильтрами (кроме обязательных)
  ProvisionSearchFilters clearFilters() {
    return ProvisionSearchFilters(projectId: projectId);
  }
}
