// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'provision_search_filters.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProvisionSearchFilters _$ProvisionSearchFiltersFromJson(
    Map<String, dynamic> json) {
  return _ProvisionSearchFilters.fromJson(json);
}

/// @nodoc
mixin _$ProvisionSearchFilters {
// Основные фильтры
  DateTime? get dateFrom => throw _privateConstructorUsedError;
  DateTime? get dateTo => throw _privateConstructorUsedError;
  String? get drawingNumber => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get material => throw _privateConstructorUsedError;
  String? get feature => throw _privateConstructorUsedError;
  ProvisionsFilter? get filterType =>
      throw _privateConstructorUsedError; // Числовые диапазоны
  double? get massFrom => throw _privateConstructorUsedError;
  double? get massTo => throw _privateConstructorUsedError;
  int? get quantityFrom => throw _privateConstructorUsedError;
  int? get quantityTo => throw _privateConstructorUsedError; // Текстовые поля
  String? get requirements => throw _privateConstructorUsedError;
  String? get materialRequirements => throw _privateConstructorUsedError;
  int? get priority => throw _privateConstructorUsedError; // Ответственный
  String? get responsiblePerson => throw _privateConstructorUsedError;
  DateTime? get taskDateFrom => throw _privateConstructorUsedError;
  DateTime? get taskDateTo =>
      throw _privateConstructorUsedError; // Статус и контракт
  List<ContractStatus>? get supplyStatus => throw _privateConstructorUsedError;
  DateTime? get plannedContractDateFrom => throw _privateConstructorUsedError;
  DateTime? get plannedContractDateTo => throw _privateConstructorUsedError;
  List<String>? get lotNumbers => throw _privateConstructorUsedError;
  List<String>? get lotNames =>
      throw _privateConstructorUsedError; // Поставщик и финансы
  String? get contractNumber => throw _privateConstructorUsedError;
  String? get supplier => throw _privateConstructorUsedError;
  DateTime? get deliveryDateFrom => throw _privateConstructorUsedError;
  DateTime? get deliveryDateTo => throw _privateConstructorUsedError;
  double? get costFrom => throw _privateConstructorUsedError;
  double? get costTo => throw _privateConstructorUsedError; // ID фильтры
  String get projectId => throw _privateConstructorUsedError;
  List<String>? get productIds => throw _privateConstructorUsedError;
  List<String>? get lotIds => throw _privateConstructorUsedError;
  List<String>? get contractIds => throw _privateConstructorUsedError;

  /// Serializes this ProvisionSearchFilters to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProvisionSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProvisionSearchFiltersCopyWith<ProvisionSearchFilters> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProvisionSearchFiltersCopyWith<$Res> {
  factory $ProvisionSearchFiltersCopyWith(ProvisionSearchFilters value,
          $Res Function(ProvisionSearchFilters) then) =
      _$ProvisionSearchFiltersCopyWithImpl<$Res, ProvisionSearchFilters>;
  @useResult
  $Res call(
      {DateTime? dateFrom,
      DateTime? dateTo,
      String? drawingNumber,
      String? name,
      String? material,
      String? feature,
      ProvisionsFilter? filterType,
      double? massFrom,
      double? massTo,
      int? quantityFrom,
      int? quantityTo,
      String? requirements,
      String? materialRequirements,
      int? priority,
      String? responsiblePerson,
      DateTime? taskDateFrom,
      DateTime? taskDateTo,
      List<ContractStatus>? supplyStatus,
      DateTime? plannedContractDateFrom,
      DateTime? plannedContractDateTo,
      List<String>? lotNumbers,
      List<String>? lotNames,
      String? contractNumber,
      String? supplier,
      DateTime? deliveryDateFrom,
      DateTime? deliveryDateTo,
      double? costFrom,
      double? costTo,
      String projectId,
      List<String>? productIds,
      List<String>? lotIds,
      List<String>? contractIds});
}

/// @nodoc
class _$ProvisionSearchFiltersCopyWithImpl<$Res,
        $Val extends ProvisionSearchFilters>
    implements $ProvisionSearchFiltersCopyWith<$Res> {
  _$ProvisionSearchFiltersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProvisionSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? drawingNumber = freezed,
    Object? name = freezed,
    Object? material = freezed,
    Object? feature = freezed,
    Object? filterType = freezed,
    Object? massFrom = freezed,
    Object? massTo = freezed,
    Object? quantityFrom = freezed,
    Object? quantityTo = freezed,
    Object? requirements = freezed,
    Object? materialRequirements = freezed,
    Object? priority = freezed,
    Object? responsiblePerson = freezed,
    Object? taskDateFrom = freezed,
    Object? taskDateTo = freezed,
    Object? supplyStatus = freezed,
    Object? plannedContractDateFrom = freezed,
    Object? plannedContractDateTo = freezed,
    Object? lotNumbers = freezed,
    Object? lotNames = freezed,
    Object? contractNumber = freezed,
    Object? supplier = freezed,
    Object? deliveryDateFrom = freezed,
    Object? deliveryDateTo = freezed,
    Object? costFrom = freezed,
    Object? costTo = freezed,
    Object? projectId = null,
    Object? productIds = freezed,
    Object? lotIds = freezed,
    Object? contractIds = freezed,
  }) {
    return _then(_value.copyWith(
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      drawingNumber: freezed == drawingNumber
          ? _value.drawingNumber
          : drawingNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as String?,
      feature: freezed == feature
          ? _value.feature
          : feature // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      massFrom: freezed == massFrom
          ? _value.massFrom
          : massFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      massTo: freezed == massTo
          ? _value.massTo
          : massTo // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityFrom: freezed == quantityFrom
          ? _value.quantityFrom
          : quantityFrom // ignore: cast_nullable_to_non_nullable
              as int?,
      quantityTo: freezed == quantityTo
          ? _value.quantityTo
          : quantityTo // ignore: cast_nullable_to_non_nullable
              as int?,
      requirements: freezed == requirements
          ? _value.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      responsiblePerson: freezed == responsiblePerson
          ? _value.responsiblePerson
          : responsiblePerson // ignore: cast_nullable_to_non_nullable
              as String?,
      taskDateFrom: freezed == taskDateFrom
          ? _value.taskDateFrom
          : taskDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      taskDateTo: freezed == taskDateTo
          ? _value.taskDateTo
          : taskDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      supplyStatus: freezed == supplyStatus
          ? _value.supplyStatus
          : supplyStatus // ignore: cast_nullable_to_non_nullable
              as List<ContractStatus>?,
      plannedContractDateFrom: freezed == plannedContractDateFrom
          ? _value.plannedContractDateFrom
          : plannedContractDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      plannedContractDateTo: freezed == plannedContractDateTo
          ? _value.plannedContractDateTo
          : plannedContractDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lotNumbers: freezed == lotNumbers
          ? _value.lotNumbers
          : lotNumbers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      lotNames: freezed == lotNames
          ? _value.lotNames
          : lotNames // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryDateFrom: freezed == deliveryDateFrom
          ? _value.deliveryDateFrom
          : deliveryDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateTo: freezed == deliveryDateTo
          ? _value.deliveryDateTo
          : deliveryDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      costFrom: freezed == costFrom
          ? _value.costFrom
          : costFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      costTo: freezed == costTo
          ? _value.costTo
          : costTo // ignore: cast_nullable_to_non_nullable
              as double?,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      productIds: freezed == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      lotIds: freezed == lotIds
          ? _value.lotIds
          : lotIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractIds: freezed == contractIds
          ? _value.contractIds
          : contractIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProvisionSearchFiltersImplCopyWith<$Res>
    implements $ProvisionSearchFiltersCopyWith<$Res> {
  factory _$$ProvisionSearchFiltersImplCopyWith(
          _$ProvisionSearchFiltersImpl value,
          $Res Function(_$ProvisionSearchFiltersImpl) then) =
      __$$ProvisionSearchFiltersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime? dateFrom,
      DateTime? dateTo,
      String? drawingNumber,
      String? name,
      String? material,
      String? feature,
      ProvisionsFilter? filterType,
      double? massFrom,
      double? massTo,
      int? quantityFrom,
      int? quantityTo,
      String? requirements,
      String? materialRequirements,
      int? priority,
      String? responsiblePerson,
      DateTime? taskDateFrom,
      DateTime? taskDateTo,
      List<ContractStatus>? supplyStatus,
      DateTime? plannedContractDateFrom,
      DateTime? plannedContractDateTo,
      List<String>? lotNumbers,
      List<String>? lotNames,
      String? contractNumber,
      String? supplier,
      DateTime? deliveryDateFrom,
      DateTime? deliveryDateTo,
      double? costFrom,
      double? costTo,
      String projectId,
      List<String>? productIds,
      List<String>? lotIds,
      List<String>? contractIds});
}

/// @nodoc
class __$$ProvisionSearchFiltersImplCopyWithImpl<$Res>
    extends _$ProvisionSearchFiltersCopyWithImpl<$Res,
        _$ProvisionSearchFiltersImpl>
    implements _$$ProvisionSearchFiltersImplCopyWith<$Res> {
  __$$ProvisionSearchFiltersImplCopyWithImpl(
      _$ProvisionSearchFiltersImpl _value,
      $Res Function(_$ProvisionSearchFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProvisionSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? drawingNumber = freezed,
    Object? name = freezed,
    Object? material = freezed,
    Object? feature = freezed,
    Object? filterType = freezed,
    Object? massFrom = freezed,
    Object? massTo = freezed,
    Object? quantityFrom = freezed,
    Object? quantityTo = freezed,
    Object? requirements = freezed,
    Object? materialRequirements = freezed,
    Object? priority = freezed,
    Object? responsiblePerson = freezed,
    Object? taskDateFrom = freezed,
    Object? taskDateTo = freezed,
    Object? supplyStatus = freezed,
    Object? plannedContractDateFrom = freezed,
    Object? plannedContractDateTo = freezed,
    Object? lotNumbers = freezed,
    Object? lotNames = freezed,
    Object? contractNumber = freezed,
    Object? supplier = freezed,
    Object? deliveryDateFrom = freezed,
    Object? deliveryDateTo = freezed,
    Object? costFrom = freezed,
    Object? costTo = freezed,
    Object? projectId = null,
    Object? productIds = freezed,
    Object? lotIds = freezed,
    Object? contractIds = freezed,
  }) {
    return _then(_$ProvisionSearchFiltersImpl(
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      drawingNumber: freezed == drawingNumber
          ? _value.drawingNumber
          : drawingNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as String?,
      feature: freezed == feature
          ? _value.feature
          : feature // ignore: cast_nullable_to_non_nullable
              as String?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      massFrom: freezed == massFrom
          ? _value.massFrom
          : massFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      massTo: freezed == massTo
          ? _value.massTo
          : massTo // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityFrom: freezed == quantityFrom
          ? _value.quantityFrom
          : quantityFrom // ignore: cast_nullable_to_non_nullable
              as int?,
      quantityTo: freezed == quantityTo
          ? _value.quantityTo
          : quantityTo // ignore: cast_nullable_to_non_nullable
              as int?,
      requirements: freezed == requirements
          ? _value.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      responsiblePerson: freezed == responsiblePerson
          ? _value.responsiblePerson
          : responsiblePerson // ignore: cast_nullable_to_non_nullable
              as String?,
      taskDateFrom: freezed == taskDateFrom
          ? _value.taskDateFrom
          : taskDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      taskDateTo: freezed == taskDateTo
          ? _value.taskDateTo
          : taskDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      supplyStatus: freezed == supplyStatus
          ? _value._supplyStatus
          : supplyStatus // ignore: cast_nullable_to_non_nullable
              as List<ContractStatus>?,
      plannedContractDateFrom: freezed == plannedContractDateFrom
          ? _value.plannedContractDateFrom
          : plannedContractDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      plannedContractDateTo: freezed == plannedContractDateTo
          ? _value.plannedContractDateTo
          : plannedContractDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lotNumbers: freezed == lotNumbers
          ? _value._lotNumbers
          : lotNumbers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      lotNames: freezed == lotNames
          ? _value._lotNames
          : lotNames // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryDateFrom: freezed == deliveryDateFrom
          ? _value.deliveryDateFrom
          : deliveryDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateTo: freezed == deliveryDateTo
          ? _value.deliveryDateTo
          : deliveryDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      costFrom: freezed == costFrom
          ? _value.costFrom
          : costFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      costTo: freezed == costTo
          ? _value.costTo
          : costTo // ignore: cast_nullable_to_non_nullable
              as double?,
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
      productIds: freezed == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      lotIds: freezed == lotIds
          ? _value._lotIds
          : lotIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractIds: freezed == contractIds
          ? _value._contractIds
          : contractIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProvisionSearchFiltersImpl implements _ProvisionSearchFilters {
  const _$ProvisionSearchFiltersImpl(
      {this.dateFrom,
      this.dateTo,
      this.drawingNumber,
      this.name,
      this.material,
      this.feature,
      this.filterType,
      this.massFrom,
      this.massTo,
      this.quantityFrom,
      this.quantityTo,
      this.requirements,
      this.materialRequirements,
      this.priority,
      this.responsiblePerson,
      this.taskDateFrom,
      this.taskDateTo,
      final List<ContractStatus>? supplyStatus,
      this.plannedContractDateFrom,
      this.plannedContractDateTo,
      final List<String>? lotNumbers,
      final List<String>? lotNames,
      this.contractNumber,
      this.supplier,
      this.deliveryDateFrom,
      this.deliveryDateTo,
      this.costFrom,
      this.costTo,
      required this.projectId,
      final List<String>? productIds,
      final List<String>? lotIds,
      final List<String>? contractIds})
      : _supplyStatus = supplyStatus,
        _lotNumbers = lotNumbers,
        _lotNames = lotNames,
        _productIds = productIds,
        _lotIds = lotIds,
        _contractIds = contractIds;

  factory _$ProvisionSearchFiltersImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProvisionSearchFiltersImplFromJson(json);

// Основные фильтры
  @override
  final DateTime? dateFrom;
  @override
  final DateTime? dateTo;
  @override
  final String? drawingNumber;
  @override
  final String? name;
  @override
  final String? material;
  @override
  final String? feature;
  @override
  final ProvisionsFilter? filterType;
// Числовые диапазоны
  @override
  final double? massFrom;
  @override
  final double? massTo;
  @override
  final int? quantityFrom;
  @override
  final int? quantityTo;
// Текстовые поля
  @override
  final String? requirements;
  @override
  final String? materialRequirements;
  @override
  final int? priority;
// Ответственный
  @override
  final String? responsiblePerson;
  @override
  final DateTime? taskDateFrom;
  @override
  final DateTime? taskDateTo;
// Статус и контракт
  final List<ContractStatus>? _supplyStatus;
// Статус и контракт
  @override
  List<ContractStatus>? get supplyStatus {
    final value = _supplyStatus;
    if (value == null) return null;
    if (_supplyStatus is EqualUnmodifiableListView) return _supplyStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? plannedContractDateFrom;
  @override
  final DateTime? plannedContractDateTo;
  final List<String>? _lotNumbers;
  @override
  List<String>? get lotNumbers {
    final value = _lotNumbers;
    if (value == null) return null;
    if (_lotNumbers is EqualUnmodifiableListView) return _lotNumbers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _lotNames;
  @override
  List<String>? get lotNames {
    final value = _lotNames;
    if (value == null) return null;
    if (_lotNames is EqualUnmodifiableListView) return _lotNames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Поставщик и финансы
  @override
  final String? contractNumber;
  @override
  final String? supplier;
  @override
  final DateTime? deliveryDateFrom;
  @override
  final DateTime? deliveryDateTo;
  @override
  final double? costFrom;
  @override
  final double? costTo;
// ID фильтры
  @override
  final String projectId;
  final List<String>? _productIds;
  @override
  List<String>? get productIds {
    final value = _productIds;
    if (value == null) return null;
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _lotIds;
  @override
  List<String>? get lotIds {
    final value = _lotIds;
    if (value == null) return null;
    if (_lotIds is EqualUnmodifiableListView) return _lotIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _contractIds;
  @override
  List<String>? get contractIds {
    final value = _contractIds;
    if (value == null) return null;
    if (_contractIds is EqualUnmodifiableListView) return _contractIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProvisionSearchFilters(dateFrom: $dateFrom, dateTo: $dateTo, drawingNumber: $drawingNumber, name: $name, material: $material, feature: $feature, filterType: $filterType, massFrom: $massFrom, massTo: $massTo, quantityFrom: $quantityFrom, quantityTo: $quantityTo, requirements: $requirements, materialRequirements: $materialRequirements, priority: $priority, responsiblePerson: $responsiblePerson, taskDateFrom: $taskDateFrom, taskDateTo: $taskDateTo, supplyStatus: $supplyStatus, plannedContractDateFrom: $plannedContractDateFrom, plannedContractDateTo: $plannedContractDateTo, lotNumbers: $lotNumbers, lotNames: $lotNames, contractNumber: $contractNumber, supplier: $supplier, deliveryDateFrom: $deliveryDateFrom, deliveryDateTo: $deliveryDateTo, costFrom: $costFrom, costTo: $costTo, projectId: $projectId, productIds: $productIds, lotIds: $lotIds, contractIds: $contractIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProvisionSearchFiltersImpl &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo) &&
            (identical(other.drawingNumber, drawingNumber) ||
                other.drawingNumber == drawingNumber) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.material, material) ||
                other.material == material) &&
            (identical(other.feature, feature) || other.feature == feature) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.massFrom, massFrom) ||
                other.massFrom == massFrom) &&
            (identical(other.massTo, massTo) || other.massTo == massTo) &&
            (identical(other.quantityFrom, quantityFrom) ||
                other.quantityFrom == quantityFrom) &&
            (identical(other.quantityTo, quantityTo) ||
                other.quantityTo == quantityTo) &&
            (identical(other.requirements, requirements) ||
                other.requirements == requirements) &&
            (identical(other.materialRequirements, materialRequirements) ||
                other.materialRequirements == materialRequirements) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.responsiblePerson, responsiblePerson) ||
                other.responsiblePerson == responsiblePerson) &&
            (identical(other.taskDateFrom, taskDateFrom) ||
                other.taskDateFrom == taskDateFrom) &&
            (identical(other.taskDateTo, taskDateTo) ||
                other.taskDateTo == taskDateTo) &&
            const DeepCollectionEquality()
                .equals(other._supplyStatus, _supplyStatus) &&
            (identical(
                    other.plannedContractDateFrom, plannedContractDateFrom) ||
                other.plannedContractDateFrom == plannedContractDateFrom) &&
            (identical(other.plannedContractDateTo, plannedContractDateTo) ||
                other.plannedContractDateTo == plannedContractDateTo) &&
            const DeepCollectionEquality()
                .equals(other._lotNumbers, _lotNumbers) &&
            const DeepCollectionEquality().equals(other._lotNames, _lotNames) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.supplier, supplier) ||
                other.supplier == supplier) &&
            (identical(other.deliveryDateFrom, deliveryDateFrom) ||
                other.deliveryDateFrom == deliveryDateFrom) &&
            (identical(other.deliveryDateTo, deliveryDateTo) ||
                other.deliveryDateTo == deliveryDateTo) &&
            (identical(other.costFrom, costFrom) ||
                other.costFrom == costFrom) &&
            (identical(other.costTo, costTo) || other.costTo == costTo) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            const DeepCollectionEquality().equals(other._lotIds, _lotIds) &&
            const DeepCollectionEquality()
                .equals(other._contractIds, _contractIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        dateFrom,
        dateTo,
        drawingNumber,
        name,
        material,
        feature,
        filterType,
        massFrom,
        massTo,
        quantityFrom,
        quantityTo,
        requirements,
        materialRequirements,
        priority,
        responsiblePerson,
        taskDateFrom,
        taskDateTo,
        const DeepCollectionEquality().hash(_supplyStatus),
        plannedContractDateFrom,
        plannedContractDateTo,
        const DeepCollectionEquality().hash(_lotNumbers),
        const DeepCollectionEquality().hash(_lotNames),
        contractNumber,
        supplier,
        deliveryDateFrom,
        deliveryDateTo,
        costFrom,
        costTo,
        projectId,
        const DeepCollectionEquality().hash(_productIds),
        const DeepCollectionEquality().hash(_lotIds),
        const DeepCollectionEquality().hash(_contractIds)
      ]);

  /// Create a copy of ProvisionSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProvisionSearchFiltersImplCopyWith<_$ProvisionSearchFiltersImpl>
      get copyWith => __$$ProvisionSearchFiltersImplCopyWithImpl<
          _$ProvisionSearchFiltersImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProvisionSearchFiltersImplToJson(
      this,
    );
  }
}

abstract class _ProvisionSearchFilters implements ProvisionSearchFilters {
  const factory _ProvisionSearchFilters(
      {final DateTime? dateFrom,
      final DateTime? dateTo,
      final String? drawingNumber,
      final String? name,
      final String? material,
      final String? feature,
      final ProvisionsFilter? filterType,
      final double? massFrom,
      final double? massTo,
      final int? quantityFrom,
      final int? quantityTo,
      final String? requirements,
      final String? materialRequirements,
      final int? priority,
      final String? responsiblePerson,
      final DateTime? taskDateFrom,
      final DateTime? taskDateTo,
      final List<ContractStatus>? supplyStatus,
      final DateTime? plannedContractDateFrom,
      final DateTime? plannedContractDateTo,
      final List<String>? lotNumbers,
      final List<String>? lotNames,
      final String? contractNumber,
      final String? supplier,
      final DateTime? deliveryDateFrom,
      final DateTime? deliveryDateTo,
      final double? costFrom,
      final double? costTo,
      required final String projectId,
      final List<String>? productIds,
      final List<String>? lotIds,
      final List<String>? contractIds}) = _$ProvisionSearchFiltersImpl;

  factory _ProvisionSearchFilters.fromJson(Map<String, dynamic> json) =
      _$ProvisionSearchFiltersImpl.fromJson;

// Основные фильтры
  @override
  DateTime? get dateFrom;
  @override
  DateTime? get dateTo;
  @override
  String? get drawingNumber;
  @override
  String? get name;
  @override
  String? get material;
  @override
  String? get feature;
  @override
  ProvisionsFilter? get filterType; // Числовые диапазоны
  @override
  double? get massFrom;
  @override
  double? get massTo;
  @override
  int? get quantityFrom;
  @override
  int? get quantityTo; // Текстовые поля
  @override
  String? get requirements;
  @override
  String? get materialRequirements;
  @override
  int? get priority; // Ответственный
  @override
  String? get responsiblePerson;
  @override
  DateTime? get taskDateFrom;
  @override
  DateTime? get taskDateTo; // Статус и контракт
  @override
  List<ContractStatus>? get supplyStatus;
  @override
  DateTime? get plannedContractDateFrom;
  @override
  DateTime? get plannedContractDateTo;
  @override
  List<String>? get lotNumbers;
  @override
  List<String>? get lotNames; // Поставщик и финансы
  @override
  String? get contractNumber;
  @override
  String? get supplier;
  @override
  DateTime? get deliveryDateFrom;
  @override
  DateTime? get deliveryDateTo;
  @override
  double? get costFrom;
  @override
  double? get costTo; // ID фильтры
  @override
  String get projectId;
  @override
  List<String>? get productIds;
  @override
  List<String>? get lotIds;
  @override
  List<String>? get contractIds;

  /// Create a copy of ProvisionSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProvisionSearchFiltersImplCopyWith<_$ProvisionSearchFiltersImpl>
      get copyWith => throw _privateConstructorUsedError;
}
