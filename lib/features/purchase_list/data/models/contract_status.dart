import 'package:freezed_annotation/freezed_annotation.dart';

/// Статусы контрактов для фильтрации
enum ContractStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('signed')
  signed,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}

extension ContractStatusX on ContractStatus {
  String get displayName {
    switch (this) {
      case ContractStatus.draft:
        return 'Черновик';
      case ContractStatus.inProgress:
        return 'В работе';
      case ContractStatus.signed:
        return 'Подписан';
      case ContractStatus.completed:
        return 'Выполнен';
      case ContractStatus.cancelled:
        return 'Отменен';
    }
  }
  
  String get jsonValue {
    switch (this) {
      case ContractStatus.draft:
        return 'draft';
      case ContractStatus.inProgress:
        return 'in_progress';
      case ContractStatus.signed:
        return 'signed';
      case ContractStatus.completed:
        return 'completed';
      case ContractStatus.cancelled:
        return 'cancelled';
    }
  }
}