// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supply.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SupplyModelImpl _$$SupplyModelImplFromJson(Map<String, dynamic> json) =>
    _$SupplyModelImpl(
      id: json['_id'] as String?,
      forInputs: (json['forInputs'] as List<dynamic>?)
          ?.map((e) => SupplyInputModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      state: $enumDecodeNullable(_$SupplyStateEnumMap, json['state']),
      supplyDate: json['supplyDate'] == null
          ? null
          : DateTime.parse(json['supplyDate'] as String),
      payments: (json['payments'] as List<dynamic>?)
          ?.map((e) => SupplyPaymentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      quantity: (json['quantity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$SupplyModelImplToJson(_$SupplyModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.forInputs case final value?) 'forInputs': value,
      if (_$SupplyStateEnumMap[instance.state] case final value?)
        'state': value,
      if (instance.supplyDate?.toIso8601String() case final value?)
        'supplyDate': value,
      if (instance.payments case final value?) 'payments': value,
      if (instance.quantity case final value?) 'quantity': value,
    };

const _$SupplyStateEnumMap = {
  SupplyState.storage: 'ready',
  SupplyState.ready: 'ready',
  SupplyState.almostReady: 'almostReady',
  SupplyState.partial: 'partial',
  SupplyState.wrong: 'wrong',
  SupplyState.inProcess: 'inProcess',
  SupplyState.error: 'error',
};

_$SupplyPaymentModelImpl _$$SupplyPaymentModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SupplyPaymentModelImpl(
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      cost: (json['cost'] as num?)?.toDouble(),
      state: $enumDecodeNullable(_$SupplyPaymentStateEnumMap, json['state']),
    );

Map<String, dynamic> _$$SupplyPaymentModelImplToJson(
        _$SupplyPaymentModelImpl instance) =>
    <String, dynamic>{
      if (instance.date?.toIso8601String() case final value?) 'date': value,
      if (instance.cost case final value?) 'cost': value,
      if (_$SupplyPaymentStateEnumMap[instance.state] case final value?)
        'state': value,
    };

const _$SupplyPaymentStateEnumMap = {
  SupplyPaymentState.ready: 'ready',
  SupplyPaymentState.inProcess: 'inProcess',
};

_$SupplyInputModelImpl _$$SupplyInputModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SupplyInputModelImpl(
      provisionItemId: json['provisionItemId'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$SupplyInputModelImplToJson(
        _$SupplyInputModelImpl instance) =>
    <String, dynamic>{
      if (instance.provisionItemId case final value?) 'provisionItemId': value,
      if (instance.quantity case final value?) 'quantity': value,
    };
