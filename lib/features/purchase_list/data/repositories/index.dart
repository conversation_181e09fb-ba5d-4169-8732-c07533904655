import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/shared/data/datasources/api.dart';

class PurchaseListRepository {
  static Future<Response<ProvisionsListModel>> getMaterials({
    String? projectId,
    ProvisionsGetMaterials? data,
  }) async {
    final body = data?.toJson() ?? {};

    final args = [
      if (projectId != null) 'projectId=$projectId',
    ];

    final request = await API.request<ProvisionsListModel>(
      url: '/provisions/getMaterials?${args.join('&')}',
      body: body,
      method: 'POST',
      fromJson: ProvisionsListModel.fromJson,
    );

    return request;
  }

  static Future<Response> create({
    required ProvisionsCreateModel data,
  }) async {
    final body = data.toJson();

    final request = await API.request(
      url: '/provisions/create',
      body: body,
      method: 'POST',
      // fromJson: ProvisionMaterialModel.fromJson,
      // options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  static Future<Response> removeItems({
    required List<String> provisionItemIds,
  }) async {
    final body = jsonEncode({
      'provisionItemIds': provisionItemIds,
    });

    final request = await API.request(
      url: '/provisions/removeItems',
      body: body,
      method: 'POST',
      // fromJson: ProvisionMaterialModel.fromJson,
      // options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  // static Future<Response> export({
  //   required String provisionId,
  // }) async {
  //   // final body = data.toJson();

  //   final request = await API.request(
  //     url: '/provisions/export/$provisionId',
  //     // body: body,
  //     method: 'GET',
  //     // fromJson: ProvisionMaterialModel.fromJson,
  //     // options: Options(contentType: 'multipart/form-data'),
  //   );

  //   return request;
  // }

  static Future<Response?> contract({
    required String provisionId,
    String? supplierId,
    String? supplierName,
    String? contractNumber,
    DateTime? contractDate,
    double? contractPrice,
    required DateTime contractStartDate,
    required DateTime contractEndDate,
    // required List<ContractItemModel> contractItems,
    required File file,
    List<ProvisionPaymentDetailsModel>? paymentDetails,
    required List<DeliveryGroupModel> deliveryGroups,
  }) async {
    final body = FormData.fromMap({
      'provisionId': provisionId,
      if (supplierId == null && supplierName != null)
        'supplierName': supplierName,
      if (supplierId != null) 'supplierId': supplierId,
      'contractNumber': contractNumber,
      'contractDate': (contractDate ?? DateTime.now()).toIso8601String(),
      'contractStartDate': contractStartDate.toIso8601String(),
      'contractEndDate': contractEndDate.toIso8601String(),
      if (contractPrice != null) 'contractPrice': contractPrice,

      // Delivery groups data
      'deliveryGroups': deliveryGroups
          .map((group) => {
                'expectedDate': group.expectedDate?.toIso8601String(),
                'materials': group.materials
                    ?.map((material) => {
                          'provisionItemId': material.provisionItemId,
                          'quantity': material.quantity,
                          if (material.price != null) 'price': material.price,
                        })
                    .toList(),
              })
          .toList(),

      // Payment details
      if (paymentDetails != null)
        'paymentDetails': paymentDetails
            .map((payment) => {
                  'paymentDate': payment.paymentDate?.toIso8601String(),
                  'amount': payment.amount,
                  'description': payment.description ?? 'description',
                })
            .toList(),

      // Contract items
      // 'contractItems': contractItems
      //     .map((item) => {
      //           if (item.materialId != null) 'materialId': item.materialId,
      //           if (item.productId != null) 'productId': item.productId,
      //           if (item.materialRequirements != null &&
      //               item.materialRequirements!.isNotEmpty)
      //             'materialRequirements': item.materialRequirements,
      //           if (item.featureType != null)
      //             'featureType': item.featureType!.getJsonnify(),
      //           'price': double.tryParse(item.costController.text) ?? 0.0,
      //           'quantity':
      //               double.tryParse(item.quantityController.text) ?? 0.0,
      //         })
      //     .toList(),

      // Contract file
      'file': await MultipartFile.fromFile(
        file.path,
        filename: file.path.split('/').last,
        // contentType: DioMediaType('application', 'pdf'),
      ),
    });

    final request = await API.request(
      url: '/provisions/contract',
      body: body,
      method: 'POST',
      options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  static Future<Response?> updateContract({
    required String provisionId,
    String? supplierId,
    String? supplierName,
    String? contractNumber,
    DateTime? contractDate,
    double? contractPrice,
    required DateTime contractStartDate,
    required DateTime contractEndDate,
    // required List<ContractMaterialModel> contractMaterials,
    // required List<ContractItemModel> contractItems,
    File? file,
    List<ProvisionPaymentDetailsModel>? paymentDetails,
    List<DeliveryGroupModel>? deliveryGroups,
  }) async {
    final body = FormData.fromMap({
      'provisionId': provisionId,
      if (supplierId == null && supplierName != null)
        'supplierName': supplierName,
      if (supplierId != null) 'supplierId': supplierId,
      'contractNumber': contractNumber,
      'contractDate': (contractDate ?? DateTime.now()).toIso8601String(),
      'contractStartDate': contractStartDate.toIso8601String(),
      'contractEndDate': contractEndDate.toIso8601String(),
      'contractPrice': contractPrice,
      // 'contractMaterials': contractMaterials
      //     .map((material) => {
      //           'materialId': material.materialId,
      //           if (material.materialRequirements != null)
      //             'materialRequirements': material.materialRequirements,
      //           'quantity': material.quantityController.text,
      //           'price': material.costController.text,
      //         })
      //     .toList(),
      'paymentDetails': paymentDetails
          ?.map((payment) => {
                'paymentDate': payment.paymentDate?.toIso8601String(),
                'amount': payment.amount,
              })
          .toList(),

      // Delivery groups data
      if (deliveryGroups != null)
        'deliveryGroups': deliveryGroups
            .map((group) => {
                  'expectedDate': group.expectedDate?.toIso8601String(),
                  'materials': group.materials
                      ?.map((material) => {
                            'provisionItemId': material.provisionItemId,
                            'quantity': material.quantity,
                            if (material.price != null) 'price': material.price,
                          })
                      .toList(),
                })
            .toList(),
      // 'contractItems': contractItems
      //     .map((item) => {
      //           if (item.materialId != null) 'materialId': item.materialId,
      //           if (item.productId != null) 'productId': item.productId,
      //           if (item.materialRequirements != null &&
      //               item.materialRequirements!.isNotEmpty)
      //             'materialRequirements': item.materialRequirements,
      //           if (item.featureType != null)
      //             'featureType': item.featureType!.getJsonnify(),
      //           'price': double.tryParse(item.costController.text) ?? 0.0,
      //           'quantity':
      //               double.tryParse(item.quantityController.text) ?? 0.0,
      //         })
      //     .toList(),
      if (file != null)
        'file': await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
          // contentType: DioMediaType('application', 'pdf'),
        ),
    });

    final request = await API.request(
      url: '/provisions/contract/update',
      body: body,
      method: 'POST',
      options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  static String export(String provisionId) {
    return '/provisions/export/$provisionId';
  }
}
