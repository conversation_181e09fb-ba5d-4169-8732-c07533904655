// import 'dart:io';

// import 'package:sphere/features/purchase_list/data/models/index.dart';
// import 'package:sphere/features/purchase_list/data/repositories/new.dart';

// /// Пример использования обновленного API для контрактов
// class ContractUpdateExample {
//   /// Пример обновления существующего контракта
//   static Future<void> updateExistingContract() async {
//     try {
//       // Создаем данные для обновления контракта
//       final updateData = ProvisionUpdateContractInput(
//         contractId: 'contract_123', // ID существующего контракта
//         productIds: ['product_1', 'product_2'],
//         contractDate: DateTime.now().toIso8601String(),
//         contractStartDate: DateTime.now().toIso8601String(),
//         contractEndDate:
//             DateTime.now().add(Duration(days: 365)).toIso8601String(),
//         contractPrice: 150000.0,
//         supplierId: 'supplier_456',
//         notes: 'Обновленный контракт с новыми условиями',
//         productDetails: [
//           ProductDetail(
//             productId: 'product_1',
//             price: 75000.0,
//             quantity: 10.0,
//             unit: 'шт',
//             deliveryDate:
//                 DateTime.now().add(Duration(days: 30)).toIso8601String(),
//             notes: 'Стандартная поставка',
//           ),
//           ProductDetail(
//             productId: 'product_2',
//             price: 75000.0,
//             quantity: 5.0,
//             unit: 'кг',
//             deliveryDate:
//                 DateTime.now().add(Duration(days: 45)).toIso8601String(),
//             notes: 'Требуется особая упаковка',
//           ),
//         ],
//         paymentDetails: [
//           PaymentDetail(
//             paymentDate:
//                 DateTime.now().add(Duration(days: 15)).toIso8601String(),
//             amount: 75000.0,
//           ),
//           PaymentDetail(
//             paymentDate:
//                 DateTime.now().add(Duration(days: 60)).toIso8601String(),
//             amount: 75000.0,
//           ),
//         ],
//       );

//       // Файлы для загрузки (опционально)
//       final files = <File>[
//         // File('/path/to/updated_contract.pdf'),
//       ];

//       // Выполняем обновление контракта
//       final response = await PurchaseListRepositoryV2.updateContract(
//         updateData,
//         files,
//       );

//       if (response.statusCode == 200 || response.statusCode == 201) {
//         print('Контракт успешно обновлен');
//       } else {
//         print('Ошибка при обновлении контракта: ${response.statusCode}');
//       }
//     } catch (e) {
//       print('Исключение при обновлении контракта: $e');
//     }
//   }

//   /// Пример создания ProvisionUpdateContractInput из существующих данных
//   static ProvisionUpdateContractInput createUpdateFromExisting(
//     String contractId,
//     ProvisionCreateContractInput existingData,
//   ) {
//     return PurchaseListRepositoryV2.createUpdateInput(contractId, existingData);
//   }

//   /// Пример изменения статуса контракта
//   static Future<void> changeContractStatus() async {
//     try {
//       final statusChangeData = ProvisionChangeContractStatusInput(
//         contractId: 'contract_123',
//         action: ContractAction.confirm,
//         reason: 'Все условия выполнены',
//       );

//       final response = await PurchaseListRepositoryV2.changeContractStatus(
//         statusChangeData,
//       );

//       if (response.statusCode == 200 || response.statusCode == 201) {
//         print('Статус контракта успешно изменен');
//       } else {
//         print('Ошибка при изменении статуса: ${response.statusCode}');
//       }
//     } catch (e) {
//       print('Исключение при изменении статуса: $e');
//     }
//   }

//   /// Пример массового изменения статуса контрактов
//   static Future<void> batchChangeContractStatus(
//       List<String> contractIds) async {
//     int successCount = 0;
//     final failedContracts = <String>[];

//     for (final contractId in contractIds) {
//       try {
//         final statusChangeData = ProvisionChangeContractStatusInput(
//           contractId: contractId,
//           action: ContractAction.confirm,
//           reason: 'Массовое подтверждение контрактов',
//         );

//         final response = await PurchaseListRepositoryV2.changeContractStatus(
//           statusChangeData,
//         );

//         if (response.statusCode == 200 || response.statusCode == 201) {
//           successCount++;
//         } else {
//           failedContracts.add(contractId);
//         }
//       } catch (e) {
//         failedContracts.add(contractId);
//       }
//     }

//     print('Успешно изменено: $successCount контрактов');
//     if (failedContracts.isNotEmpty) {
//       print('Ошибки для контрактов: ${failedContracts.join(', ')}');
//     }
//   }

//   /// Пример всех доступных действий с контрактами
//   static Future<void> demonstrateAllActions() async {
//     final contractId = 'contract_123';

//     // Подтверждение контракта
//     await _performAction(
//         contractId, ContractAction.confirm, 'Контракт проверен и одобрен');

//     // Отмена контракта
//     await _performAction(
//         contractId, ContractAction.revert, 'Найдены ошибки в условиях');

//     // Отмена контракта
//     await _performAction(
//         contractId, ContractAction.cancel, 'Контракт больше не актуален');
//   }

//   static Future<void> _performAction(
//       String contractId, ContractAction action, String reason) async {
//     try {
//       final statusChangeData = ProvisionChangeContractStatusInput(
//         contractId: contractId,
//         action: action,
//         reason: reason,
//       );

//       final response =
//           await PurchaseListRepositoryV2.changeContractStatus(statusChangeData);

//       if (response.statusCode == 200 || response.statusCode == 201) {
//         print(
//             '${action.displayName}: Успешно выполнено для контракта $contractId');
//       } else {
//         print(
//             '${action.displayName}: Ошибка ${response.statusCode} для контракта $contractId');
//       }
//     } catch (e) {
//       print('${action.displayName}: Исключение для контракта $contractId - $e');
//     }
//   }

//   /// Пример создания детализированного контракта с полной информацией о продуктах
//   static Future<void> createDetailedContract() async {
//     try {
//       final contractInput = ProvisionCreateContractInput(
//         productIds: ['electronics_001', 'furniture_002', 'supplies_003'],
//         contractDate: DateTime.now().toIso8601String(),
//         contractStartDate:
//             DateTime.now().add(Duration(days: 7)).toIso8601String(),
//         contractEndDate:
//             DateTime.now().add(Duration(days: 365)).toIso8601String(),
//         contractPrice: 285000.0,
//         supplierId: 'supplier_tech_solutions',
//         notes: 'Комплексная поставка оборудования и мебели для офиса',

//         // Детализированная информация по каждому продукту
//         productDetails: [
//           // Электроника
//           ProductDetail(
//             productId: 'electronics_001',
//             price: 150000.0,
//             quantity: 10.0,
//             unit: 'шт',
//             deliveryDate:
//                 DateTime.now().add(Duration(days: 14)).toIso8601String(),
//             notes: 'Ноутбуки с предустановленным ПО, гарантия 3 года',
//           ),

//           // Мебель
//           ProductDetail(
//             productId: 'furniture_002',
//             price: 85000.0,
//             quantity: 25.0,
//             unit: 'шт',
//             deliveryDate:
//                 DateTime.now().add(Duration(days: 21)).toIso8601String(),
//             notes: 'Офисные столы, требуется сборка на месте',
//           ),

//           // Расходные материалы
//           ProductDetail(
//             productId: 'supplies_003',
//             price: 50000.0,
//             quantity: 100.0,
//             unit: 'упак',
//             deliveryDate:
//                 DateTime.now().add(Duration(days: 7)).toIso8601String(),
//             notes: 'Канцелярские принадлежности, ежемесячная поставка',
//           ),
//         ],

//         // График платежей
//         paymentDetails: [
//           PaymentDetail(
//             paymentDate:
//                 DateTime.now().add(Duration(days: 30)).toIso8601String(),
//             amount: 142500.0, // 50% предоплата
//           ),
//           PaymentDetail(
//             paymentDate:
//                 DateTime.now().add(Duration(days: 60)).toIso8601String(),
//             amount: 142500.0, // 50% по факту поставки
//           ),
//         ],
//       );

//       final files = <File>[
//         // File('/path/to/contract_specification.pdf'),
//         // File('/path/to/technical_requirements.pdf'),
//       ];

//       final response =
//           await PurchaseListRepositoryV2.contract(contractInput, files);

//       if (response.statusCode == 200 || response.statusCode == 201) {
//         print('Детализированный контракт успешно создан');
//         print('Общая сумма: ${contractInput.contractPrice} ₽');
//         print(
//             'Количество продуктов: ${contractInput.productDetails?.length ?? 0}');
//         print(
//             'Количество платежей: ${contractInput.paymentDetails?.length ?? 0}');
//       } else {
//         print('Ошибка при создании контракта: ${response.statusCode}');
//       }
//     } catch (e) {
//       print('Исключение при создании детализированного контракта: $e');
//     }
//   }
// }
