import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class PurchaseListRepositoryV2 {
  static Future<Response<GetRequirementsOutput>> getRequirements(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<GetRequirementsOutput>(
      url: '/provisions/requirements',
      body: body,
      method: 'POST',
      fromJson: GetRequirementsOutput.fromJson,
    );

    return request;
  }

  static Future<Response<ProvisionsColumnOptionsOutput>> getColumnOptions(
    ProvisionsColumnOptionsInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProvisionsColumnOptionsOutput>(
      url: '/provisions/requirements/column-options',
      body: body,
      method: 'POST',
      fromJson: ProvisionsColumnOptionsOutput.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>> createLot(
    ProvisionCreateLotInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/provisions/lots/create',
      body: body,
      method: 'POST',
      // fromJson: ProvisionsColumnOptionsOutput.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>> addToLot(
    ProvisionAddToLotInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/provisions/lots/add-products',
      body: body,
      method: 'POST',
      // fromJson: ProvisionsColumnOptionsOutput.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>> removeFromLot(
    ProvisionAddToLotInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/provisions/lots/remove-products',
      body: body,
      method: 'POST',
      // fromJson: ProvisionsColumnOptionsOutput.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>> contract(
    ProvisionCreateContractInput data,
    List<File> files,
  ) async {
    try {
      // Формируем FormData для отправки данных и файлов
      final formData = FormData();

      // Добавляем поля контракта
      formData.fields.addAll([
        MapEntry('productIds', jsonEncode(data.productIds)),
        // MapEntry('contractNumber', data.contractNumber),
        MapEntry('contractDate', data.contractDate),
        MapEntry('contractPrice', data.contractPrice.toString()),
        MapEntry(
            'filterType',
            data.filterType?.getBackendValue() ??
                ProvisionsFilter.cooperation.getBackendValue())
      ]);

      // Добавляем опциональные поля
      if (data.contractStartDate != null) {
        formData.fields
            .add(MapEntry('contractStartDate', data.contractStartDate!));
      }
      if (data.contractEndDate != null) {
        formData.fields.add(MapEntry('contractEndDate', data.contractEndDate!));
      }
      if (data.supplierId != null) {
        formData.fields.add(MapEntry('supplierId', data.supplierId!));
      }
      // if (data.plannedDeliveryDate != null) {
      //   formData.fields
      //       .add(MapEntry('plannedDeliveryDate', data.plannedDeliveryDate!));
      // }
      if (data.notes != null) {
        formData.fields.add(MapEntry('notes', data.notes!));
      }
      if (data.productDetails != null && data.productDetails!.isNotEmpty) {
        formData.fields.add(MapEntry('productDetails',
            jsonEncode(data.productDetails!.map((e) => e.toJson()).toList())));
      }
      if (data.paymentDetails != null && data.paymentDetails!.isNotEmpty) {
        formData.fields.add(MapEntry('paymentDetails',
            jsonEncode(data.paymentDetails!.map((e) => e.toJson()).toList())));
      }

      // Добавляем файлы по отдельности
      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        formData.files.add(MapEntry(
          'files',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
            contentType: DioMediaType('application', 'pdf'),
          ),
        ));
      }

      // Выполняем запрос
      final request = await API.request<dynamic>(
        url: '/provisions/contract',
        body: formData,
        method: 'POST',
        options: Options(
          contentType: 'multipart/form-data',
        ),
      );

      if (request.statusCode != 200 && request.statusCode != 201) {
        throw DioException(
          requestOptions: request.requestOptions,
          response: request,
          error: 'Contract creation failed with status ${request.statusCode}',
        );
      }

      return request;
    } on DioException catch (e) {
      // Re-throw Dio-specific errors with additional context
      throw DioException(
        requestOptions: e.requestOptions,
        response: e.response,
        error: 'Failed to create contract: ${e.error}',
      );
    } catch (e, stackTrace) {
      // Wrap other exceptions with more context
      throw Exception(
          'Failed to create contract: $e\nStack trace: $stackTrace');
    }
  }

  static Future<Response<dynamic>> updateContract(
    ProvisionUpdateContractInput data,
    List<File> files,
  ) async {
    try {
      // Формируем FormData для отправки данных и файлов
      final formData = FormData();

      // Добавляем поля контракта
      formData.fields.addAll([
        MapEntry('contractId', data.contractId),
        // MapEntry('productIds', jsonEncode(data.productIds)),
        // MapEntry('contractNumber', data.contractNumber),
        MapEntry('contractDate', data.contractDate),
        MapEntry('contractPrice', data.contractPrice.toString()),
        MapEntry(
            'filterType',
            data.filterType?.getBackendValue() ??
                ProvisionsFilter.cooperation.getBackendValue())
      ]);

      // Добавляем опциональные поля
      if (data.contractStartDate != null) {
        formData.fields
            .add(MapEntry('contractStartDate', data.contractStartDate!));
      }
      if (data.contractEndDate != null) {
        formData.fields.add(MapEntry('contractEndDate', data.contractEndDate!));
      }
      if (data.supplierId != null) {
        formData.fields.add(MapEntry('supplierId', data.supplierId!));
      }
      // if (data.plannedDeliveryDate != null) {
      //   formData.fields
      //       .add(MapEntry('plannedDeliveryDate', data.plannedDeliveryDate!));
      // }
      if (data.notes != null) {
        formData.fields.add(MapEntry('notes', data.notes!));
      }
      if (data.productDetails != null && data.productDetails!.isNotEmpty) {
        formData.fields.add(MapEntry('productDetails',
            jsonEncode(data.productDetails!.map((e) => e.toJson()).toList())));
      }
      if (data.paymentDetails != null && data.paymentDetails!.isNotEmpty) {
        formData.fields.add(MapEntry('paymentDetails',
            jsonEncode(data.paymentDetails!.map((e) => e.toJson()).toList())));
      }

      // Добавляем файлы по отдельности (файлы опциональны при обновлении)
      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        formData.files.add(MapEntry(
          'files',
          await MultipartFile.fromFile(
            file.path,
            filename: file.path.split('/').last,
            contentType: DioMediaType('application', 'pdf'),
          ),
        ));
      }

      // Выполняем запрос на обновление
      final request = await API.request<dynamic>(
        url: '/provisions/contract/update',
        body: formData,
        method: 'POST',
        options: Options(
          contentType: 'multipart/form-data',
        ),
      );

      if (request.statusCode != 200 && request.statusCode != 201) {
        throw DioException(
          requestOptions: request.requestOptions,
          response: request,
          error: 'Contract update failed with status ${request.statusCode}',
        );
      }

      return request;
    } on DioException catch (e) {
      // Re-throw Dio-specific errors with additional context
      throw DioException(
        requestOptions: e.requestOptions,
        response: e.response,
        error: 'Failed to update contract: ${e.error}',
      );
    } catch (e, stackTrace) {
      // Wrap other exceptions with more context
      throw Exception(
          'Failed to update contract: $e\nStack trace: $stackTrace');
    }
  }

  static Future<Response<ProvisionChangeContractStatusResponse>>
      changeContractStatus(
    ProvisionChangeContractStatusInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProvisionChangeContractStatusResponse>(
      url: '/provisions/contract/status',
      body: body,
      method: 'POST',
      fromJson: ProvisionChangeContractStatusResponse.fromJson,
    );

    return request;
  }

  static Future<Response<Uint8List>> export(
    String projectId,
    SearchFiltersModel filters,
  ) {
    final data = jsonEncode({
      'projectId': projectId,
      'filters': filters.toJson(),
      'includeDrawings': true,
    });

    return API.request<Uint8List>(
      url: '/provisions/export',
      method: 'POST',
      body: data,
      options: Options(responseType: ResponseType.bytes),
    );
  }

  static String getExportUrl(String projectId, SearchFiltersModel filters) {
    final filtersJson = Uri.encodeComponent(jsonEncode(filters.toJson()));
    return '/provisions/export?projectId=$projectId&filters=$filtersJson';
  }

  /// Создает ProvisionUpdateContractInput из ProvisionCreateContractInput и contractId
  static ProvisionUpdateContractInput createUpdateInput(
    String contractId,
    ProvisionCreateContractInput createInput,
  ) {
    return ProvisionUpdateContractInput(
      contractId: contractId,
      productIds: createInput.productIds,
      contractDate: createInput.contractDate,
      contractStartDate: createInput.contractStartDate,
      contractEndDate: createInput.contractEndDate,
      supplierId: createInput.supplierId,
      plannedDeliveryDate: createInput.plannedDeliveryDate,
      contractPrice: createInput.contractPrice,
      productDetails: createInput.productDetails,
      paymentDetails: createInput.paymentDetails,
      filterType: createInput.filterType,
      notes: createInput.notes,
    );
  }

  // API методы для складов

  /// Создание нового склада
  /// Доступно только для админов и главных менеджеров
  static Future<Response<dynamic>> createWarehouse(
    WarehouseCreateInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/warehouses',
      body: body,
      method: 'POST',
    );

    return request;
  }

  /// Удаление склада
  /// Доступно только для админов
  static Future<Response<dynamic>> deleteWarehouse(
    WarehouseDeleteInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/warehouses/delete',
      body: body,
      method: 'POST',
    );

    return request;
  }

  /// Получение списка всех складов
  static Future<Response<SearchWarehousesOutput>> searchWarehouses({
    String? projectId,
    bool? includeContractors,
    WarehouseType? type,
  }) async {
    final input = WarehouseSearchInput(
      projectId: projectId,
      includeContractors: includeContractors ?? true,
      type: type,
    );

    final request = await API.request<SearchWarehousesOutput>(
      url: '/warehouses/search',
      body: input.toJson(),
      method: 'POST',
      fromJson: SearchWarehousesOutput.fromJson,
    );

    return request;
  }

// Allowed values"general""contractor""virtual_project"
// Example"general"

// OUTPUT:
//   warehouses array<object>
// Items object
// _id string
// ID склада

// type string
// Тип склада

// Allowed values"general""contractor""virtual_project"
// Example"general"
// name string
// Название склада

// address string
// Адрес склада (для физических складов)

// projectId string
// ID проекта (для виртуальных складов проекта)

// physicalWarehouseId string
// ID физического склада (для виртуальных складов)

// contractorId string
// ID контрагента (для складов контрагента)

// keepers array<object>
// Кладовщики склада

// Items object
// isActive boolean
// Активен ли склад

// createdAt stringdate-time
// Дата создания

// updatedAt stringdate-time
// Дата обновления

// createdBy string
// ID пользователя, создавшего склад

// updatedBy string
// ID пользователя, обновившего склад

// total number
// Общее количество складов

  /// Обновление информации о складе
  /// Доступно админам, главным менеджерам и кладовщикам своего склада
  static Future<Response<dynamic>> updateWarehouse(
    WarehouseUpdateInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/warehouses/update',
      body: body,
      method: 'POST',
    );

    return request;
  }

  /// Получение информации о складе по ID
  static Future<Response<Warehouse>> viewWarehouse(
    WarehouseViewInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<Warehouse>(
      url: '/warehouses/view',
      body: body,
      method: 'POST',
      fromJson: Warehouse.fromJson,
    );

    return request;
  }
}
