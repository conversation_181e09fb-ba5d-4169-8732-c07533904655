// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:sphere/features/purchase_list/data/models/index.dart';
// import 'package:sphere/shared/styles/fonts.dart';
// import 'package:sphere/shared/widgets/containers/card/index.dart';

// class ProductDetailsSection extends StatefulWidget {
//   const ProductDetailsSection({
//     super.key,
//     required this.selectedProductIds,
//     required this.productDetails,
//     required this.onProductDetailsChanged,
//   });

//   final List<String> selectedProductIds;
//   final List<ProductDetail> productDetails;
//   final ValueChanged<List<ProductDetail>> onProductDetailsChanged;

//   @override
//   State<ProductDetailsSection> createState() => _ProductDetailsSectionState();
// }

// class _ProductDetailsSectionState extends State<ProductDetailsSection> {
//   late List<ProductDetail> _productDetails;

//   @override
//   void initState() {
//     super.initState();
//     // Отложенная инициализация после завершения построения
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       _initializeProductDetails();
//     });
//   }

//   void _initializeProductDetails() {
//     _productDetails = List.from(widget.productDetails);

//     bool hasChanges = false;

//     // Создаем записи для всех выбранных продуктов, если их еще нет
//     for (final productId in widget.selectedProductIds) {
//       if (!_productDetails.any((detail) => detail.productId == productId)) {
//         _productDetails.add(ProductDetail(
//           productId: productId,
//           price: 0.0,
//           unit: 'шт',
//           distributions: [
//             Distribution(
//               warehouseId: null,
//               quantity: 1.0,
//               deliveryDate: null,
//               notes: null,
//             ),
//           ],
//           notes: null,
//         ));
//         hasChanges = true;
//       }
//     }

//     // Удаляем записи для продуктов, которые больше не выбраны
//     final initialLength = _productDetails.length;
//     _productDetails.removeWhere(
//       (detail) => !widget.selectedProductIds.contains(detail.productId),
//     );
//     if (_productDetails.length != initialLength) {
//       hasChanges = true;
//     }

//     // Отложенное уведомление об изменениях после завершения построения
//     if (hasChanges) {
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         _notifyChange();
//       });
//     }
//   }

//   @override
//   void didUpdateWidget(ProductDetailsSection oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.selectedProductIds != widget.selectedProductIds) {
//       // Отложенная инициализация после завершения построения
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         _initializeProductDetails();
//       });
//     }
//   }

//   void _notifyChange() {
//     if (mounted) {
//       widget.onProductDetailsChanged(_productDetails);
//     }
//   }

//   void _updateProductDetail(int index, ProductDetail updatedDetail) {
//     if (mounted && index >= 0 && index < _productDetails.length) {
//       setState(() {
//         _productDetails[index] = updatedDetail;
//       });
//       _notifyChange();
//     }
//   }

//   Future<void> _pickDate(BuildContext context, int index) async {
//     if (!mounted || index < 0 || index >= _productDetails.length) return;

//     final DateTime? pickedDate = await showDatePicker(
//       context: context,
//       initialDate: DateTime.now(),
//       firstDate: DateTime.now(),
//       lastDate: DateTime(2100),
//     );

//     if (pickedDate != null && mounted) {
//       final formattedDate =
//           '${pickedDate.year}-${pickedDate.month.toString().padLeft(2, '0')}-${pickedDate.day.toString().padLeft(2, '0')}';
//       _updateProductDetail(
//         index,
//         _productDetails[index].copyWith(deliveryDate: formattedDate),
//       );
//     }
//   }

// // productDetails array<object>
// // Детали для каждого продукта (цены, даты, количества)

// // Items object
// // Детальная информация по продукту в контракте (цена, распределения по складам/датам)

// // productId string
// // price number≥ 0
// // unit string
// // distributions array<object>
// // notes string

// // POST
// // /warehouses
// // Создание нового склада

// // Создает новый склад (только для админов и главных менеджеров)

// // Parameters
// // Try it out
// // No parameters

// // Request body

// // application/json
// // Example Value
// // Schema
// // {
// //   "name": "string",
// //   "type": "general",
// //   "address": "string",
// //   "description": "string"
// // }
// // Responses
// // Code	Description	Links
// // 201
// // Склад успешно создан

// // No links

// // POST
// // /warehouses/delete
// // Удаление склада

// // Удаляет склад (только для админов)

// // Parameters
// // Try it out
// // No parameters

// // Request body

// // application/json
// // Example Value
// // Schema
// // {
// //   "warehouseId": "FE8799164e455C35bF16B632"
// // }
// // Responses
// // Code	Description	Links
// // 200
// // Склад успешно удален

// // No links

// // POST
// // /warehouses/search
// // Получение списка складов

// // Возвращает список всех складов

// // Parameters
// // Try it out
// // No parameters

// // Responses
// // Code	Description	Links
// // 200
// // Список складов успешно получен

// // No links

// // POST
// // /warehouses/update
// // Обновление склада

// // Обновляет информацию о складе (доступно админам, главным менеджерам и кладовщикам своего склада)

// // Parameters
// // Try it out
// // No parameters

// // Request body

// // application/json
// // Example Value
// // Schema
// // {
// //   "warehouseId": "cf9FC2280c8BEda5F0Bc57Fd",
// //   "name": "string",
// //   "address": "string",
// //   "description": "string"
// // }
// // Responses
// // Code	Description	Links
// // 200
// // Склад успешно обновлен

// // No links

// // POST
// // /warehouses/view
// // Получение склада по ID

// // Возвращает информацию о складе по его ID

// // Parameters
// // Try it out
// // No parameters

// // Request body

// // application/json
// // Example Value
// // Schema
// // {
// //   "warehouseId": "79BA8EdFddAeE114132C481B"
// // }
// // Responses
// // Code	Description	Links
// // 200
// // Информация о складе успешно получена

// // No links

//   @override
//   Widget build(BuildContext context) {
//     if (_productDetails.isEmpty) {
//       return const SizedBox.shrink();
//     }

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           'Детали продуктов',
//           style: Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
//         ),
//         const SizedBox(height: 8.0),
//         Text(
//           'Укажите детальную информацию для каждого продукта в контракте',
//           style: Fonts.bodySmall.copyWith(
//             color: Theme.of(context).colorScheme.onSurfaceVariant,
//           ),
//         ),
//         const SizedBox(height: 12.0),
//         ...List.generate(_productDetails.length, (index) {
//           final detail = _productDetails[index];
//           return CustomCard(
//             margin: const EdgeInsets.only(bottom: 12.0),
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     'Продукт ${index + 1} (ID: ${detail.productId})',
//                     style:
//                         Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
//                   ),
//                   const SizedBox(height: 12.0),

//                   // Первая строка: Цена и Количество
//                   Row(
//                     children: [
//                       Expanded(
//                         child: TextFormField(
//                           initialValue: detail.price?.toString() ?? '',
//                           decoration: const InputDecoration(
//                             labelText: 'Цена *',
//                             border: OutlineInputBorder(),
//                             suffixText: '₽',
//                           ),
//                           keyboardType:
//                               TextInputType.numberWithOptions(decimal: true),
//                           inputFormatters: [
//                             FilteringTextInputFormatter.allow(
//                                 RegExp(r'^\d*\.?\d*')),
//                           ],
//                           onChanged: (value) {
//                             final price = double.tryParse(value) ?? 0.0;
//                             _updateProductDetail(
//                               index,
//                               detail.copyWith(price: price),
//                             );
//                           },
//                         ),
//                       ),
//                       const SizedBox(width: 12.0),
//                       Expanded(
//                         child: TextFormField(
//                           initialValue: detail.quantity?.toString() ?? '',
//                           decoration: const InputDecoration(
//                             labelText: 'Количество *',
//                             border: OutlineInputBorder(),
//                           ),
//                           keyboardType:
//                               TextInputType.numberWithOptions(decimal: true),
//                           inputFormatters: [
//                             FilteringTextInputFormatter.allow(
//                                 RegExp(r'^\d*\.?\d*')),
//                           ],
//                           onChanged: (value) {
//                             final quantity = double.tryParse(value) ?? 1.0;
//                             _updateProductDetail(
//                               index,
//                               detail.copyWith(quantity: quantity),
//                             );
//                           },
//                         ),
//                       ),
//                     ],
//                   ),

//                   const SizedBox(height: 12.0),

//                   // Вторая строка: Единица измерения и Дата поставки
//                   Row(
//                     children: [
//                       // TODO: сделать dropdown из UnitType

//                       Expanded(
//                         child: TextFormField(
//                           initialValue: detail.unit ?? '',
//                           decoration: const InputDecoration(
//                             labelText: 'Единица измерения',
//                             border: OutlineInputBorder(),
//                             hintText: 'шт, кг, м, л...',
//                           ),
//                           onChanged: (value) {
//                             _updateProductDetail(
//                               index,
//                               detail.copyWith(
//                                   unit: value.isEmpty ? null : value),
//                             );
//                           },
//                         ),
//                       ),
//                       const SizedBox(width: 12.0),
//                       Expanded(
//                         child: TextFormField(
//                           controller: TextEditingController(
//                               text: detail.deliveryDate ?? ''),
//                           decoration: InputDecoration(
//                             labelText: 'Дата поставки',
//                             border: const OutlineInputBorder(),
//                             suffixIcon: IconButton(
//                               icon: const Icon(Icons.calendar_today),
//                               onPressed: () => _pickDate(context, index),
//                             ),
//                           ),
//                           readOnly: true,
//                           onTap: () => _pickDate(context, index),
//                         ),
//                       ),
//                     ],
//                   ),

//                   const SizedBox(height: 12.0),

//                   // Третья строка: Примечания
//                   TextFormField(
//                     initialValue: detail.notes ?? '',
//                     decoration: const InputDecoration(
//                       labelText: 'Примечания',
//                       border: OutlineInputBorder(),
//                       hintText: 'Дополнительная информация о продукте',
//                     ),
//                     maxLines: 2,
//                     onChanged: (value) {
//                       _updateProductDetail(
//                         index,
//                         detail.copyWith(notes: value.isEmpty ? null : value),
//                       );
//                     },
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }),
//       ],
//     );
//   }
// }
