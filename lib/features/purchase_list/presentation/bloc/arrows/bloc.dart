import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bloc.freezed.dart';
part 'bloc.g.dart';
part 'events.dart';
part 'state.dart';

class BlocArrows extends Bloc<BlocArrowsEvents, BlocArrowsState> {
  BlocArrows() : super(BlocArrowsState(cardArrows: {})) {
    on<SetArrows>((event, emit) {
      final updatedCardArrows =
          Map<String, List<PurchaseCardArrowModel>>.from(state.cardArrows);
      updatedCardArrows[event.cardId] = event.arrows;
      emit(state.copyWith(cardArrows: updatedCardArrows));
    });
  }
}
