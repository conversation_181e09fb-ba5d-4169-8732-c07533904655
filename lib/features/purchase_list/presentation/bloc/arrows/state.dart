part of 'bloc.dart';

@freezed
class BlocArrowsState with _$<PERSON>tate {
  const factory BlocArrowsState({
    @Default({}) Map<String, List<PurchaseCardArrowModel>> cardArrows,
  }) = _BlocArrowsState;

  factory BlocArrowsState.fromJson(Map<String, dynamic> json) =>
      _$BlocArrowsStateFromJson(json);
}

@freezed
class PurchaseCardArrowModel with _$PurchaseCardArrowModel {
  const factory PurchaseCardArrowModel({
    @Default(0.0) double y1,
    @Default(0.0) double y2,
    @Default(0xFF000000) int? color,
    @Default(0.0) double quantity,
  }) = _PurchaseCardArrowModel;

  factory PurchaseCardArrowModel.fromJson(Map<String, dynamic> json) =>
      _$PurchaseCardArrowModelFromJson(json);
}
