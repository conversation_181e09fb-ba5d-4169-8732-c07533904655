// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SetArrows {
  String get cardId => throw _privateConstructorUsedError;
  List<PurchaseCardArrowModel> get arrows => throw _privateConstructorUsedError;

  /// Create a copy of SetArrows
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SetArrowsCopyWith<SetArrows> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SetArrowsCopyWith<$Res> {
  factory $SetArrowsCopyWith(SetArrows value, $Res Function(SetArrows) then) =
      _$SetArrowsCopyWithImpl<$Res, SetArrows>;
  @useResult
  $Res call({String cardId, List<PurchaseCardArrowModel> arrows});
}

/// @nodoc
class _$SetArrowsCopyWithImpl<$Res, $Val extends SetArrows>
    implements $SetArrowsCopyWith<$Res> {
  _$SetArrowsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SetArrows
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardId = null,
    Object? arrows = null,
  }) {
    return _then(_value.copyWith(
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      arrows: null == arrows
          ? _value.arrows
          : arrows // ignore: cast_nullable_to_non_nullable
              as List<PurchaseCardArrowModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SetArrowsImplCopyWith<$Res>
    implements $SetArrowsCopyWith<$Res> {
  factory _$$SetArrowsImplCopyWith(
          _$SetArrowsImpl value, $Res Function(_$SetArrowsImpl) then) =
      __$$SetArrowsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String cardId, List<PurchaseCardArrowModel> arrows});
}

/// @nodoc
class __$$SetArrowsImplCopyWithImpl<$Res>
    extends _$SetArrowsCopyWithImpl<$Res, _$SetArrowsImpl>
    implements _$$SetArrowsImplCopyWith<$Res> {
  __$$SetArrowsImplCopyWithImpl(
      _$SetArrowsImpl _value, $Res Function(_$SetArrowsImpl) _then)
      : super(_value, _then);

  /// Create a copy of SetArrows
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardId = null,
    Object? arrows = null,
  }) {
    return _then(_$SetArrowsImpl(
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      arrows: null == arrows
          ? _value._arrows
          : arrows // ignore: cast_nullable_to_non_nullable
              as List<PurchaseCardArrowModel>,
    ));
  }
}

/// @nodoc

class _$SetArrowsImpl implements _SetArrows {
  const _$SetArrowsImpl(
      {required this.cardId,
      required final List<PurchaseCardArrowModel> arrows})
      : _arrows = arrows;

  @override
  final String cardId;
  final List<PurchaseCardArrowModel> _arrows;
  @override
  List<PurchaseCardArrowModel> get arrows {
    if (_arrows is EqualUnmodifiableListView) return _arrows;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_arrows);
  }

  @override
  String toString() {
    return 'SetArrows(cardId: $cardId, arrows: $arrows)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetArrowsImpl &&
            (identical(other.cardId, cardId) || other.cardId == cardId) &&
            const DeepCollectionEquality().equals(other._arrows, _arrows));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, cardId, const DeepCollectionEquality().hash(_arrows));

  /// Create a copy of SetArrows
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SetArrowsImplCopyWith<_$SetArrowsImpl> get copyWith =>
      __$$SetArrowsImplCopyWithImpl<_$SetArrowsImpl>(this, _$identity);
}

abstract class _SetArrows implements SetArrows {
  const factory _SetArrows(
      {required final String cardId,
      required final List<PurchaseCardArrowModel> arrows}) = _$SetArrowsImpl;

  @override
  String get cardId;
  @override
  List<PurchaseCardArrowModel> get arrows;

  /// Create a copy of SetArrows
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SetArrowsImplCopyWith<_$SetArrowsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BlocArrowsState _$BlocArrowsStateFromJson(Map<String, dynamic> json) {
  return _BlocArrowsState.fromJson(json);
}

/// @nodoc
mixin _$BlocArrowsState {
  Map<String, List<PurchaseCardArrowModel>> get cardArrows =>
      throw _privateConstructorUsedError;

  /// Serializes this BlocArrowsState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BlocArrowsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BlocArrowsStateCopyWith<BlocArrowsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocArrowsStateCopyWith<$Res> {
  factory $BlocArrowsStateCopyWith(
          BlocArrowsState value, $Res Function(BlocArrowsState) then) =
      _$BlocArrowsStateCopyWithImpl<$Res, BlocArrowsState>;
  @useResult
  $Res call({Map<String, List<PurchaseCardArrowModel>> cardArrows});
}

/// @nodoc
class _$BlocArrowsStateCopyWithImpl<$Res, $Val extends BlocArrowsState>
    implements $BlocArrowsStateCopyWith<$Res> {
  _$BlocArrowsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocArrowsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardArrows = null,
  }) {
    return _then(_value.copyWith(
      cardArrows: null == cardArrows
          ? _value.cardArrows
          : cardArrows // ignore: cast_nullable_to_non_nullable
              as Map<String, List<PurchaseCardArrowModel>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlocArrowsStateImplCopyWith<$Res>
    implements $BlocArrowsStateCopyWith<$Res> {
  factory _$$BlocArrowsStateImplCopyWith(_$BlocArrowsStateImpl value,
          $Res Function(_$BlocArrowsStateImpl) then) =
      __$$BlocArrowsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<String, List<PurchaseCardArrowModel>> cardArrows});
}

/// @nodoc
class __$$BlocArrowsStateImplCopyWithImpl<$Res>
    extends _$BlocArrowsStateCopyWithImpl<$Res, _$BlocArrowsStateImpl>
    implements _$$BlocArrowsStateImplCopyWith<$Res> {
  __$$BlocArrowsStateImplCopyWithImpl(
      _$BlocArrowsStateImpl _value, $Res Function(_$BlocArrowsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocArrowsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardArrows = null,
  }) {
    return _then(_$BlocArrowsStateImpl(
      cardArrows: null == cardArrows
          ? _value._cardArrows
          : cardArrows // ignore: cast_nullable_to_non_nullable
              as Map<String, List<PurchaseCardArrowModel>>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BlocArrowsStateImpl implements _BlocArrowsState {
  const _$BlocArrowsStateImpl(
      {final Map<String, List<PurchaseCardArrowModel>> cardArrows = const {}})
      : _cardArrows = cardArrows;

  factory _$BlocArrowsStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$BlocArrowsStateImplFromJson(json);

  final Map<String, List<PurchaseCardArrowModel>> _cardArrows;
  @override
  @JsonKey()
  Map<String, List<PurchaseCardArrowModel>> get cardArrows {
    if (_cardArrows is EqualUnmodifiableMapView) return _cardArrows;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_cardArrows);
  }

  @override
  String toString() {
    return 'BlocArrowsState(cardArrows: $cardArrows)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlocArrowsStateImpl &&
            const DeepCollectionEquality()
                .equals(other._cardArrows, _cardArrows));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_cardArrows));

  /// Create a copy of BlocArrowsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BlocArrowsStateImplCopyWith<_$BlocArrowsStateImpl> get copyWith =>
      __$$BlocArrowsStateImplCopyWithImpl<_$BlocArrowsStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BlocArrowsStateImplToJson(
      this,
    );
  }
}

abstract class _BlocArrowsState implements BlocArrowsState {
  const factory _BlocArrowsState(
          {final Map<String, List<PurchaseCardArrowModel>> cardArrows}) =
      _$BlocArrowsStateImpl;

  factory _BlocArrowsState.fromJson(Map<String, dynamic> json) =
      _$BlocArrowsStateImpl.fromJson;

  @override
  Map<String, List<PurchaseCardArrowModel>> get cardArrows;

  /// Create a copy of BlocArrowsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BlocArrowsStateImplCopyWith<_$BlocArrowsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PurchaseCardArrowModel _$PurchaseCardArrowModelFromJson(
    Map<String, dynamic> json) {
  return _PurchaseCardArrowModel.fromJson(json);
}

/// @nodoc
mixin _$PurchaseCardArrowModel {
  double get y1 => throw _privateConstructorUsedError;
  double get y2 => throw _privateConstructorUsedError;
  int? get color => throw _privateConstructorUsedError;
  double get quantity => throw _privateConstructorUsedError;

  /// Serializes this PurchaseCardArrowModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PurchaseCardArrowModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PurchaseCardArrowModelCopyWith<PurchaseCardArrowModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PurchaseCardArrowModelCopyWith<$Res> {
  factory $PurchaseCardArrowModelCopyWith(PurchaseCardArrowModel value,
          $Res Function(PurchaseCardArrowModel) then) =
      _$PurchaseCardArrowModelCopyWithImpl<$Res, PurchaseCardArrowModel>;
  @useResult
  $Res call({double y1, double y2, int? color, double quantity});
}

/// @nodoc
class _$PurchaseCardArrowModelCopyWithImpl<$Res,
        $Val extends PurchaseCardArrowModel>
    implements $PurchaseCardArrowModelCopyWith<$Res> {
  _$PurchaseCardArrowModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PurchaseCardArrowModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? y1 = null,
    Object? y2 = null,
    Object? color = freezed,
    Object? quantity = null,
  }) {
    return _then(_value.copyWith(
      y1: null == y1
          ? _value.y1
          : y1 // ignore: cast_nullable_to_non_nullable
              as double,
      y2: null == y2
          ? _value.y2
          : y2 // ignore: cast_nullable_to_non_nullable
              as double,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PurchaseCardArrowModelImplCopyWith<$Res>
    implements $PurchaseCardArrowModelCopyWith<$Res> {
  factory _$$PurchaseCardArrowModelImplCopyWith(
          _$PurchaseCardArrowModelImpl value,
          $Res Function(_$PurchaseCardArrowModelImpl) then) =
      __$$PurchaseCardArrowModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double y1, double y2, int? color, double quantity});
}

/// @nodoc
class __$$PurchaseCardArrowModelImplCopyWithImpl<$Res>
    extends _$PurchaseCardArrowModelCopyWithImpl<$Res,
        _$PurchaseCardArrowModelImpl>
    implements _$$PurchaseCardArrowModelImplCopyWith<$Res> {
  __$$PurchaseCardArrowModelImplCopyWithImpl(
      _$PurchaseCardArrowModelImpl _value,
      $Res Function(_$PurchaseCardArrowModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of PurchaseCardArrowModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? y1 = null,
    Object? y2 = null,
    Object? color = freezed,
    Object? quantity = null,
  }) {
    return _then(_$PurchaseCardArrowModelImpl(
      y1: null == y1
          ? _value.y1
          : y1 // ignore: cast_nullable_to_non_nullable
              as double,
      y2: null == y2
          ? _value.y2
          : y2 // ignore: cast_nullable_to_non_nullable
              as double,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as int?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PurchaseCardArrowModelImpl implements _PurchaseCardArrowModel {
  const _$PurchaseCardArrowModelImpl(
      {this.y1 = 0.0,
      this.y2 = 0.0,
      this.color = 0xFF000000,
      this.quantity = 0.0});

  factory _$PurchaseCardArrowModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$PurchaseCardArrowModelImplFromJson(json);

  @override
  @JsonKey()
  final double y1;
  @override
  @JsonKey()
  final double y2;
  @override
  @JsonKey()
  final int? color;
  @override
  @JsonKey()
  final double quantity;

  @override
  String toString() {
    return 'PurchaseCardArrowModel(y1: $y1, y2: $y2, color: $color, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseCardArrowModelImpl &&
            (identical(other.y1, y1) || other.y1 == y1) &&
            (identical(other.y2, y2) || other.y2 == y2) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, y1, y2, color, quantity);

  /// Create a copy of PurchaseCardArrowModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseCardArrowModelImplCopyWith<_$PurchaseCardArrowModelImpl>
      get copyWith => __$$PurchaseCardArrowModelImplCopyWithImpl<
          _$PurchaseCardArrowModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PurchaseCardArrowModelImplToJson(
      this,
    );
  }
}

abstract class _PurchaseCardArrowModel implements PurchaseCardArrowModel {
  const factory _PurchaseCardArrowModel(
      {final double y1,
      final double y2,
      final int? color,
      final double quantity}) = _$PurchaseCardArrowModelImpl;

  factory _PurchaseCardArrowModel.fromJson(Map<String, dynamic> json) =
      _$PurchaseCardArrowModelImpl.fromJson;

  @override
  double get y1;
  @override
  double get y2;
  @override
  int? get color;
  @override
  double get quantity;

  /// Create a copy of PurchaseCardArrowModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PurchaseCardArrowModelImplCopyWith<_$PurchaseCardArrowModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
