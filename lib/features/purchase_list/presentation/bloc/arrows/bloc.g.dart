// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BlocArrowsStateImpl _$$BlocArrowsStateImplFromJson(
        Map<String, dynamic> json) =>
    _$BlocArrowsStateImpl(
      cardArrows: (json['cardArrows'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                k,
                (e as List<dynamic>)
                    .map((e) => PurchaseCardArrowModel.fromJson(
                        e as Map<String, dynamic>))
                    .toList()),
          ) ??
          const {},
    );

Map<String, dynamic> _$$BlocArrowsStateImplToJson(
        _$<PERSON>tateImpl instance) =>
    <String, dynamic>{
      'cardArrows': instance.cardArrows,
    };

_$PurchaseCardArrowModelImpl _$$PurchaseCardArrowModelImplFromJson(
        Map<String, dynamic> json) =>
    _$PurchaseCardArrowModelImpl(
      y1: (json['y1'] as num?)?.toDouble() ?? 0.0,
      y2: (json['y2'] as num?)?.toDouble() ?? 0.0,
      color: (json['color'] as num?)?.toInt() ?? 0xFF000000,
      quantity: (json['quantity'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$PurchaseCardArrowModelImplToJson(
        _$PurchaseCardArrowModelImpl instance) =>
    <String, dynamic>{
      'y1': instance.y1,
      'y2': instance.y2,
      'color': instance.color,
      'quantity': instance.quantity,
    };
