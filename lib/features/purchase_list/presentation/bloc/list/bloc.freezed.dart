// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BlocPurchaseListState {
  bool get selecting => throw _privateConstructorUsedError;
  Map<String, ProvisionItemModel> get selectedProducts =>
      throw _privateConstructorUsedError;
  ProvisionsListModel get productItems => throw _privateConstructorUsedError;
  ProvisionsFilter? get selectedFilter => throw _privateConstructorUsedError;
  List<ProvisionProductModel> get products =>
      throw _privateConstructorUsedError;
  Map<String, bool> get columnVisibility => throw _privateConstructorUsedError;
  SearchFiltersModel? get searchFilters => throw _privateConstructorUsedError;
  bool get isFiltersOpen => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  int get totalItems =>
      throw _privateConstructorUsedError; // New field for column options
  ProvisionsColumnOptionsOutput get options =>
      throw _privateConstructorUsedError;
  String? get error =>
      throw _privateConstructorUsedError; // Fields for task creation with provision items
// @Default([]) List<ProvisionItemModel> selectedProvisionItemsForTask,
  List<ProvisionProductModel> get selectedProvisionProductsForTask =>
      throw _privateConstructorUsedError; // Fields for task progress management
  List<TaskProgressModel> get selectedProgressTasks =>
      throw _privateConstructorUsedError;

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BlocPurchaseListStateCopyWith<BlocPurchaseListState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocPurchaseListStateCopyWith<$Res> {
  factory $BlocPurchaseListStateCopyWith(BlocPurchaseListState value,
          $Res Function(BlocPurchaseListState) then) =
      _$BlocPurchaseListStateCopyWithImpl<$Res, BlocPurchaseListState>;
  @useResult
  $Res call(
      {bool selecting,
      Map<String, ProvisionItemModel> selectedProducts,
      ProvisionsListModel productItems,
      ProvisionsFilter? selectedFilter,
      List<ProvisionProductModel> products,
      Map<String, bool> columnVisibility,
      SearchFiltersModel? searchFilters,
      bool isFiltersOpen,
      bool isLoading,
      int totalItems,
      ProvisionsColumnOptionsOutput options,
      String? error,
      List<ProvisionProductModel> selectedProvisionProductsForTask,
      List<TaskProgressModel> selectedProgressTasks});

  $ProvisionsListModelCopyWith<$Res> get productItems;
  $SearchFiltersModelCopyWith<$Res>? get searchFilters;
  $ProvisionsColumnOptionsOutputCopyWith<$Res> get options;
}

/// @nodoc
class _$BlocPurchaseListStateCopyWithImpl<$Res,
        $Val extends BlocPurchaseListState>
    implements $BlocPurchaseListStateCopyWith<$Res> {
  _$BlocPurchaseListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selecting = null,
    Object? selectedProducts = null,
    Object? productItems = null,
    Object? selectedFilter = freezed,
    Object? products = null,
    Object? columnVisibility = null,
    Object? searchFilters = freezed,
    Object? isFiltersOpen = null,
    Object? isLoading = null,
    Object? totalItems = null,
    Object? options = null,
    Object? error = freezed,
    Object? selectedProvisionProductsForTask = null,
    Object? selectedProgressTasks = null,
  }) {
    return _then(_value.copyWith(
      selecting: null == selecting
          ? _value.selecting
          : selecting // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedProducts: null == selectedProducts
          ? _value.selectedProducts
          : selectedProducts // ignore: cast_nullable_to_non_nullable
              as Map<String, ProvisionItemModel>,
      productItems: null == productItems
          ? _value.productItems
          : productItems // ignore: cast_nullable_to_non_nullable
              as ProvisionsListModel,
      selectedFilter: freezed == selectedFilter
          ? _value.selectedFilter
          : selectedFilter // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      products: null == products
          ? _value.products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProvisionProductModel>,
      columnVisibility: null == columnVisibility
          ? _value.columnVisibility
          : columnVisibility // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      searchFilters: freezed == searchFilters
          ? _value.searchFilters
          : searchFilters // ignore: cast_nullable_to_non_nullable
              as SearchFiltersModel?,
      isFiltersOpen: null == isFiltersOpen
          ? _value.isFiltersOpen
          : isFiltersOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as ProvisionsColumnOptionsOutput,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedProvisionProductsForTask: null == selectedProvisionProductsForTask
          ? _value.selectedProvisionProductsForTask
          : selectedProvisionProductsForTask // ignore: cast_nullable_to_non_nullable
              as List<ProvisionProductModel>,
      selectedProgressTasks: null == selectedProgressTasks
          ? _value.selectedProgressTasks
          : selectedProgressTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskProgressModel>,
    ) as $Val);
  }

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProvisionsListModelCopyWith<$Res> get productItems {
    return $ProvisionsListModelCopyWith<$Res>(_value.productItems, (value) {
      return _then(_value.copyWith(productItems: value) as $Val);
    });
  }

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SearchFiltersModelCopyWith<$Res>? get searchFilters {
    if (_value.searchFilters == null) {
      return null;
    }

    return $SearchFiltersModelCopyWith<$Res>(_value.searchFilters!, (value) {
      return _then(_value.copyWith(searchFilters: value) as $Val);
    });
  }

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProvisionsColumnOptionsOutputCopyWith<$Res> get options {
    return $ProvisionsColumnOptionsOutputCopyWith<$Res>(_value.options,
        (value) {
      return _then(_value.copyWith(options: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BlocPurchaseListStateImplCopyWith<$Res>
    implements $BlocPurchaseListStateCopyWith<$Res> {
  factory _$$BlocPurchaseListStateImplCopyWith(
          _$BlocPurchaseListStateImpl value,
          $Res Function(_$BlocPurchaseListStateImpl) then) =
      __$$BlocPurchaseListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool selecting,
      Map<String, ProvisionItemModel> selectedProducts,
      ProvisionsListModel productItems,
      ProvisionsFilter? selectedFilter,
      List<ProvisionProductModel> products,
      Map<String, bool> columnVisibility,
      SearchFiltersModel? searchFilters,
      bool isFiltersOpen,
      bool isLoading,
      int totalItems,
      ProvisionsColumnOptionsOutput options,
      String? error,
      List<ProvisionProductModel> selectedProvisionProductsForTask,
      List<TaskProgressModel> selectedProgressTasks});

  @override
  $ProvisionsListModelCopyWith<$Res> get productItems;
  @override
  $SearchFiltersModelCopyWith<$Res>? get searchFilters;
  @override
  $ProvisionsColumnOptionsOutputCopyWith<$Res> get options;
}

/// @nodoc
class __$$BlocPurchaseListStateImplCopyWithImpl<$Res>
    extends _$BlocPurchaseListStateCopyWithImpl<$Res,
        _$BlocPurchaseListStateImpl>
    implements _$$BlocPurchaseListStateImplCopyWith<$Res> {
  __$$BlocPurchaseListStateImplCopyWithImpl(_$BlocPurchaseListStateImpl _value,
      $Res Function(_$BlocPurchaseListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selecting = null,
    Object? selectedProducts = null,
    Object? productItems = null,
    Object? selectedFilter = freezed,
    Object? products = null,
    Object? columnVisibility = null,
    Object? searchFilters = freezed,
    Object? isFiltersOpen = null,
    Object? isLoading = null,
    Object? totalItems = null,
    Object? options = null,
    Object? error = freezed,
    Object? selectedProvisionProductsForTask = null,
    Object? selectedProgressTasks = null,
  }) {
    return _then(_$BlocPurchaseListStateImpl(
      selecting: null == selecting
          ? _value.selecting
          : selecting // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedProducts: null == selectedProducts
          ? _value._selectedProducts
          : selectedProducts // ignore: cast_nullable_to_non_nullable
              as Map<String, ProvisionItemModel>,
      productItems: null == productItems
          ? _value.productItems
          : productItems // ignore: cast_nullable_to_non_nullable
              as ProvisionsListModel,
      selectedFilter: freezed == selectedFilter
          ? _value.selectedFilter
          : selectedFilter // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      products: null == products
          ? _value._products
          : products // ignore: cast_nullable_to_non_nullable
              as List<ProvisionProductModel>,
      columnVisibility: null == columnVisibility
          ? _value._columnVisibility
          : columnVisibility // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      searchFilters: freezed == searchFilters
          ? _value.searchFilters
          : searchFilters // ignore: cast_nullable_to_non_nullable
              as SearchFiltersModel?,
      isFiltersOpen: null == isFiltersOpen
          ? _value.isFiltersOpen
          : isFiltersOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      totalItems: null == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as ProvisionsColumnOptionsOutput,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedProvisionProductsForTask: null == selectedProvisionProductsForTask
          ? _value._selectedProvisionProductsForTask
          : selectedProvisionProductsForTask // ignore: cast_nullable_to_non_nullable
              as List<ProvisionProductModel>,
      selectedProgressTasks: null == selectedProgressTasks
          ? _value._selectedProgressTasks
          : selectedProgressTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskProgressModel>,
    ));
  }
}

/// @nodoc

class _$BlocPurchaseListStateImpl extends _BlocPurchaseListState {
  const _$BlocPurchaseListStateImpl(
      {this.selecting = false,
      final Map<String, ProvisionItemModel> selectedProducts = const {},
      this.productItems = const ProvisionsListModel(),
      this.selectedFilter = null,
      final List<ProvisionProductModel> products = const [],
      final Map<String, bool> columnVisibility = const {},
      this.searchFilters,
      this.isFiltersOpen = false,
      this.isLoading = false,
      this.totalItems = 0,
      this.options = const ProvisionsColumnOptionsOutput(),
      this.error = null,
      final List<ProvisionProductModel> selectedProvisionProductsForTask =
          const [],
      final List<TaskProgressModel> selectedProgressTasks = const []})
      : _selectedProducts = selectedProducts,
        _products = products,
        _columnVisibility = columnVisibility,
        _selectedProvisionProductsForTask = selectedProvisionProductsForTask,
        _selectedProgressTasks = selectedProgressTasks,
        super._();

  @override
  @JsonKey()
  final bool selecting;
  final Map<String, ProvisionItemModel> _selectedProducts;
  @override
  @JsonKey()
  Map<String, ProvisionItemModel> get selectedProducts {
    if (_selectedProducts is EqualUnmodifiableMapView) return _selectedProducts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_selectedProducts);
  }

  @override
  @JsonKey()
  final ProvisionsListModel productItems;
  @override
  @JsonKey()
  final ProvisionsFilter? selectedFilter;
  final List<ProvisionProductModel> _products;
  @override
  @JsonKey()
  List<ProvisionProductModel> get products {
    if (_products is EqualUnmodifiableListView) return _products;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_products);
  }

  final Map<String, bool> _columnVisibility;
  @override
  @JsonKey()
  Map<String, bool> get columnVisibility {
    if (_columnVisibility is EqualUnmodifiableMapView) return _columnVisibility;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_columnVisibility);
  }

  @override
  final SearchFiltersModel? searchFilters;
  @override
  @JsonKey()
  final bool isFiltersOpen;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final int totalItems;
// New field for column options
  @override
  @JsonKey()
  final ProvisionsColumnOptionsOutput options;
  @override
  @JsonKey()
  final String? error;
// Fields for task creation with provision items
// @Default([]) List<ProvisionItemModel> selectedProvisionItemsForTask,
  final List<ProvisionProductModel> _selectedProvisionProductsForTask;
// Fields for task creation with provision items
// @Default([]) List<ProvisionItemModel> selectedProvisionItemsForTask,
  @override
  @JsonKey()
  List<ProvisionProductModel> get selectedProvisionProductsForTask {
    if (_selectedProvisionProductsForTask is EqualUnmodifiableListView)
      return _selectedProvisionProductsForTask;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedProvisionProductsForTask);
  }

// Fields for task progress management
  final List<TaskProgressModel> _selectedProgressTasks;
// Fields for task progress management
  @override
  @JsonKey()
  List<TaskProgressModel> get selectedProgressTasks {
    if (_selectedProgressTasks is EqualUnmodifiableListView)
      return _selectedProgressTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedProgressTasks);
  }

  @override
  String toString() {
    return 'BlocPurchaseListState(selecting: $selecting, selectedProducts: $selectedProducts, productItems: $productItems, selectedFilter: $selectedFilter, products: $products, columnVisibility: $columnVisibility, searchFilters: $searchFilters, isFiltersOpen: $isFiltersOpen, isLoading: $isLoading, totalItems: $totalItems, options: $options, error: $error, selectedProvisionProductsForTask: $selectedProvisionProductsForTask, selectedProgressTasks: $selectedProgressTasks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlocPurchaseListStateImpl &&
            (identical(other.selecting, selecting) ||
                other.selecting == selecting) &&
            const DeepCollectionEquality()
                .equals(other._selectedProducts, _selectedProducts) &&
            (identical(other.productItems, productItems) ||
                other.productItems == productItems) &&
            (identical(other.selectedFilter, selectedFilter) ||
                other.selectedFilter == selectedFilter) &&
            const DeepCollectionEquality().equals(other._products, _products) &&
            const DeepCollectionEquality()
                .equals(other._columnVisibility, _columnVisibility) &&
            (identical(other.searchFilters, searchFilters) ||
                other.searchFilters == searchFilters) &&
            (identical(other.isFiltersOpen, isFiltersOpen) ||
                other.isFiltersOpen == isFiltersOpen) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems) &&
            (identical(other.options, options) || other.options == options) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality().equals(
                other._selectedProvisionProductsForTask,
                _selectedProvisionProductsForTask) &&
            const DeepCollectionEquality()
                .equals(other._selectedProgressTasks, _selectedProgressTasks));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selecting,
      const DeepCollectionEquality().hash(_selectedProducts),
      productItems,
      selectedFilter,
      const DeepCollectionEquality().hash(_products),
      const DeepCollectionEquality().hash(_columnVisibility),
      searchFilters,
      isFiltersOpen,
      isLoading,
      totalItems,
      options,
      error,
      const DeepCollectionEquality().hash(_selectedProvisionProductsForTask),
      const DeepCollectionEquality().hash(_selectedProgressTasks));

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BlocPurchaseListStateImplCopyWith<_$BlocPurchaseListStateImpl>
      get copyWith => __$$BlocPurchaseListStateImplCopyWithImpl<
          _$BlocPurchaseListStateImpl>(this, _$identity);
}

abstract class _BlocPurchaseListState extends BlocPurchaseListState {
  const factory _BlocPurchaseListState(
          {final bool selecting,
          final Map<String, ProvisionItemModel> selectedProducts,
          final ProvisionsListModel productItems,
          final ProvisionsFilter? selectedFilter,
          final List<ProvisionProductModel> products,
          final Map<String, bool> columnVisibility,
          final SearchFiltersModel? searchFilters,
          final bool isFiltersOpen,
          final bool isLoading,
          final int totalItems,
          final ProvisionsColumnOptionsOutput options,
          final String? error,
          final List<ProvisionProductModel> selectedProvisionProductsForTask,
          final List<TaskProgressModel> selectedProgressTasks}) =
      _$BlocPurchaseListStateImpl;
  const _BlocPurchaseListState._() : super._();

  @override
  bool get selecting;
  @override
  Map<String, ProvisionItemModel> get selectedProducts;
  @override
  ProvisionsListModel get productItems;
  @override
  ProvisionsFilter? get selectedFilter;
  @override
  List<ProvisionProductModel> get products;
  @override
  Map<String, bool> get columnVisibility;
  @override
  SearchFiltersModel? get searchFilters;
  @override
  bool get isFiltersOpen;
  @override
  bool get isLoading;
  @override
  int get totalItems; // New field for column options
  @override
  ProvisionsColumnOptionsOutput get options;
  @override
  String? get error; // Fields for task creation with provision items
// @Default([]) List<ProvisionItemModel> selectedProvisionItemsForTask,
  @override
  List<ProvisionProductModel>
      get selectedProvisionProductsForTask; // Fields for task progress management
  @override
  List<TaskProgressModel> get selectedProgressTasks;

  /// Create a copy of BlocPurchaseListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BlocPurchaseListStateImplCopyWith<_$BlocPurchaseListStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
