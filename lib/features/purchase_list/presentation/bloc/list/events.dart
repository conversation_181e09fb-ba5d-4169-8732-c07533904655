part of 'bloc.dart';

sealed class BlocPurchaseListEvents {}

final class SetSelectedProducts extends BlocPurchaseListEvents {
  final Map<String, ProvisionItemModel> selectedProducts;
  SetSelectedProducts(this.selectedProducts);
}

final class SetSelecting extends BlocPurchaseListEvents {
  final bool selecting;
  SetSelecting(this.selecting);
}

final class ToggleSelecting extends BlocPurchaseListEvents {
  ToggleSelecting();
}

final class SelectProvisionProduct extends BlocPurchaseListEvents {
  final ProvisionItemModel product;
  SelectProvisionProduct(this.product);
}

// Unified event for toggling provision product selection
final class ToggleProvisionProductSelection extends BlocPurchaseListEvents {
  final ProvisionProductModel product;
  ToggleProvisionProductSelection(this.product);
}

// Clear all selections
final class ClearProvisionProductSelections extends BlocPurchaseListEvents {
  ClearProvisionProductSelections();
}

// Events for task progress management
final class ToggleSelectedProgressTaskInPurchaseList
    extends BlocPurchaseListEvents {
  final TaskProgressModel item;
  ToggleSelectedProgressTaskInPurchaseList(this.item);
}

final class ClearProgressTasksSelectionsInPurchaseList
    extends BlocPurchaseListEvents {
  ClearProgressTasksSelectionsInPurchaseList();
}

final class SetProvisionProductItems extends BlocPurchaseListEvents {
  final ProvisionsListModel items;
  SetProvisionProductItems(this.items);
}

final class SetProvisionsFilter extends BlocPurchaseListEvents {
  final ProvisionsFilter? filter;
  SetProvisionsFilter(this.filter);
}

final class FetchPurchases extends BlocPurchaseListEvents {
  final String projectId;
  FetchPurchases(this.projectId);
}

final class SetProvisionProducts extends BlocPurchaseListEvents {
  final List<ProvisionProductModel> products;
  SetProvisionProducts(this.products);
}

final class UpdateColumnVisibility extends BlocPurchaseListEvents {
  final Map<String, bool> columnVisibility;
  UpdateColumnVisibility(this.columnVisibility);
}

final class ApplyFilters extends BlocPurchaseListEvents {
  final SearchFiltersModel filters;
  ApplyFilters(this.filters);
}

final class ClearFilters extends BlocPurchaseListEvents {}

final class ToggleFiltersPanel extends BlocPurchaseListEvents {}

final class LoadDataWithFilters extends BlocPurchaseListEvents {
  final int? offset;
  final int? limit;
  LoadDataWithFilters({this.offset, this.limit});
}

// New event for removing a specific filter
final class RemoveFilter extends BlocPurchaseListEvents {
  final String filterKey;
  RemoveFilter(this.filterKey);
}

final class FetchColumnOptions extends BlocPurchaseListEvents {
  final ProvisionsColumnOptionsInput input;
  FetchColumnOptions(this.input);
}
