import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';

List<ProvisionMaterialModel> provisionGroupItems({
  required List<ProvisionItemModel> items,
  ProvisionsFilter filter = ProvisionsFilter.materials,
}) {
  final List<ProvisionMaterialModel> materials = [];
  final Set<String> processedGroupIds = {};

  final bool isCooperation = filter == ProvisionsFilter.cooperation;

  for (var item in items) {
    // Пропускаем, если группа уже обработана
    if (processedGroupIds.contains(item.materialGroupId) ||
        item.materialGroupId == null) {
      continue;
    }

    // Добавляем groupId в обработанные
    processedGroupIds.add(item.materialGroupId!);

    // Продукты в материальной карточке
    final List<ProvisionItemModel> groupedItems = [];
    double totalQuantity = 0.0;
    double quantity = 0.0;
    double totalPrice = 0.0;

    // Получаем сгруппированные элементы
    for (var otherItem in items) {
      if (otherItem.materialGroupId == item.materialGroupId) {
        groupedItems.add(otherItem);
      }
    }

    // Вычисляем итоговые значения
    for (final groupedItem in groupedItems) {
      totalQuantity += isCooperation
          ? groupedItem.cooperationQuantity ?? 0.0
          : groupedItem.totalQuantity ?? 0.0;
      quantity += groupedItem.deliveryQuantity ?? 0.0;
      if (groupedItem.paymentDetails == null) continue;
      for (final payment in groupedItem.paymentDetails!) {
        totalPrice += payment.amount ?? 0.0;
      }
    }

    // Генерируем модель материала
    materials.add(ProvisionMaterialModel(
      materialName: item.material?.name,
      unitType: item.material?.baseUnit,
      materialId: item.material?.id,
      materialRequirements: item.product?.parameters?.materialRequirements,
      quantity: quantity,
      totalQuantity: totalQuantity,
      totalPrice: totalPrice,
      products: groupedItems,
      // deliveries:
      // double? storageQuantity,
      // List<String>? operations,
      // List<ProvisionDeliveryModel>? deliveries,
    ));
  }

  return materials;
}
