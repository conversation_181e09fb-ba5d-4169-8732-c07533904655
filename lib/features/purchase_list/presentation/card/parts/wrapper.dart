import 'package:flutter/material.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';

class PurchaseCardWrapper extends StatelessWidget {
  const PurchaseCardWrapper({
    super.key,
    this.onTap,
    this.onSecondaryTapDown,
    this.isListItem = false,
    required this.child,
    this.isExpanded = false,
    this.isSmall = false,
    this.padding,
    this.isLoading = false,
  });

  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final bool isListItem;
  final Widget child;
  final bool isExpanded;
  final bool isSmall;
  final EdgeInsets? padding;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    // void onSecondaryTap(TapDownDetails details) {
    //   CustomDropdownMenu.instance.show(
    //     context: context,
    //     items: [
    //       CustomDropdownMenuItem(
    //         onTap: () {
    //           context.read<BlocPurchaseList>().add(ToggleSelecting());
    //         },
    //         icon: Assets.icons.check,
    //         text: 'Выбрать',
    //       ),
    //     ],
    //     position: details.globalPosition,
    //   );
    // }

    final content = AnimatedSize(
      alignment: Alignment.topCenter,
      duration: Duration(milliseconds: 200),
      curve: Curves.decelerate,
      child: SizedBox(
        height: isExpanded || isSmall ? null : 80.0,
        child: ClipRRect(child: child),
      ),
    );

    if (isListItem) {
      return InkWell(
        onTap: onTap,
        onSecondaryTapDown: onSecondaryTapDown,
        splashFactory: InkSparkle.splashFactory,
        child: Ink(
          padding: padding ?? EdgeInsets.symmetric(horizontal: 12.0),
          child: content,
        ),
      );
    }
    return CustomCard(
      onTap: onTap,
      onSecondaryTapDown: onSecondaryTapDown,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 12.0),
      isLoading: isLoading,
      child: content,
    );
  }
}
