import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class PurchaseCardQuantity extends StatelessWidget {
  const PurchaseCardQuantity({
    super.key,
    this.quantity = 0.0,
    this.totalQuantity,
    this.unitType = UnitType.kg,
  });

  final double quantity;
  final double? totalQuantity;
  final UnitType unitType;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Row(
      children: [
        Text(
          '$quantity${totalQuantity != null ? ' / ' : ''}',
          style: Fonts.bodySmall.merge(
            TextStyle(
              height: 1.2,
              color: isDarkTheme
                  ? AppColors.darkDescription
                  : AppColors.lightDescription,
            ),
          ),
        ),
        if (totalQuantity != null)
          Text(
            '$totalQuantity',
            style: Fonts.bodySmall.merge(
              TextStyle(
                height: 1.2,
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
              ),
            ),
          ),
        Text(
          ' ${unitType.getName()}',
          style: Fonts.bodySmall.merge(
            TextStyle(
              height: 1.2,
              color: isDarkTheme
                  ? AppColors.darkDescription
                  : AppColors.lightDescription,
            ),
          ),
        ),
      ],
    );
  }
}
