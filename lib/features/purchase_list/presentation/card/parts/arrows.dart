import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/arrows/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/paths/unification.dart';
import 'package:sphere/shared/styles/colors.dart';

class PurchaseCardArrows extends StatelessWidget {
  final String cardId;

  const PurchaseCardArrows({
    super.key,
    required this.cardId,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return BlocBuilder<BlocArrows, BlocArrowsState>(
      builder: (context, state) {
        final arrows = state.cardArrows[cardId] ?? [];
        // print(arrows);

        return Container(
          width: double.infinity,
          // clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            border: Border.all(
              color: isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
            ),
            borderRadius: BorderRadius.circular(20.0),
            color: isDarkTheme
                ? AppColors.darkBackground
                : AppColors.lightBackground,
          ),
          child: Stack(
            children: [
              ...arrows.map((arrow) {
                final color = Color(arrow.color ?? 0xFF000000);

                return Positioned.fill(
                  child: CustomPaint(
                    painter: UnificationPath(
                      color: color,
                      y1: arrow.y1,
                      y2: arrow.y2,
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
