import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';

class PurchaseGroupBranch extends StatelessWidget {
  const PurchaseGroupBranch({
    super.key,
    required this.selecting,
    required this.selected,
    this.onSelected,
    this.width = 24.0,
  });

  final bool selecting;
  final bool selected;
  final void Function(bool selected)? onSelected;
  final double width;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return SizedBox(
      width: width,
      child: Stack(alignment: Alignment.center, children: [
        Divider(
          thickness: 2.0,
          color: isDarkTheme
              ? AppColors.darkAccentStroke
              : AppColors.lightAccentStroke,
        ),
        // if (selecting)
        //   Checkbox(
        //     value: selected,
        //     onChanged: (selected) {
        //       onSelected?.call(selected ?? false);
        //     },
        //   )
      ]),
    );
  }
}
