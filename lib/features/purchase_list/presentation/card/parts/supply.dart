import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/purchase_list/data/models/supply.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';

class PurchaseCardSupply extends StatelessWidget {
  const PurchaseCardSupply({
    super.key,
    required this.supply,
    required this.unitType,
  });

  final SupplyModel supply;
  final UnitType unitType;

  // Color _getPaymentColor(SupplyPaymentState state) {
  //   switch (state) {
  //     case SupplyPaymentState.ready:
  //       return SupplyPaymentStateColor.green.get();
  //     case SupplyPaymentState.inProcess:
  //       return SupplyPaymentStateColor.purple.get();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: '${supply.state?.getName()}',
      textStyle: Fonts.labelSmall.merge(TextStyle(
        color:
            (supply.state ?? SupplyState.inProcess).getSupplyStateColor().get(),
      )),
      child: CustomCard(
        onTap: () {},
        onSecondaryTapDown: (details) {
          CustomDropdownMenu.instance.show(
            context: context,
            items: [
              CustomDropdownMenuItem(
                onTap: () {},
                icon: Assets.icons.edit,
                text: 'Изменить',
              ),
              CustomDropdownMenuItem(
                onTap: () {},
                icon: Assets.icons.delete,
                text: 'Удалить',
              ),
            ],
            position: details.globalPosition,
          );
        },
        border: Border.all(
          color: (supply.state ?? SupplyState.inProcess)
              .getSupplyStateColor()
              .get(),
          width: 2.0,
        ),
        padding: EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(children: [
              if (supply.supplyDate != null)
                Text(
                  '-> ${getDateString(supply.supplyDate!)}',
                  style: Fonts.labelSmall.merge(
                    TextStyle(
                      fontFamily: Fonts.mono,
                    ),
                  ),
                ),
              if (supply.supplyDate != null) SizedBox(width: 8.0),
              Text(
                '${supply.quantity} ${unitType.getName()}',
                style: Fonts.labelSmall.merge(
                  TextStyle(
                    fontFamily: Fonts.mono,
                    color: (supply.state ?? SupplyState.inProcess)
                        .getSupplyStateColor()
                        .get(),
                  ),
                ),
              ),
            ]),
            Column(children: [
              if (supply.payments != null)
                ...supply.payments!.map((payment) {
                  return Row(
                    children: [
                      Text(
                        getDateString(payment.date ?? DateTime(2000)),
                        style: Fonts.labelSmall.merge(
                          TextStyle(
                            fontFamily: Fonts.mono,
                          ),
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Text(
                        '${payment.cost?.toStringAsFixed(2)} ₽',
                        style: Fonts.labelSmall.merge(
                          TextStyle(
                            fontFamily: Fonts.mono,
                            // color: _getPaymentColor(
                            //   payment.state ?? SupplyPaymentState.inProcess,
                            // ),
                          ),
                        ),
                      ),
                    ],
                  );
                })
            ]),
          ],
        ),
      ),
    );
  }
}
