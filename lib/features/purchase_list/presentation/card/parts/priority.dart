import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class PurchaseCardPriority extends StatelessWidget {
  const PurchaseCardPriority({
    super.key,
    required this.priority,
    this.onTap,
  });

  final int priority;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return GhostButton(
      backgroundOffset: Offset(-2, -2),
      onTap: onTap,
      child: SizedBox(
        width: 24.0,
        height: 24.0,
        child: Center(
          child: Text(
            priority.toString(),
            style: Fonts.labelSmall.merge(
              TextStyle(
                fontFamily: Fonts.mono,
                color: isDarkTheme
                    ? AppColors.darkSecondary
                    : AppColors.lightSecondary,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
