import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/purchase_list/data/models/purchase_item.dart';
import 'package:sphere/features/purchase_list/data/models/supply.dart';
import 'package:sphere/features/purchase_list/presentation/card/index.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/supply.dart';

class PurchaseCardSuplies extends StatelessWidget {
  PurchaseCardSuplies({
    super.key,
    required this.data,
    required this.setSupplyCoordinates,
  });

  final PurchaseItemModel data;
  final void Function(Map<String, SupplyFromToObject> newSupplyCoordinates)
      setSupplyCoordinates;

  final Map<String, GlobalKey> _supplyKeys = {};

  void _updateSupplyCoordinates(BuildContext context) {
    final Map<String, SupplyFromToObject> coordinates = {};

    // Получаем RenderBox родительского контейнера
    final RenderBox? parentBox = context.findRenderObject() as RenderBox?;
    if (parentBox == null) {
      print('Parent RenderBox is null');
      return;
    }

    // print('Updating supply coordinates...');

    if (data.supplies == null) {
      print('Supplies is null');
      return;
    }

    for (final supply in data.supplies!) {
      if (supply.id == null) {
        print('Supply id is null');
        continue;
      }

      final key = _supplyKeys[supply.id!];
      if (key == null) {
        print('Key is null for supply ${supply.id}');
        continue;
      }

      if (key.currentContext == null) {
        print('Current context is null for supply ${supply.id}');
        continue;
      }

      final RenderBox renderBox =
          key.currentContext!.findRenderObject() as RenderBox;

      // Преобразуем глобальные координаты в локальные относительно родителя
      final offset = renderBox.localToGlobal(Offset.zero);
      final parentOffset = parentBox.localToGlobal(Offset.zero);
      final relativeY = offset.dy - parentOffset.dy + renderBox.size.height / 2;

      final color =
          (supply.state ?? SupplyState.inProcess).getSupplyStateColor().get();

      // print('Supply ${supply.id}: y=$relativeY, forInputs=${supply.forInputs}');

      coordinates[supply.id!] = SupplyFromToObject(
        supply.forInputs ?? [],
        relativeY,
        color,
      );
    }

    setSupplyCoordinates(coordinates);
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateSupplyCoordinates(context);
    });

    Offset myPointerDragAnchorStrategy(
      Draggable<Object> draggable,
      BuildContext context,
      Offset position,
    ) {
      return Offset(15, 15);
    }

    return Expanded(
      flex: 2,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        spacing: 12.0,
        children: [
          if (data.storageQuantity != null && data.storageQuantity! > 0.0)
            Draggable<SupplyModel>(
              data: SupplyModel(
                id: 'storage',
                quantity: data.storageQuantity!,
                state: SupplyState.storage,
                forInputs: [],
              ),
              dragAnchorStrategy: myPointerDragAnchorStrategy,
              feedback: Container(
                height: 30.0,
                width: 30.0,
                decoration: BoxDecoration(
                  color: SupplyState.storage.getSupplyStateColor().get(),
                  borderRadius: BorderRadius.circular(1000.0),
                ),
              ),
              childWhenDragging: Opacity(
                opacity: 0.5,
                child: PurchaseCardSupply(
                  key: ValueKey('storage'),
                  supply: SupplyModel(
                    id: 'storage',
                    quantity: data.storageQuantity!,
                    state: SupplyState.storage,
                    forInputs: [],
                  ),
                  unitType: data.unitType ?? UnitType.kg,
                ),
              ),
              onDragEnd: (details) {
                // Логика при завершении перетаскивания
              },
              child: PurchaseCardSupply(
                key: ValueKey('storage'),
                supply: SupplyModel(
                  id: 'storage',
                  quantity: data.storageQuantity!,
                  state: SupplyState.storage,
                  forInputs: [],
                ),
                unitType: data.unitType ?? UnitType.kg,
              ),
            ),
          if ((data.supplies ?? []).isNotEmpty)
            ...data.supplies!.map(
              (supply) {
                if (supply.id != null) {
                  _supplyKeys[supply.id!] = GlobalKey();
                }

                final child = PurchaseCardSupply(
                  key: supply.id != null
                      ? _supplyKeys[supply.id!]
                      : ValueKey(supply.id),
                  supply: supply,
                  unitType: data.unitType ?? UnitType.kg,
                );

                return Draggable<SupplyModel>(
                  data: supply,
                  dragAnchorStrategy: myPointerDragAnchorStrategy,
                  feedback: Container(
                    height: 30.0,
                    width: 30.0,
                    decoration: BoxDecoration(
                      color: supply.state?.getSupplyStateColor().get(),
                      borderRadius: BorderRadius.circular(1000.0),
                    ),
                  ),
                  childWhenDragging: Opacity(opacity: 0.5, child: child),
                  onDragEnd: (details) {
                    // Логика при завершении перетаскивания
                  },
                  child: child,
                );
              },
            ),
          // InkWell(
          //   borderRadius: BorderRadius.circular(20.0),
          //   onTap: () {
          //     showBaseDialog(context, builder: (context) {
          //       return Column();
          //     });
          //   },
          //   child: Ink(
          //     padding: EdgeInsets.all(12.0),
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(20.0),
          //     ),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.center,
          //       children: [
          //         SVG(Assets.icons.add),
          //         SizedBox(width: 4.0),
          //         Text('Добавить поставку', style: Fonts.labelSmall),
          //       ],
          //     ),
          //   ),
          // )
        ],
      ),
    );
  }
}
