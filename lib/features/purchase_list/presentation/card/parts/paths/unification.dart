import 'package:flutter/material.dart';

class UnificationPath extends CustomPainter {
  UnificationPath({
    required this.color,
    this.strokeWidth = 2.0,
    this.borderWidth = 3.0,
    required this.y1,
    required this.y2,
  });

  final Color color;
  final double strokeWidth;
  final double borderWidth;
  final double y1; // Относительные координаты (0-100)
  final double y2; // Относительные координаты (0-100)

  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    // final double height = size.height;

    // Преобразуем относительные значения в пиксели
    final double startY = y1;
    final double endY = y2;

    // print('arrow space width: $width');
    // print('arrow space height: $height');
    // print('arrow start: $startY');
    // print('arrow end: $endY');

    // Основная линия
    final Path path = Path();
    path.moveTo(0.0, startY);
    path.cubicTo(
      width * 0.6,
      startY,
      width * 0.4,
      endY,
      width,
      endY,
    );

    // Рисуем белую обводку (границу)
    final Paint borderPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.66)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth + borderWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawPath(path, borderPaint);

    // Рисуем основную линию
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawPath(path, paint);

    // Рисуем кружок на левом конце
    final double circleRadius = 6.0;
    canvas.drawCircle(Offset(0.0, startY), circleRadius, paint);
  }

  @override
  bool shouldRepaint(covariant UnificationPath oldDelegate) {
    // Перерисовываем только если изменились координаты или цвет
    return oldDelegate.y1 != y1 ||
        oldDelegate.y2 != y2 ||
        oldDelegate.color != color;
  }
}
