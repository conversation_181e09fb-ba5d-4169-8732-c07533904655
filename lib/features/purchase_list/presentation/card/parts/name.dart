import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/quantity.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class PurchaseCardName extends StatelessWidget {
  const PurchaseCardName({
    super.key,
    required this.data,
    this.isExpanded = false,
    this.refresher,
  });

  final ProvisionMaterialModel data;
  final bool isExpanded;
  final void Function()? refresher;

  @override
  Widget build(BuildContext context) {
    final selectedFilter =
        context.read<BlocPurchaseList>().state.selectedFilter;
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Flexible(
                    child: Text(
                      selectedFilter == ProvisionsFilter.cooperation
                          ? data.products?.first.product?.name ??
                              'Пустое название материала'
                          : data.materialName ?? 'Пустое название материала',
                      style: Fonts.titleSmall,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  // TODO: total quantity for cooperation
                  if (data.products?.first.multipliedQuantity != null &&
                      selectedFilter == ProvisionsFilter.cooperation)
                    SizedBox(width: 12.0),
                  if (data.products?.first.multipliedQuantity != null &&
                      selectedFilter == ProvisionsFilter.cooperation)
                    Text('x${data.products?.first.multipliedQuantity}',
                        style: Fonts.labelSmall.merge(TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        ))),
                  if (data.materialRequirements != null &&
                      data.materialRequirements!.isNotEmpty)
                    SizedBox(width: 12.0),
                  if (data.materialRequirements != null &&
                      data.materialRequirements!.isNotEmpty)
                    Tooltip(
                      message:
                          'Дополнительное требование к материалу:\n${data.materialRequirements ?? 'Requirements'}',
                      textStyle: Fonts.bodyMedium,
                      child: SVG(
                        Assets.icons.help,
                        color: isDarkTheme
                            ? AppColors.darkWarning
                            : AppColors.lightWarning,
                      ),
                    ),
                  // PurchaseCardStates(
                  //   states: [
                  //     ...(data.supplies ?? []).map((supply) {
                  //       return supply.state ?? SupplyState.inProcess;
                  //     })
                  //   ],
                  // ),
                ],
              ),
              // Wrap(spacing: 6.0, children: [
              //   if (data.type != null)
              //     Text(
              //       data.type!,
              //       style: Fonts.bodySmall.merge(
              //         TextStyle(
              //           height: 1.1,
              //           color: isDarkTheme
              //               ? AppColors.darkDescription
              //               : AppColors.lightDescription,
              //         ),
              //       ),
              //     ),
              //   if (data.contractor != null)
              //     GhostButton(
              //       onTap: () {},
              //       child: Text(
              //         data.contractor!,
              //         style: Fonts.labelSmall.merge(
              //           TextStyle(
              //             fontSize: 14.0,
              //             height: 1.1,
              //             color: isDarkTheme
              //                 ? AppColors.darkSecondary
              //                 : AppColors.lightSecondary,
              //           ),
              //         ),
              //       ),
              //     ),
              // ]),
            ],
          ),
        ),
        if (selectedFilter == ProvisionsFilter.cooperation &&
            data.materialName != null)
          SizedBox(width: 8.0),
        if (selectedFilter == ProvisionsFilter.cooperation &&
            data.materialName != null)
          Row(children: [
            Text(
              'Наш материал:',
              style: Fonts.bodySmall.merge(TextStyle(
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
              )),
            ),
            Checkbox.adaptive(
              value: data.products?.first.product?.parameters?.needMaterial ??
                  false,
              onChanged: (value) async {
                final product = data.products?.first.product;
                final productId = product?.id;
                final parameters = product?.parameters;
                if (productId == null || parameters == null) return;
                await ProductRepository.updateParameters(
                  productId,
                  parameters.copyWith(needMaterial: value),
                );
                refresher?.call();
              },
            )
          ]),
        if (selectedFilter == ProvisionsFilter.cooperation &&
            data.materialName != null)
          SizedBox(width: 8.0),
        if (selectedFilter == ProvisionsFilter.cooperation &&
            data.materialName != null)
          Text(
            data.materialName!,
            style: Fonts.labelSmall,
          ),
        if (selectedFilter != ProvisionsFilter.cooperation)
          SizedBox(width: 8.0),
        if (selectedFilter != ProvisionsFilter.cooperation)
          PurchaseCardQuantity(
            quantity: data.quantity ?? 0.0,
            totalQuantity: data.totalQuantity,
            unitType: data.unitType ?? UnitType.kg,
          ),
        // if (data.storageQuantity != null && data.storageQuantity! > 0)
        //   SizedBox(width: 8.0),
        // if (data.storageQuantity != null && data.storageQuantity! > 0)
        //   Tooltip(
        //     message: 'Остатки, которые можно использовать',
        //     textStyle: Fonts.labelSmall,
        //     child: InkWell(
        //       splashFactory: InkSparkle.splashFactory,
        //       hoverColor: Color(0x20808080),
        //       borderRadius: BorderRadius.circular(4.0),
        //       onTap: () {},
        //       child: Ink(
        //         padding: EdgeInsets.symmetric(
        //           horizontal: 6.0,
        //           vertical: 2.0,
        //         ),
        //         child: Row(
        //           children: [
        //             SVG(Assets.icons.warehouse, width: 18.0),
        //             SizedBox(width: 4.0),
        //             Text(
        //               '${data.storageQuantity} ${data.unitType?.getName()}',
        //               style: Fonts.labelSmall,
        //             ),
        //           ],
        //         ),
        //       ),
        //     ),
        //   )
      ],
    );
  }
}
