import 'package:flutter/material.dart';
import 'package:sphere/features/purchase_list/data/models/supply.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class PurchaseCardStates extends StatelessWidget {
  const PurchaseCardStates({
    super.key,
    required this.states,
  });

  final List<SupplyState> states;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: SizedBox(
        width: 180,
        child: Wrap(
          spacing: 6.0,
          runSpacing: 6.0,
          alignment: WrapAlignment.start,
          children: List.generate(states.length, (index) {
            final state = states[index];

            return Tooltip(
              message: state.getName(),
              textStyle: Fonts.labelSmall.merge(TextStyle(
                fontSize: 14.0,
                color: state.getSupplyStateColor().get(),
              )),
              child: PurchaseCardState(
                color: state.getSupplyStateColor().get(),
              ),
            );
          }),
        ),
      ),
    );
  }
}

class PurchaseCardState extends StatelessWidget {
  const PurchaseCardState({super.key, this.color = AppColors.medium});

  final Color color;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 10.0,
      height: 10.0,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1000),
        color: color,
      ),
    );
  }
}
