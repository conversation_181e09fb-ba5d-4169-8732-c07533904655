import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/models/purchase_item.dart';
import 'package:sphere/features/purchase_list/data/models/supply.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/arrows/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/branch.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/inputs.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/name.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/suplies.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/wrapper.dart';

class ProvisionMaterialCard extends StatefulWidget {
  const ProvisionMaterialCard({
    super.key,
    this.onTap,
    this.isChild = false,
    this.childType = PurchaseItemChildType.start,
    this.selected = false,
    this.onSelected,
    this.onChangePriority,
    required this.data,
    required this.refresher,
    this.isLoading = false,
    this.forceExpanded = false,
  });

  final void Function()? onTap;
  final bool isChild;
  final PurchaseItemChildType childType;
  final bool selected;
  final void Function(bool value)? onSelected;
  final void Function(int priority)? onChangePriority;
  final ProvisionMaterialModel data;
  final void Function() refresher;
  final bool isLoading;
  final bool forceExpanded;

  @override
  State<ProvisionMaterialCard> createState() => _ProvisionMaterialCardState();
}

class _ProvisionMaterialCardState extends State<ProvisionMaterialCard>
    with TickerProviderStateMixin {
  final ValueNotifier<Map<String, double>> _inputsCoordinatesNotifier =
      ValueNotifier({});
  final ValueNotifier<Map<String, SupplyFromToObject>>
      _supplyCoordinatesNotifier = ValueNotifier({});
  bool _isExpanded = false;

  void _toggleExpand() {
    if (widget.forceExpanded) return;
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _setInputsCoordinates(Map<String, double> newInputsCoordinates) {
    _inputsCoordinatesNotifier.value = newInputsCoordinates;
    _calculateArrowsCoordinates();
  }

  void _setSupplyCoordinates(
      Map<String, SupplyFromToObject> newSupplyCoordinates) {
    _supplyCoordinatesNotifier.value = newSupplyCoordinates;
    _calculateArrowsCoordinates();
  }

  void _calculateArrowsCoordinates() {
    final inputsCoordinates = _inputsCoordinatesNotifier.value;
    final supplyCoordinates = _supplyCoordinatesNotifier.value;

    if (inputsCoordinates.isEmpty || supplyCoordinates.isEmpty) {
      context.read<BlocArrows>().add(SetArrows(
          cardId: widget.data.products
                  ?.map((product) => product.uniqueId)
                  .join('') ??
              'null_id',
          arrows: []));
      return;
    }

    List<PurchaseCardArrowModel> arrows = [];

    supplyCoordinates.forEach((id, object) {
      print('supplyLinks: ${object.forInputs}');
      for (final input in object.forInputs) {
        print('provisionId: ${input.provisionItemId}');
        if (inputsCoordinates.containsKey(input.provisionItemId)) {
          final y1 = inputsCoordinates[input.provisionItemId] ?? 0.0;
          final y2 = object.y;
          arrows.add(PurchaseCardArrowModel(
            y1: y1,
            y2: y2,
            color: object.color?.value,
          ));
          print('arrow: $y1 $y2');
        } else {
          print(
            'error: ${input.provisionItemId} not contains in inputCoordinates ($inputsCoordinates)',
          );
        }
      }
    });
    print('arrows: $arrows\n\n');

    context.read<BlocArrows>().add(SetArrows(
          cardId: widget.data.products
                  ?.map((product) => product.uniqueId)
                  .join('') ??
              'null_id',
          arrows: arrows,
        ));
  }

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.forceExpanded;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, size) {
      final isSmall = size.maxHeight <= 100.0;
      final onTap = widget.onTap ?? _toggleExpand;

      return BlocBuilder<BlocPurchaseList, BlocPurchaseListState>(
        builder: (context, state) {
          return PurchaseCardWrapper(
            isSmall: false,
            onTap: onTap,
            // onSecondaryTapDown: (details) {
            //   if (!widget.isChild) return;
            //   CustomDropdownMenu.instance.show(
            //     context: context,
            //     items: [
            //       CustomDropdownMenuItem(
            //         icon: Assets.icons.add,
            //         text: 'Добавить поставку',
            //         description: 'Для материала "${widget.data.materialName}"',
            //         onTap: () {
            //           // launchUrl(Uri.parse(
            //           //   API.getBaseURL() +
            //           //       PurchaseListRepository.export(widget.data.id!),
            //           // ));
            //           CustomDropdownMenu.instance.hide();
            //         },
            //       ),
            //     ],
            //     position: details.globalPosition,
            //   );
            // },
            isListItem: widget.isChild,
            isExpanded: _isExpanded,
            isLoading: widget.isLoading,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                PurchaseCardBranch(
                  childType: widget.childType,
                  isChild: widget.isChild,
                  // selecting: state.selecting,
                  selecting: false,
                  selected: widget.selected,
                  onSelected: widget.onSelected,
                  height: size.maxHeight,
                ),
                SizedBox(width: 12.0),
                Expanded(
                  child: ShaderMask(
                    blendMode: _isExpanded || isSmall
                        ? BlendMode.dst
                        : BlendMode.modulate,
                    shaderCallback: (bounds) {
                      return LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Colors.white, Colors.white.withAlpha(0)],
                        stops: [0.50, 1.0],
                      ).createShader(bounds);
                    },
                    child: ClipRRect(
                      child: ScrollConfiguration(
                        behavior: ScrollConfiguration.of(context)
                            .copyWith(scrollbars: false),
                        child: SingleChildScrollView(
                          physics: NeverScrollableScrollPhysics(),
                          child: Column(
                            children: [
                              SizedBox(height: 12.0),
                              PurchaseCardName(
                                data: widget.data,
                                isExpanded: _isExpanded || isSmall,
                                refresher: widget.refresher,
                              ),
                              SizedBox(height: 12.0),
                              IntrinsicHeight(
                                child: Row(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    ProvisionProductsCard(
                                      data: widget.data,
                                      setInputsCoordinates:
                                          _setInputsCoordinates,
                                      refresher: widget.refresher,
                                    ),
                                    // if (widget.isChild) SizedBox(width: 6.0),
                                    // if (widget.isChild)
                                    //   SizedBox(
                                    //     width: 100,
                                    //     child: PurchaseCardArrows(
                                    //       cardId: widget.data.products
                                    //               ?.map((product) =>
                                    //                   product.uniqueId)
                                    //               .join('') ??
                                    //           'null_id',
                                    //     ),
                                    //   ),
                                    if (!widget.isChild)
                                      Expanded(flex: 3, child: SizedBox()),
                                    if (widget.isChild) SizedBox(width: 6.0),
                                    if (widget.isChild)
                                      PurchaseCardSuplies(
                                        data: PurchaseItemModel(
                                          supplies: [
                                            ...(widget.data.deliveries ?? [])
                                                .map((supply) {
                                              double quantity = 0.0;

                                              supply.links?.forEach((link) {
                                                quantity +=
                                                    link.quantity ?? 0.0;
                                              });

                                              return SupplyModel(
                                                id: supply.id,
                                                supplyDate: supply.expectedDate,
                                                state: SupplyState.inProcess,
                                                forInputs: supply.links,
                                                quantity: quantity,
                                              );
                                            }),
                                          ],
                                          storageQuantity:
                                              widget.data.storageQuantity,
                                        ),
                                        setSupplyCoordinates:
                                            _setSupplyCoordinates,
                                      ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 12.0),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    });
  }
}

class SupplyFromToObject {
  final List<SupplyInputModel> forInputs;
  final double y;
  final Color? color;

  SupplyFromToObject(
    this.forInputs,
    this.y,
    this.color,
  );
}

enum PurchaseItemChildType {
  start,
  middle,
  end;
}
