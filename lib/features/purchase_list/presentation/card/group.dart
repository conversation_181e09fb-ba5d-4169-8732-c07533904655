import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/core/helpers/pick_file.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/client/data/repositories/index.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/index.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/card/helpers/group_items.dart';
import 'package:sphere/features/purchase_list/presentation/card/index.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/group_branch.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/group_name.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/wrapper.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:url_launcher/url_launcher.dart';

class PurchaseGroupCard extends StatefulWidget {
  const PurchaseGroupCard({
    super.key,
    this.selected = false,
    this.onSelected,
    required this.data,
    required this.refresher,
    this.isLoading = false,
    this.filter = ProvisionsFilter.materials,
  });

  final bool selected;
  final void Function(bool value)? onSelected;
  final ProvisionLotModel data;
  final void Function() refresher;
  final bool isLoading;
  final ProvisionsFilter filter;

  @override
  State<PurchaseGroupCard> createState() => _PurchaseGroupCardState();
}

class _PurchaseGroupCardState extends State<PurchaseGroupCard> {
  bool _isExpanded = false;

  // form
  final _contractorNameController = TextEditingController();
  final _numberOfContractController = TextEditingController();
  final _priceOfContractController = TextEditingController();
  DateTime? _startContractDate;
  DateTime? _endContractDate;
  File? _selectedFile;
  List<ClientModel> _clients = [];
  ClientModel? _selectedClient;
  List<ProvisionPaymentDetailsModel> _paymentDetails = [];
  List<DeliveryGroupModel> _deliveryGroups = [DeliveryGroupModel()];
  final List<TextEditingController> _quantityControllers = [];
  final List<TextEditingController> _priceControllers = [];

  // form for adding
  DateTime date = DateTime.now();
  final amountController = TextEditingController(text: '0.0');

  @override
  void initState() {
    super.initState();

    _selectedClient = widget.data.supplier;
    _endContractDate = widget.data.contractEndDate;
    _startContractDate = widget.data.contractStartDate;
    _priceOfContractController.text =
        (widget.data.contractPrice ?? '').toString();
    _numberOfContractController.text = widget.data.contractNumber ?? '';
    _contractorNameController.text = widget.data.supplier?.name ?? '';

    // Create modifiable copies of the lists
    _paymentDetails = widget.data.paymentDetails != null
        ? List.from(widget.data.paymentDetails!)
        : [];

    _deliveryGroups = widget.data.deliveryGroups != null
        ? widget.data.deliveryGroups!
            .map((e) => e.copyWith(
                  materials: e.materials?.map((m) => m.copyWith()).toList(),
                ))
            .toList()
        : [DeliveryGroupModel()];

    _initializeControllers();
  }

  @override
  void dispose() {
    _contractorNameController.dispose();
    _numberOfContractController.dispose();
    _priceOfContractController.dispose();
    amountController.dispose();

    // Очищаем все контроллеры для материалов
    for (var controller in _quantityControllers) {
      controller.dispose();
    }
    for (var controller in _priceControllers) {
      controller.dispose();
    }

    super.dispose();
  }

  void _initializeControllers() {
    // Очищаем старые контроллеры
    // _quantityControllers.clear();
    // _priceControllers.clear();

    // Создаем контроллеры для каждого материала в каждой группе поставок
    for (var group in _deliveryGroups) {
      for (var item in widget.data.provisionItems ?? []) {
        final material = group.materials?.firstWhere(
          (m) => m.provisionItemId == item.id,
          orElse: () => DeliveryMaterialModel(
            provisionItemId: item.id,
            price: 0.0,
            quantity: 0.0,
          ),
        );

        _quantityControllers.add(TextEditingController(
          text: material?.quantity?.toString() ?? '0.0',
        ));
        _priceControllers.add(TextEditingController(
          text: '0.0',
        ));
      }
    }
  }

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  void _contract() async {
    if (widget.data.id == null || _selectedFile == null) return;

    // Фильтруем материалы с нулевым или отрицательным количеством
    final filteredDeliveryGroups = _deliveryGroups.map((group) {
      final filteredMaterials = group.materials?.where((material) {
        return (material.quantity ?? 0) > 0;
      }).toList();
      return group.copyWith(materials: filteredMaterials);
    }).toList();

    final result = await PurchaseListRepository.contract(
      provisionId: widget.data.id!,
      supplierName:
          _selectedClient != null ? null : _contractorNameController.text,
      supplierId: _selectedClient?.id,
      contractPrice: double.tryParse(_priceOfContractController.text) ?? 0.0,
      contractNumber: _numberOfContractController.text,
      contractStartDate: _startContractDate ?? DateTime.now(),
      contractEndDate:
          _endContractDate ?? DateTime.now().add(Duration(days: 1)),
      paymentDetails: _paymentDetails,
      file: _selectedFile!,
      deliveryGroups:
          filteredDeliveryGroups, // Используем отфильтрованные группы
    );

    if ((result?.statusCode ?? 500) < 300) {
      context.router.maybePop();
      widget.refresher();
    }
  }

  void _updateContract() async {
    if (widget.data.id == null) return;

    // Фильтруем материалы с нулевым или отрицательным количеством
    final filteredDeliveryGroups = _deliveryGroups.map((group) {
      final filteredMaterials = group.materials?.where((material) {
        return (material.quantity ?? 0) > 0;
      }).toList();
      return group.copyWith(materials: filteredMaterials);
    }).toList();

    final result = await PurchaseListRepository.updateContract(
      provisionId: widget.data.id!,
      supplierName:
          _selectedClient != null ? null : _contractorNameController.text,
      supplierId: _selectedClient?.id,
      contractPrice: double.tryParse(_priceOfContractController.text) ?? 0.0,
      contractNumber: _numberOfContractController.text,
      contractStartDate: _startContractDate ?? DateTime.now(),
      contractEndDate:
          _endContractDate ?? DateTime.now().add(Duration(days: 1)),
      paymentDetails: _paymentDetails,
      file: _selectedFile,
      deliveryGroups:
          filteredDeliveryGroups, // Используем отфильтрованные группы
    );

    if ((result?.statusCode ?? 500) < 300) {
      context.router.maybePop();
      widget.refresher();
    }
  }

  void _enterDataIntoContract({bool isUpdate = false}) {
    showBaseDialog(
      context,
      padding: EdgeInsets.all(0),
      maxWidth: 1200.0,
      builder: (context) {
        // final materials = provisionGroupItems(
        //   items: widget.data.provisionItems ?? [],
        //   filter: widget.filter,
        // );
        final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

        return StatefulBuilder(
          builder: (context, setDialogState) {
            void selectFile() async {
              final file = await pickFile();
              setDialogState(() {
                _selectedFile = file;
              });
            }

            void addPayment() {
              _paymentDetails.add(ProvisionPaymentDetailsModel(
                paymentDate: date,
                amount: double.tryParse(amountController.text) ?? 0.0,
              ));
              setDialogState(() => {});
            }

            void removePayment(int index) {
              _paymentDetails.removeAt(index);
              setDialogState(() => {});
            }

            void getClients() async {
              if (_contractorNameController.text.isEmpty) {
                setDialogState(() {
                  _clients = [];
                });
                return;
              }
              final result = await ClientRepository.search(SearchModel(
                filters: SearchFiltersModel(
                  query: _contractorNameController.text,
                  clientType: ClientType.supplier,
                ),
              ));

              setDialogState(() {
                _clients = result?.data?.items ?? [];
              });
            }

            void addDeliveryGroup() {
              _deliveryGroups.add(DeliveryGroupModel());
              _initializeControllers();
              setDialogState(() {});
            }

            void removeDeliveryGroup(int index) {
              _deliveryGroups.removeAt(index);
              setDialogState(() {});
            }

            void updateDeliveryDate(int index, DateTime? date) {
              if (date != null) {
                _deliveryGroups[index] = _deliveryGroups[index].copyWith(
                  expectedDate: date,
                  // materials:
                );
                setDialogState(() {});
              }
            }

            void updateDeliveryMaterialQuantityAndPrice(
              int groupIndex,
              int itemIndex,
              String quantity,
              String price,
            ) {
              if (groupIndex >= _deliveryGroups.length) return;

              // Создаем новую группу если нужно
              if (_deliveryGroups[groupIndex].materials == null) {
                _deliveryGroups[groupIndex] =
                    _deliveryGroups[groupIndex].copyWith(
                  materials: [],
                );
              }

              final currentMaterials = List<DeliveryMaterialModel>.from(
                  _deliveryGroups[groupIndex].materials ?? []);

              // Убедимся, что у нас достаточно элементов в списке материалов
              while (currentMaterials.length <= itemIndex) {
                currentMaterials.add(DeliveryMaterialModel(
                  provisionItemId: widget.data.provisionItems?[itemIndex].id,
                  price: 0.0,
                  quantity: 0.0,
                ));
              }

              // Обновляем количество и цену
              currentMaterials[itemIndex] =
                  currentMaterials[itemIndex].copyWith(
                quantity: double.tryParse(quantity) ?? 0.0,
                price: double.tryParse(price) ?? 0.0,
              );

              // Обновляем группу поставок
              _deliveryGroups[groupIndex] =
                  _deliveryGroups[groupIndex].copyWith(
                materials: currentMaterials,
              );

              setState(() {});
              setDialogState(() {});
            }

            return Column(
              children: [
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.all(18.0),
                    children: [
                      Text(
                        'Внесение данных по договору',
                        style: Fonts.titleSmall,
                      ),
                      SizedBox(height: 18.0),
                      Row(children: [
                        Expanded(
                          child: TextField(
                            onChanged: (_) {
                              if (_selectedClient != null) {
                                _selectedClient = null;
                              }
                              getClients();
                            },
                            controller: _contractorNameController,
                            style: Fonts.labelSmall,
                            decoration: InputDecoration(
                              labelText: 'Поставщик',
                            ),
                          ),
                        ),
                        SizedBox(width: 12.0),
                        Expanded(
                          child: TextField(
                            controller: _numberOfContractController,
                            style: Fonts.labelSmall,
                            decoration: InputDecoration(
                              labelText: 'Номер договора/спецификации/счета',
                            ),
                          ),
                        ),
                        SizedBox(width: 12.0),
                        Expanded(
                          child: TextField(
                            controller: _priceOfContractController,
                            style: Fonts.labelSmall,
                            decoration: InputDecoration(
                              labelText: 'Сумма договора',
                            ),
                          ),
                        ),
                      ]),
                      if (_clients.isNotEmpty || _selectedClient != null)
                        SizedBox(height: 8.0),
                      if (_clients.isNotEmpty || _selectedClient != null)
                        Wrap(
                          spacing: 8.0,
                          runSpacing: 8.0,
                          children: [
                            if (_selectedClient != null)
                              CustomChip(
                                selected: true,
                                text: _selectedClient?.name,
                              ),
                            if (_selectedClient == null)
                              ..._clients.map((client) {
                                return CustomChip(
                                  onTap: () {
                                    setDialogState(() {
                                      _contractorNameController.text =
                                          client.name ?? 'NAME NOT FOUND!';
                                      _selectedClient = client;
                                    });
                                  },
                                  text: client.name,
                                );
                              }),
                          ],
                        ),
                      SizedBox(height: 24.0),
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Дата начала договора:',
                                  style: Fonts.labelSmall,
                                ),
                                GhostButton(
                                  onTap: () async {
                                    final date = await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime.now(),
                                      lastDate: DateTime.now()
                                          .add(Duration(days: 730)),
                                    );

                                    setDialogState(() {
                                      _startContractDate = date;
                                    });
                                  },
                                  child: Text(
                                    getDateString(
                                        _startContractDate ?? DateTime.now()),
                                    style: Fonts.labelSmall.merge(
                                      TextStyle(
                                        color: isDarkTheme
                                            ? AppColors.darkSecondary
                                            : AppColors.lightSecondary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 30.0,
                            child: VerticalDivider(width: 24.0),
                          ),
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Дата окончания договора:',
                                  style: Fonts.labelSmall,
                                ),
                                GhostButton(
                                  onTap: () async {
                                    final date = await showDatePicker(
                                      context: context,
                                      initialDate:
                                          DateTime.now().add(Duration(days: 1)),
                                      firstDate:
                                          DateTime.now().add(Duration(days: 1)),
                                      lastDate: DateTime.now()
                                          .add(Duration(days: 730)),
                                    );

                                    setDialogState(() {
                                      _endContractDate = date;
                                    });
                                  },
                                  child: Text(
                                    getDateString(_endContractDate ??
                                        DateTime.now().add(Duration(days: 1))),
                                    style: Fonts.labelSmall.merge(
                                      TextStyle(
                                        color: isDarkTheme
                                            ? AppColors.darkSecondary
                                            : AppColors.lightSecondary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24.0),
                      Row(
                        children: [
                          Text(
                            'Даты оплат:',
                            style: Fonts.labelSmall,
                          ),
                          SizedBox(width: 8.0),
                          Expanded(
                            child: Wrap(
                                spacing: 4.0,
                                runSpacing: 4.0,
                                children: [
                                  ..._paymentDetails
                                      .asMap()
                                      .entries
                                      .map((entry) {
                                    final payment = entry.value;
                                    final index = entry.key;

                                    return CustomChip(
                                      onTap: () {
                                        removePayment(index);
                                      },
                                      child: Row(children: [
                                        Text(
                                          getDateString(payment.paymentDate ??
                                              DateTime.now()),
                                          style: Fonts.labelSmall,
                                        ),
                                        SizedBox(width: 4.0),
                                        Text(
                                          '${payment.amount ?? 0.0} ₽',
                                          style: Fonts.labelSmall.merge(
                                            TextStyle(
                                              color: isDarkTheme
                                                  ? AppColors.darkSecondary
                                                  : AppColors.lightSecondary,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 4.0),
                                        SVG(Assets.icons.close),
                                      ]),
                                    );
                                  }),
                                  CustomChip(
                                    onTap: () {
                                      showDialog(
                                          context: context,
                                          builder: (context) {
                                            return StatefulBuilder(builder:
                                                (context, setStateDialog) {
                                              return AlertDialog(
                                                backgroundColor: isDarkTheme
                                                    ? AppColors.darkBackground
                                                    : AppColors.lightBackground,
                                                content: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text('Дата оплаты:',
                                                              style: Fonts
                                                                  .labelSmall),
                                                          SizedBox(width: 4.0),
                                                          GhostButton(
                                                              onTap: () async {
                                                                date =
                                                                    await showDatePicker(
                                                                          context:
                                                                              context,
                                                                          initialDate:
                                                                              date,
                                                                          firstDate:
                                                                              DateTime.now(),
                                                                          lastDate:
                                                                              DateTime.now().add(Duration(days: 720)),
                                                                        ) ??
                                                                        date;
                                                                setStateDialog(
                                                                    () {});
                                                                setDialogState(
                                                                    () => {});
                                                                setState(() {});
                                                              },
                                                              child: Text(
                                                                  getDateString(
                                                                      date),
                                                                  style: Fonts
                                                                      .labelSmall
                                                                      .merge(TextStyle(
                                                                          color: isDarkTheme
                                                                              ? AppColors.darkSecondary
                                                                              : AppColors.lightSecondary)))),
                                                        ],
                                                      ),
                                                      SizedBox(height: 16.0),
                                                      Row(children: [
                                                        SizedBox(
                                                          width: 300.0,
                                                          child: TextField(
                                                            controller:
                                                                amountController,
                                                            style: Fonts
                                                                .labelSmall,
                                                            decoration:
                                                                InputDecoration(
                                                              labelText:
                                                                  'Стоимость',
                                                            ),
                                                          ),
                                                        ),
                                                        SizedBox(width: 4.0),
                                                        Text('₽',
                                                            style: Fonts
                                                                .labelSmall),
                                                      ]),
                                                    ]),
                                                actions: [
                                                  TextButton(
                                                      onPressed: () {
                                                        context.maybePop();
                                                        addPayment();
                                                      },
                                                      child: Text(
                                                        'Добавить',
                                                        style: Fonts.labelSmall,
                                                      )),
                                                ],
                                              );
                                            });
                                          });
                                    },
                                    child: SVG(Assets.icons.add),
                                  ),
                                ]),
                          ),
                        ],
                      ),
                      SizedBox(height: 24.0),
                      Row(children: [
                        Expanded(
                          child: Text(
                            _selectedFile?.path.split('\\').last ??
                                'Вы пока не выбрали документ',
                            style: Fonts.bodySmall,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 12.0),
                        GhostButton(
                          onTap: selectFile,
                          child: Text('Выбрать документ',
                              style: Fonts.labelSmall.merge(TextStyle(
                                  color: isDarkTheme
                                      ? AppColors.darkSecondary
                                      : AppColors.lightSecondary))),
                        ),
                      ]),
                      SizedBox(height: 24.0),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'График поставок:',
                            style: Fonts.labelSmall,
                          ),
                          SizedBox(height: 12.0),
                          ..._deliveryGroups.asMap().entries.map((entry) {
                            final groupIndex = entry.key;
                            final group = entry.value;
                            return Container(
                              margin: EdgeInsets.only(bottom: 12.0),
                              padding: EdgeInsets.all(12.0),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: isDarkTheme
                                      ? AppColors.darkStroke
                                      : AppColors.lightStroke,
                                ),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Row(children: [
                                          Text(
                                            'Поставка ${groupIndex + 1}',
                                            style: Fonts.labelSmall,
                                          ),
                                          if (_deliveryGroups.length > 1)
                                            SizedBox(width: 8.0),
                                          if (_deliveryGroups.length > 1)
                                            IconButton(
                                              icon: SVG(Assets.icons.delete,
                                                  color: isDarkTheme
                                                      ? AppColors.darkSecondary
                                                      : AppColors
                                                          .lightSecondary),
                                              onPressed: () =>
                                                  removeDeliveryGroup(
                                                      groupIndex),
                                            ),
                                          Spacer(),
                                          GhostButton(
                                            onTap: () async {
                                              final date = await showDatePicker(
                                                context: context,
                                                initialDate:
                                                    group.expectedDate ??
                                                        DateTime.now(),
                                                firstDate: DateTime.now(),
                                                lastDate: DateTime.now()
                                                    .add(Duration(days: 730)),
                                              );
                                              updateDeliveryDate(
                                                  groupIndex, date);
                                            },
                                            child: Text(
                                              group.expectedDate != null
                                                  ? getDateString(
                                                      group.expectedDate!)
                                                  : 'Выбрать дату',
                                              style: Fonts.labelSmall.merge(
                                                TextStyle(
                                                  color: isDarkTheme
                                                      ? AppColors.darkSecondary
                                                      : AppColors
                                                          .lightSecondary,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ]),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 12.0),
                                  ...List.generate(
                                    widget.data.provisionItems?.length ?? 0,
                                    (itemIndex) {
                                      final item = widget
                                          .data.provisionItems![itemIndex];
                                      final materialIndex = groupIndex *
                                              (widget.data.provisionItems
                                                      ?.length ??
                                                  0) +
                                          itemIndex;
                                      final quantityController =
                                          _quantityControllers.length >
                                                  materialIndex
                                              ? _quantityControllers[
                                                  materialIndex]
                                              : TextEditingController(
                                                  text: '0.0');
                                      final priceController = _priceControllers
                                                  .length >
                                              materialIndex
                                          ? _priceControllers[materialIndex]
                                          : TextEditingController(text: '0.0');

                                      // Вычисляем totalQuantityResult динамически
                                      double calculateTotalQuantityResult() {
                                        double totalDelivered = 0.0;
                                        for (var group in _deliveryGroups) {
                                          if (group.materials != null &&
                                              group.materials!.length >
                                                  itemIndex) {
                                            totalDelivered += group
                                                    .materials![itemIndex]
                                                    .quantity ??
                                                0.0;
                                          }
                                        }
                                        return (item.totalQuantity ?? 0.0) -
                                            totalDelivered;
                                      }

                                      // final totalQuantityResult =
                                      //     calculateTotalQuantityResult();

                                      return Column(
                                        children: [
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  '${item.product?.name}: ${item.featureType?.getName() ?? ''}',
                                                  style: Fonts.bodySmall,
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 8.0),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: TextField(
                                                  controller:
                                                      quantityController,
                                                  onChanged: (value) {
                                                    updateDeliveryMaterialQuantityAndPrice(
                                                      groupIndex,
                                                      itemIndex,
                                                      value,
                                                      priceController.text,
                                                    );
                                                    // Вызываем setState для обновления интерфейса
                                                    setDialogState(() {});
                                                  },
                                                  decoration: InputDecoration(
                                                    labelText: 'Количество',
                                                    suffixText:
                                                        '/ ${item.totalQuantity} / ${calculateTotalQuantityResult()}',
                                                  ),
                                                  style: Fonts.labelSmall,
                                                ),
                                              ),
                                              SizedBox(width: 12.0),
                                              Expanded(
                                                child: TextField(
                                                  controller: priceController,
                                                  onChanged: (value) {
                                                    updateDeliveryMaterialQuantityAndPrice(
                                                      groupIndex,
                                                      itemIndex,
                                                      quantityController.text,
                                                      value,
                                                    );
                                                  },
                                                  decoration: InputDecoration(
                                                    labelText:
                                                        'Цена за единицу',
                                                  ),
                                                  style: Fonts.labelSmall,
                                                ),
                                              ),
                                              SizedBox(width: 12.0),
                                              Text(
                                                '= ${(double.tryParse(quantityController.text) ?? 0) * (double.tryParse(priceController.text) ?? 0)} ₽',
                                                style: Fonts.bodySmall,
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 12.0),
                                        ],
                                      );
                                    },
                                  ),
                                ],
                              ),
                            );
                          }),
                          Center(
                            child: GhostButton(
                              onTap: addDeliveryGroup,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SVG(Assets.icons.add,
                                      color: isDarkTheme
                                          ? AppColors.darkSecondary
                                          : AppColors.lightSecondary),
                                  SizedBox(width: 8.0),
                                  Text('Добавить поставку',
                                      style: Fonts.labelSmall.merge(TextStyle(
                                          color: isDarkTheme
                                              ? AppColors.darkSecondary
                                              : AppColors.lightSecondary))),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(12.0),
                  child: CustomElevatedButton(
                    disabled: isUpdate ? false : _selectedFile == null,
                    type: CustomElevatedButtonTypes.accent,
                    text: 'Контрактация',
                    onPressed: isUpdate ? _updateContract : _contract,
                  ),
                )
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = context.read<BlocPurchaseList>().state;
    final materials = provisionGroupItems(
      items: widget.data.provisionItems ?? [],
      filter: widget.filter,
    );
    final selecting = state.selecting;

    return PurchaseCardWrapper(
      isLoading: widget.isLoading,
      padding: EdgeInsets.all(0.0),
      onTap: _toggleExpand,
      isExpanded: _isExpanded,
      child: ShaderMask(
        blendMode: _isExpanded ? BlendMode.dst : BlendMode.dstIn,
        shaderCallback: (bounds) {
          return LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.white, Colors.white.withAlpha(0)],
            stops: [0.50, 1.0],
          ).createShader(bounds);
        },
        child: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
          child: SingleChildScrollView(
            physics: NeverScrollableScrollPhysics(),
            child: Column(
              children: [
                SizedBox(height: 12.0),
                Row(children: [
                  SizedBox(width: 12.0),
                  PurchaseGroupBranch(
                    selecting: selecting,
                    selected: widget.selected,
                    onSelected: widget.onSelected,
                  ),
                  SizedBox(width: 12.0),
                  PurchaseGroupCardName(
                    data: widget.data,
                    name: widget.data.provisionName,
                    clients: widget.data.supplier?.name != null
                        ? [widget.data.supplier!.name!]
                        : [],
                    onSecondaryTapDown: (details) {
                      CustomDropdownMenu.instance.show(
                        context: context,
                        items: [
                          if (widget.data.provisionStatus ==
                              ProvisionStatus.direction)
                            CustomDropdownMenuItem(
                              icon: Assets.icons.paste,
                              text: 'Внести данные по договору',
                              onTap: () {
                                if (widget.data.id == null) return;
                                _enterDataIntoContract();
                                CustomDropdownMenu.instance.hide();
                              },
                            ),
                          if (widget.data.provisionStatus ==
                              ProvisionStatus.contracted)
                            CustomDropdownMenuItem(
                              icon: Assets.icons.edit,
                              text: 'Редактировать данные по договору',
                              onTap: () {
                                if (widget.data.id == null) return;
                                _enterDataIntoContract(isUpdate: true);
                                CustomDropdownMenu.instance.hide();
                              },
                            ),
                          CustomDropdownMenuItem(
                            icon: Assets.icons.description,
                            text: 'Экспорт в .xlsx',
                            onTap: () {
                              if (widget.data.id == null) return;
                              launchUrl(Uri.parse(
                                API.getBaseURL() +
                                    PurchaseListRepository.export(
                                        widget.data.id!),
                              ));
                              Future.delayed(Duration(seconds: 1),
                                  () => widget.refresher());
                              CustomDropdownMenu.instance.hide();
                            },
                          ),
                          CustomDropdownMenuItem(
                            icon: Assets.icons.copy,
                            text: 'Копировать ссылку',
                            onTap: () {
                              if (widget.data.id == null) return;
                              Clipboard.setData(
                                ClipboardData(
                                  text: API.getBaseURL() +
                                      PurchaseListRepository.export(
                                          widget.data.id!),
                                ),
                              );
                              CustomDropdownMenu.instance.hide();
                            },
                          ),
                        ],
                        position: details.globalPosition,
                      );
                    },
                  ),
                  SizedBox(width: 12.0),
                ]),
                SizedBox(height: 12.0),
                ...List.generate(materials.length, (index) {
                  final item = materials[index];
                  PurchaseItemChildType type;

                  if (index == 0) {
                    type = PurchaseItemChildType.start;
                  } else if (index < materials.length - 1) {
                    type = PurchaseItemChildType.middle;
                  } else {
                    type = PurchaseItemChildType.end;
                  }

                  return ProvisionMaterialCard(
                    isChild: true,
                    childType: type,
                    selected: widget.selected,
                    data: item,
                    refresher: widget.refresher,
                    forceExpanded: true,
                  );
                })
              ],
            ),
          ),
        ),
      ),
    );
  }
}
