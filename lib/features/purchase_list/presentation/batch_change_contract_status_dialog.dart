import 'package:flutter/material.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class BatchChangeContractStatusDialog extends StatefulWidget {
  const BatchChangeContractStatusDialog({
    super.key,
    required this.contracts,
    required this.onStatusChanged,
  });

  final List<ContractModel> contracts;
  final VoidCallback onStatusChanged;

  @override
  State<BatchChangeContractStatusDialog> createState() => _BatchChangeContractStatusDialogState();
}

class _BatchChangeContractStatusDialogState extends State<BatchChangeContractStatusDialog> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  ContractAction? _selectedAction;
  bool _isLoading = false;
  final List<String> _failedContracts = [];

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _changeStatusBatch() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedAction == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Выберите действие')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _failedContracts.clear();
    });

    try {
      int successCount = 0;
      
      for (final contract in widget.contracts) {
        if (contract.id == null) continue;
        
        try {
          final statusChangeData = ProvisionChangeContractStatusInput(
            contractId: contract.id,
            action: _selectedAction,
            reason: _reasonController.text.trim().isNotEmpty 
                ? _reasonController.text.trim() 
                : null,
          );

          final response = await PurchaseListRepositoryV2.changeContractStatus(
            statusChangeData,
          );

          if (response.statusCode == 200 || response.statusCode == 201) {
            successCount++;
          } else {
            _failedContracts.add(contract.contractNumber ?? contract.id!);
          }
        } catch (e) {
          _failedContracts.add(contract.contractNumber ?? contract.id!);
        }
      }

      if (mounted) {
        if (successCount > 0) {
          String message = 'Статус изменен для $successCount контракт(ов)';
          if (_failedContracts.isNotEmpty) {
            message += '\nОшибки: ${_failedContracts.join(', ')}';
          }
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              duration: Duration(seconds: _failedContracts.isNotEmpty ? 5 : 3),
            ),
          );
          
          widget.onStatusChanged();
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Не удалось изменить статус ни одного контракта'),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ошибка: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Изменить статус контрактов (${widget.contracts.length})',
              style: Fonts.titleSmall,
            ),
            const SizedBox(height: 16.0),
            
            // Список контрактов
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Контракты для изменения:',
                    style: Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8.0),
                  ...widget.contracts.take(5).map((contract) => Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Text(
                      '• ${contract.contractNumber ?? 'Без номера'} - ${contract.supplier?.name ?? 'Без поставщика'}',
                      style: Fonts.bodySmall,
                    ),
                  )),
                  if (widget.contracts.length > 5) ...[
                    Text(
                      '... и еще ${widget.contracts.length - 5} контракт(ов)',
                      style: Fonts.bodySmall.copyWith(fontStyle: FontStyle.italic),
                    ),
                  ],
                ],
              ),
            ),
            
            const SizedBox(height: 16.0),
            
            // Выбор действия
            Text(
              'Действие',
              style: Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8.0),
            
            ...ContractAction.values.map((action) => RadioListTile<ContractAction>(
              title: Text(action.displayName, style: Fonts.bodyMedium),
              value: action,
              groupValue: _selectedAction,
              onChanged: (value) {
                setState(() {
                  _selectedAction = value;
                });
              },
              contentPadding: EdgeInsets.zero,
            )),
            
            const SizedBox(height: 16.0),
            
            // Причина (опционально)
            TextFormField(
              controller: _reasonController,
              decoration: const InputDecoration(
                labelText: 'Причина (опционально)',
                border: OutlineInputBorder(),
                hintText: 'Укажите причину изменения статуса',
              ),
              style: Fonts.bodyMedium,
              maxLines: 3,
              minLines: 1,
            ),
            
            const SizedBox(height: 24.0),
            
            // Кнопки
            Row(
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Отмена',
                    style: Fonts.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: CustomElevatedButton(
                    onPressed: () {
                      if (!_isLoading) _changeStatusBatch();
                    },
                    text: _isLoading ? 'Изменение...' : 'Изменить статус всех',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
