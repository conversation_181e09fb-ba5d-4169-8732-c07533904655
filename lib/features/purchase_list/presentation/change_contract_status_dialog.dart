import 'package:flutter/material.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class ChangeContractStatusDialog extends StatefulWidget {
  const ChangeContractStatusDialog({
    super.key,
    required this.contract,
    required this.onStatusChanged,
  });

  final ContractModel contract;
  final VoidCallback onStatusChanged;

  @override
  State<ChangeContractStatusDialog> createState() =>
      _ChangeContractStatusDialogState();
}

class _ChangeContractStatusDialogState
    extends State<ChangeContractStatusDialog> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  ContractAction? _selectedAction;
  bool _isLoading = false;

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _changeStatus() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedAction == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Выберите действие')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final statusChangeData = ProvisionChangeContractStatusInput(
        contractId: widget.contract.id,
        action: _selectedAction,
        reason: _reasonController.text.trim().isNotEmpty
            ? _reasonController.text.trim()
            : null,
      );

      final response = await PurchaseListRepositoryV2.changeContractStatus(
        statusChangeData,
      );

      if (mounted) {
        if (response.statusCode == 200 || response.statusCode == 201) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.data?.message ??
                  'Статус контракта успешно изменен!'),
            ),
          );
          widget.onStatusChanged();
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Ошибка сервера: ${response.statusMessage ?? 'Неизвестная ошибка'}',
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Ошибка при изменении статуса контракта';
        if (e.toString().contains('DioException')) {
          errorMessage = 'Ошибка сети или сервера';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$errorMessage: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Изменить статус контракта',
              style: Fonts.titleSmall,
            ),
            const SizedBox(height: 16.0),

            // Информация о контракте
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Контракт: ${widget.contract.contractNumber ?? 'Без номера'}',
                    style:
                        Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
                  ),
                  if (widget.contract.supplier?.name != null) ...[
                    const SizedBox(height: 4.0),
                    Text(
                      'Поставщик: ${widget.contract.supplier!.name}',
                      style: Fonts.bodySmall,
                    ),
                  ],
                  if (widget.contract.contractPrice != null) ...[
                    const SizedBox(height: 4.0),
                    Text(
                      'Сумма: ${widget.contract.contractPrice!.toStringAsFixed(2)} ₽',
                      style: Fonts.bodySmall,
                    ),
                  ],
                  if (widget.contract.status != null) ...[
                    const SizedBox(height: 4.0),
                    Text(
                      'Текущий статус: ${widget.contract.status!.getName()}',
                      style: Fonts.bodySmall,
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16.0),

            // Выбор действия
            Text(
              'Действие',
              style: Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8.0),

            ...ContractAction.values
                .map((action) => RadioListTile<ContractAction>(
                      title: Text(action.displayName, style: Fonts.bodyMedium),
                      value: action,
                      groupValue: _selectedAction,
                      onChanged: (value) {
                        setState(() {
                          _selectedAction = value;
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                    )),

            const SizedBox(height: 16.0),

            // Причина (опционально)
            TextFormField(
              controller: _reasonController,
              decoration: const InputDecoration(
                labelText: 'Причина (опционально)',
                border: OutlineInputBorder(),
                hintText: 'Укажите причину изменения статуса',
              ),
              style: Fonts.bodyMedium,
              maxLines: 3,
              minLines: 1,
            ),

            const SizedBox(height: 24.0),

            // Кнопки
            Row(
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Отмена',
                    style: Fonts.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: CustomElevatedButton(
                    onPressed: () {
                      if (!_isLoading) _changeStatus();
                    },
                    text: _isLoading ? 'Изменение...' : 'Изменить статус',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
