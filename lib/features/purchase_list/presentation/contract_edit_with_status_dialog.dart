import 'package:flutter/material.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/features/purchase_list/presentation/create_contract.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class ContractEditWithStatusDialog extends StatefulWidget {
  const ContractEditWithStatusDialog({
    super.key,
    required this.contract,
    required this.projectId,
    required this.onContractUpdated,
  });

  final ContractModel contract;
  final String projectId;
  final VoidCallback onContractUpdated;

  @override
  State<ContractEditWithStatusDialog> createState() =>
      _ContractEditWithStatusDialogState();
}

class _ContractEditWithStatusDialogState
    extends State<ContractEditWithStatusDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Status change form
  final _statusFormKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  ContractAction? _selectedAction;
  bool _isStatusLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _changeStatus() async {
    if (!_statusFormKey.currentState!.validate()) {
      return;
    }

    if (_selectedAction == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Выберите действие')),
      );
      return;
    }

    setState(() {
      _isStatusLoading = true;
    });

    try {
      final statusChangeData = ProvisionChangeContractStatusInput(
        contractId: widget.contract.id,
        action: _selectedAction,
        reason: _reasonController.text.trim().isNotEmpty
            ? _reasonController.text.trim()
            : null,
      );

      final response = await PurchaseListRepositoryV2.changeContractStatus(
        statusChangeData,
      );

      if (mounted) {
        if (response.statusCode == 200 || response.statusCode == 201) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.data?.message ??
                  'Статус контракта успешно изменен!'),
            ),
          );
          widget.onContractUpdated();
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Ошибка сервера: ${response.statusMessage ?? 'Неизвестная ошибка'}',
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ошибка: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isStatusLoading = false;
        });
      }
    }
  }

  Widget _buildStatusChangeTab() {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _statusFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Contract info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkTheme
                    ? AppColors.darkBackground
                    : AppColors.lightBackground,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isDarkTheme
                      ? AppColors.darkDescription
                      : AppColors.lightDescription,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Информация о контракте',
                    style:
                        Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 8),
                  if (widget.contract.contractNumber != null) ...[
                    Text(
                      'Номер: ${widget.contract.contractNumber}',
                      style: Fonts.bodySmall,
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (widget.contract.supplier?.name != null) ...[
                    Text(
                      'Поставщик: ${widget.contract.supplier!.name}',
                      style: Fonts.bodySmall,
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (widget.contract.contractPrice != null)
                    Text(
                      'Сумма: ${widget.contract.contractPrice!.toStringAsFixed(2)} ₽',
                      style: Fonts.bodySmall,
                    ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Action selection
            Text(
              'Действие',
              style: Fonts.bodyMedium.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),

            ...ContractAction.values
                .map((action) => RadioListTile<ContractAction>(
                      title: Text(_getActionDisplayName(action),
                          style: Fonts.bodyMedium),
                      subtitle: Text(_getActionDescription(action),
                          style: Fonts.bodySmall),
                      value: action,
                      groupValue: _selectedAction,
                      onChanged: (value) {
                        setState(() {
                          _selectedAction = value;
                        });
                      },
                      contentPadding: EdgeInsets.zero,
                    )),

            const SizedBox(height: 16),

            // Reason field
            TextFormField(
              controller: _reasonController,
              decoration: InputDecoration(
                labelText: _selectedAction == ContractAction.cancel
                    ? 'Причина (обязательно)'
                    : 'Причина (опционально)',
                border: const OutlineInputBorder(),
                hintText: 'Укажите причину изменения статуса',
              ),
              style: Fonts.bodyMedium,
              maxLines: 3,
              minLines: 1,
              validator: (value) {
                if (_selectedAction == ContractAction.cancel &&
                    (value == null || value.trim().isEmpty)) {
                  return 'Причина обязательна для отмены контракта';
                }
                return null;
              },
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Отмена',
                    style: Fonts.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isStatusLoading
                        ? null
                        : () {
                            _changeStatus();
                          },
                    child: Text(
                        _isStatusLoading ? 'Изменение...' : 'Изменить статус'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getActionDisplayName(ContractAction action) {
    switch (action) {
      case ContractAction.confirm:
        return 'Подтвердить контракт';
      case ContractAction.revert:
        return 'Вернуть в черновик';
      case ContractAction.cancel:
        return 'Отменить контракт';
    }
  }

  String _getActionDescription(ContractAction action) {
    switch (action) {
      case ContractAction.confirm:
        return 'Подтверждение контракта и создание поставок';
      case ContractAction.revert:
        return 'Возврат в черновик (отмена поставок)';
      case ContractAction.cancel:
        return 'Полная отмена контракта';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.edit_document,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Редактирование контракта',
                  style: Fonts.headlineSmall,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
        ),

        // Tabs
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Редактировать'),
            Tab(text: 'Изменить статус'),
          ],
        ),

        // Tab content
        Flexible(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Edit contract tab
              CreateContractBody(
                projectId: widget.projectId,
                selectedProductIds: widget.contract.productIds ?? [],
                existingContract: widget.contract,
                onContractCreated: () {
                  widget.onContractUpdated();
                  Navigator.of(context).pop();
                },
              ),

              // Change status tab
              _buildStatusChangeTab(),
            ],
          ),
        ),
      ],
    );
  }
}
