import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/features/purchase_list/domain/column.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class ColumnVisibilityDialog extends StatefulWidget {
  const ColumnVisibilityDialog({super.key});

  @override
  State<ColumnVisibilityDialog> createState() => _ColumnVisibilityDialogState();
}

class _ColumnVisibilityDialogState extends State<ColumnVisibilityDialog> {
  late Map<String, bool> _columnVisibility;

  @override
  void initState() {
    super.initState();
    final currentVisibility =
        context.read<BlocPurchaseList>().state.columnVisibility;
    _columnVisibility = Map<String, bool>.from(currentVisibility);
  }

  void _toggleColumn(String index) {
    // Не позволяем скрывать столбец "Название" (индекс 2)
    if (index == '2') return;

    setState(() {
      _columnVisibility[index] = !(_columnVisibility[index] ?? true);
    });
  }

  void _showAll() {
    setState(() {
      for (var key in _columnVisibility.keys) {
        _columnVisibility[key] = true;
      }
    });
  }

  void _hideAll() {
    setState(() {
      for (var key in _columnVisibility.keys) {
        // Оставляем столбец "Название" (индекс 2) всегда видимым
        _columnVisibility[key] = key == '2' ? true : false;
      }
    });
  }

  void _applyChanges() {
    context.read<BlocPurchaseList>().add(
          UpdateColumnVisibility(_columnVisibility),
        );
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Заголовок
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Настройка отображения столбцов',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Кнопки управления
        Row(
          children: [
            GhostButton(
              onTap: _showAll,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: const Text('Показать все'),
              ),
            ),
            const SizedBox(width: 12),
            GhostButton(
              onTap: _hideAll,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: const Text('Скрыть все'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Список столбцов
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.separated(
              padding: const EdgeInsets.all(12),
              itemCount: PurchaseListTableData.columns.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final column = PurchaseListTableData.columns[index];
                final isVisible = _columnVisibility[index.toString()] ?? true;

                final isNameColumn = index == 2; // Столбец "Название"

                return InkWell(
                  onTap: isNameColumn
                      ? null
                      : () => _toggleColumn(index.toString()),
                  borderRadius: BorderRadius.circular(4),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 12,
                    ),
                    child: Row(
                      children: [
                        Checkbox(
                          value: isVisible,
                          onChanged: isNameColumn
                              ? null
                              : (_) => _toggleColumn(index.toString()),
                          activeColor: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    column.name ?? '',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.copyWith(
                                          color: isNameColumn
                                              ? (isDarkTheme
                                                  ? AppColors.darkDescription
                                                  : AppColors.lightDescription)
                                              : null,
                                        ),
                                  ),
                                  if (isNameColumn) ...[
                                    const SizedBox(width: 8),
                                    Text(
                                      '(обязательный)',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: isDarkTheme
                                                ? AppColors.darkDescription
                                                : AppColors.lightDescription,
                                            fontStyle: FontStyle.italic,
                                          ),
                                    ),
                                  ],
                                ],
                              ),
                              if (column.description != null) ...[
                                const SizedBox(height: 4),
                                Text(
                                  column.description!,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: isDarkTheme
                                            ? AppColors.darkDescription
                                            : AppColors.lightDescription,
                                      ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Кнопка применения
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            GhostButton(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: const Text('Отмена'),
              ),
            ),
            const SizedBox(width: 12),
            CustomElevatedButton(
              text: 'Применить',
              onPressed: _applyChanges,
            ),
          ],
        ),
      ],
    );
  }
}

void showColumnVisibilityDialog(BuildContext context) {
  showBaseDialog(
    context,
    builder: (context) => const ColumnVisibilityDialog(),
    maxWidth: 600,
  );
}
