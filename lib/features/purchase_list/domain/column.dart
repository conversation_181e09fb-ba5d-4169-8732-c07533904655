import 'package:freezed_annotation/freezed_annotation.dart';

part 'column.freezed.dart';

@freezed
abstract class TableColumnModel with _$TableColumnModel {
  factory TableColumnModel({
    String? name,
    String? description,
    double? width,
  }) = _TableColumnModel;
}

class PurchaseListTableData {
  static final List<TableColumnModel> columns = [
    // --- 1
    // 1
    TableColumnModel(
      name: 'Дата ввода',
      description: 'Дата ввода позиции',
      width: 200,
    ),
    // 2
    TableColumnModel(
      name: 'Номер чертежа',
      description: 'Номер чертежа (обозначение)',
      width: 300,
    ),
    // 3
    TableColumnModel(
      name: 'Название',
      description: 'Наименование позиции',
      width: 300,
    ),
    // 4
    TableColumnModel(
      name: 'Материал',
      description: 'Наименование материала',
      width: 300,
    ),
    // 5
    TableColumnModel(
      name: 'Наш материал',
      description: 'Наш материал, закупка с нашей стороны',
      width: 200,
    ),
    // 6
    TableColumnModel(
      name: 'Признак',
      description: 'Признаки',
      width: 200,
    ),
    // 7
    TableColumnModel(
      name: 'Масса ед., кг',
      description: 'Масса одной штуки в кг',
      width: 200,
    ),
    // 8
    TableColumnModel(
      name: 'Количество',
      // description: 'Масса одной штуки в кг',
      width: 200,
    ),
    // 9
    TableColumnModel(
      name: 'Масса, кг',
      description: 'Масса общая в кг',
      width: 200,
    ),
    // 10
    TableColumnModel(
      name: 'Примечание',
      description: 'Примечание / Требования',
      width: 300,
    ),
    // 11
    TableColumnModel(
      name: 'Доп. тр. к материалу',
      description: 'Дополнительное требование к материалу',
      width: 300,
    ),
    // 12
    TableColumnModel(
      name: 'Узел',
      // description: 'Дополнительное требование к материалу',
      width: 300,
    ),
    // 13
    TableColumnModel(
      name: 'Номер сборки',
      description: 'Номер чертежа род. сборки',
      width: 300,
    ),
    // 14
    TableColumnModel(
      name: 'Приоритет',
      description: 'Приоритет позиции',
      width: 200,
    ),
    // --- 2
    // 15
    TableColumnModel(
      name: 'Ответственный',
      description: 'Ответственный по этой позиции',
      width: 300,
    ),
    // 16
    TableColumnModel(
      name: 'Дата задачи',
      // description: 'Приоритет позиции',
      width: 200,
    ),
    // 17
    TableColumnModel(
      name: 'Статус задачи',
      description: 'Статус выполнения задачи',
      width: 200,
    ),
    // 18
    TableColumnModel(
      name: 'Дедлайн задачи',
      description: 'Плановая дата завершения задачи',
      width: 200,
    ),
    // --- 3
    // 19
    TableColumnModel(
      name: 'Статус закупа',
      description: 'Статус закупки позиции',
      width: 300,
    ),
    // 20
    TableColumnModel(
      name: 'Плановая дата',
      description: 'Плановая дата завершения тендера',
      width: 300,
    ),
    // 21
    TableColumnModel(
      name: 'Номер лота',
      // description: 'Плановая дата завершения тендера',
      width: 300,
    ),
    // 22
    TableColumnModel(
      name: 'Название лота',
      // description: 'Плановая дата завершения тендера',
      width: 300,
    ),
    // --- 4
    // 23
    TableColumnModel(
      name: 'Номер заказа',
      // description: 'Плановая дата завершения тендера',
      width: 300,
    ),
    // 24
    TableColumnModel(
      name: 'Поставщик',
      // description: 'Приоритет позиции',
      width: 300,
    ),
    // 25
    TableColumnModel(
      name: 'Дата поставки',
      // description: 'Приоритет позиции',
      width: 300,
    ),
    // 26
    TableColumnModel(
      name: '№ Договора|Спецификации|счета',
      description: '№ Договора или Спецификации или счета',
      width: 300,
    ),
    // 27
    TableColumnModel(
      name: 'Стоимость',
      // description: '№ Договора или Спецификации или счета',
      width: 300,
    ),
    // 28
    TableColumnModel(
      name: 'Примечание',
      // description: 'Приоритет позиции',
      width: 300,
    ),
    // --- 5
    // 29
    TableColumnModel(
      name: 'ФИО Кладовщика',
      // description: 'Приоритет позиции',
      width: 300,
    ),
    // 30
    TableColumnModel(
      name: 'Количество',
      description: 'Количество на складе',
      width: 300,
    ),
    // 31
    TableColumnModel(
      name: 'Дата поступления',
      // description: 'Приоритет позиции',
      width: 300,
    ),
    // --- 6
    // 32
    TableColumnModel(
      name: 'Сертификаты',
      // description: 'Приоритет позиции',
      width: 300,
    ),
    // 33
    TableColumnModel(
      name: 'Акт на брак',
      // description: 'Приоритет позиции',
      width: 300,
    ),
    // // --- 7 Task columns
    // // 31
    // TableColumnModel(
    //   name: 'Задачи',
    //   description: 'Количество задач по статусам',
    //   width: 200,
    // ),
    // // 32
    // TableColumnModel(
    //   name: 'Исполнители',
    //   description: 'Исполнители задач',
    //   width: 300,
    // ),
    // // 33
    // TableColumnModel(
    //   name: 'Статус задач',
    //   description: 'Статусы задач',
    //   width: 200,
    // ),
    // // 34
    // TableColumnModel(
    //   name: 'Дедлайн задач',
    //   description: 'Крайние сроки выполнения задач',
    //   width: 200,
    // ),
  ];
}
