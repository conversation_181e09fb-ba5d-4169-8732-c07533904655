// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'materials.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ContractItemModel {
  String? get materialId => throw _privateConstructorUsedError;
  String? get materialRequirements => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  ParametersFeatureType? get featureType => throw _privateConstructorUsedError;
  bool? get needMaterial => throw _privateConstructorUsedError;
  TextEditingController get quantityController =>
      throw _privateConstructorUsedError;
  TextEditingController get costController =>
      throw _privateConstructorUsedError;

  /// Create a copy of ContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContractItemModelCopyWith<ContractItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContractItemModelCopyWith<$Res> {
  factory $ContractItemModelCopyWith(
          ContractItemModel value, $Res Function(ContractItemModel) then) =
      _$ContractItemModelCopyWithImpl<$Res, ContractItemModel>;
  @useResult
  $Res call(
      {String? materialId,
      String? materialRequirements,
      String? productId,
      ParametersFeatureType? featureType,
      bool? needMaterial,
      TextEditingController quantityController,
      TextEditingController costController});
}

/// @nodoc
class _$ContractItemModelCopyWithImpl<$Res, $Val extends ContractItemModel>
    implements $ContractItemModelCopyWith<$Res> {
  _$ContractItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? materialRequirements = freezed,
    Object? productId = freezed,
    Object? featureType = freezed,
    Object? needMaterial = freezed,
    Object? quantityController = null,
    Object? costController = null,
  }) {
    return _then(_value.copyWith(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      needMaterial: freezed == needMaterial
          ? _value.needMaterial
          : needMaterial // ignore: cast_nullable_to_non_nullable
              as bool?,
      quantityController: null == quantityController
          ? _value.quantityController
          : quantityController // ignore: cast_nullable_to_non_nullable
              as TextEditingController,
      costController: null == costController
          ? _value.costController
          : costController // ignore: cast_nullable_to_non_nullable
              as TextEditingController,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContractItemModelImplCopyWith<$Res>
    implements $ContractItemModelCopyWith<$Res> {
  factory _$$ContractItemModelImplCopyWith(_$ContractItemModelImpl value,
          $Res Function(_$ContractItemModelImpl) then) =
      __$$ContractItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? materialId,
      String? materialRequirements,
      String? productId,
      ParametersFeatureType? featureType,
      bool? needMaterial,
      TextEditingController quantityController,
      TextEditingController costController});
}

/// @nodoc
class __$$ContractItemModelImplCopyWithImpl<$Res>
    extends _$ContractItemModelCopyWithImpl<$Res, _$ContractItemModelImpl>
    implements _$$ContractItemModelImplCopyWith<$Res> {
  __$$ContractItemModelImplCopyWithImpl(_$ContractItemModelImpl _value,
      $Res Function(_$ContractItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? materialRequirements = freezed,
    Object? productId = freezed,
    Object? featureType = freezed,
    Object? needMaterial = freezed,
    Object? quantityController = null,
    Object? costController = null,
  }) {
    return _then(_$ContractItemModelImpl(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      featureType: freezed == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      needMaterial: freezed == needMaterial
          ? _value.needMaterial
          : needMaterial // ignore: cast_nullable_to_non_nullable
              as bool?,
      quantityController: null == quantityController
          ? _value.quantityController
          : quantityController // ignore: cast_nullable_to_non_nullable
              as TextEditingController,
      costController: null == costController
          ? _value.costController
          : costController // ignore: cast_nullable_to_non_nullable
              as TextEditingController,
    ));
  }
}

/// @nodoc

class _$ContractItemModelImpl implements _ContractItemModel {
  const _$ContractItemModelImpl(
      {this.materialId,
      this.materialRequirements,
      this.productId,
      this.featureType,
      this.needMaterial,
      required this.quantityController,
      required this.costController});

  @override
  final String? materialId;
  @override
  final String? materialRequirements;
  @override
  final String? productId;
  @override
  final ParametersFeatureType? featureType;
  @override
  final bool? needMaterial;
  @override
  final TextEditingController quantityController;
  @override
  final TextEditingController costController;

  @override
  String toString() {
    return 'ContractItemModel(materialId: $materialId, materialRequirements: $materialRequirements, productId: $productId, featureType: $featureType, needMaterial: $needMaterial, quantityController: $quantityController, costController: $costController)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContractItemModelImpl &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.materialRequirements, materialRequirements) ||
                other.materialRequirements == materialRequirements) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.featureType, featureType) ||
                other.featureType == featureType) &&
            (identical(other.needMaterial, needMaterial) ||
                other.needMaterial == needMaterial) &&
            (identical(other.quantityController, quantityController) ||
                other.quantityController == quantityController) &&
            (identical(other.costController, costController) ||
                other.costController == costController));
  }

  @override
  int get hashCode => Object.hash(runtimeType, materialId, materialRequirements,
      productId, featureType, needMaterial, quantityController, costController);

  /// Create a copy of ContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContractItemModelImplCopyWith<_$ContractItemModelImpl> get copyWith =>
      __$$ContractItemModelImplCopyWithImpl<_$ContractItemModelImpl>(
          this, _$identity);
}

abstract class _ContractItemModel implements ContractItemModel {
  const factory _ContractItemModel(
          {final String? materialId,
          final String? materialRequirements,
          final String? productId,
          final ParametersFeatureType? featureType,
          final bool? needMaterial,
          required final TextEditingController quantityController,
          required final TextEditingController costController}) =
      _$ContractItemModelImpl;

  @override
  String? get materialId;
  @override
  String? get materialRequirements;
  @override
  String? get productId;
  @override
  ParametersFeatureType? get featureType;
  @override
  bool? get needMaterial;
  @override
  TextEditingController get quantityController;
  @override
  TextEditingController get costController;

  /// Create a copy of ContractItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContractItemModelImplCopyWith<_$ContractItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
