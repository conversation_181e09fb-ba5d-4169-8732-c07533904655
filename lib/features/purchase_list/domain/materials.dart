import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/project/data/models/parameters.dart';

part 'materials.freezed.dart';

@freezed
class ContractItemModel with _$ContractItemModel {
  const factory ContractItemModel({
    String? materialId,
    String? materialRequirements,
    String? productId,
    ParametersFeatureType? featureType,
    bool? needMaterial,
    required TextEditingController quantityController,
    required TextEditingController costController,
  }) = _ContractItemModel;

  // factory ContractItemModel.fromJson(Map<String, dynamic> json) =>
  //     _$ContractItemModelFromJson(json);
}
