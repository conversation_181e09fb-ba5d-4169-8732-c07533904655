import 'package:flutter/material.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/models/view.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class ProjectEditParameters extends StatefulWidget {
  const ProjectEditParameters({super.key, this.project, this.onSave});

  final ProjectModel? project;
  final void Function(ProjectViewModel)? onSave;

  @override
  State<ProjectEditParameters> createState() => _ProjectEditParametersState();
}

class _ProjectEditParametersState extends State<ProjectEditParameters> {
  final _massController = TextEditingController();
  final _widthController = TextEditingController();
  final _lengthController = TextEditingController();
  final _heightController = TextEditingController();
  final _quantityController = TextEditingController();
  ProjectViewModel _project = ProjectViewModel();

  bool _isLoading = false;

  Future<void> _save() async {
    if (widget.project?.id == null) return;
    setState(() {
      _isLoading = true;
    });

    final result = await ProjectRepository.updateParameters(
      widget.project!.id!,
      ParametersModel(
        mass: double.tryParse(_massController.text),
        width: double.tryParse(_widthController.text),
        length: double.tryParse(_lengthController.text),
        height: double.tryParse(_heightController.text),
        quantity:
            double.tryParse(_quantityController.text.replaceAll(r',', '.')),
      ),
    );

    setState(() {
      _project = result?.data ?? ProjectViewModel();
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    final parameters = widget.project!.parameters;
    if (parameters != null) {
      _massController.text =
          parameters.mass != null ? parameters.mass.toString() : '';
      _widthController.text =
          parameters.width != null ? parameters.width.toString() : '';
      _lengthController.text =
          parameters.length != null ? parameters.length.toString() : '';
      _heightController.text =
          parameters.height != null ? parameters.height.toString() : '';
      _quantityController.text =
          parameters.quantity != null ? parameters.quantity.toString() : '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0.0),
            itemCount: 13, // Fixed number of form items
            itemBuilder: (context, index) {
              switch (index) {
                case 0:
                  return Text(
                    'Редактирование параметров',
                    style: Fonts.titleMedium.merge(
                      const TextStyle(height: 1.6),
                    ),
                  );
                case 1:
                  return const SizedBox(height: 10.0);
                case 2:
                  return Text(
                    'Масса изделия',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 3:
                  return const SizedBox(height: 10.0);
                case 4:
                  return Row(children: [
                    Expanded(
                      child: TextField(
                        controller: _massController,
                        style: Fonts.labelSmall,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          hintText: 'Масса',
                        ),
                      ),
                    ),
                    const SizedBox(width: 12.0),
                    Text(
                      'кг',
                      style: Fonts.bodyMedium.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        ),
                      ),
                    ),
                  ]);
                case 5:
                  return const SizedBox(height: 24.0);
                case 6:
                  return Text(
                    'Ширина изделия',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 7:
                  return const SizedBox(height: 10.0);
                case 8:
                  return Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _widthController,
                          style: Fonts.labelSmall,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            hintText: 'Ширина',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12.0),
                      Text(
                        'мм',
                        style: Fonts.bodyMedium.merge(
                          TextStyle(
                            color: isDarkTheme
                                ? AppColors.darkDescription
                                : AppColors.lightDescription,
                          ),
                        ),
                      ),
                    ],
                  );
                case 9:
                  return const SizedBox(height: 24.0);
                case 10:
                  return Text(
                    'Длина изделия',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 11:
                  return const SizedBox(height: 10.0);
                case 12:
                  return Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _lengthController,
                          style: Fonts.labelSmall,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            hintText: 'Длина',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12.0),
                      Text(
                        'мм',
                        style: Fonts.bodyMedium.merge(
                          TextStyle(
                            color: isDarkTheme
                                ? AppColors.darkDescription
                                : AppColors.lightDescription,
                          ),
                        ),
                      ),
                    ],
                  );
              }
              return const SizedBox.shrink();
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () async {
              await _save();
              widget.onSave?.call(_project);
            },
            text: 'Сохранить',
          ),
        ),
      ]),
    ]);
  }
}
