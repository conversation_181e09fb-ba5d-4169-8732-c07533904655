import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/pick_folder.dart';
import 'package:sphere/core/helpers/zip_folder.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/files/data/repositories/index.dart';
import 'package:sphere/features/files/presentation/index.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/project/data/models/view.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/features/project/presentation/add_workers.dart';
import 'package:sphere/features/project/presentation/edit.dart';
import 'package:sphere/features/project/presentation/edit_parameters.dart';
import 'package:sphere/features/project/presentation/remove_workers.dart';
import 'package:sphere/features/project/presentation/view_workers.dart';
import 'package:sphere/features/tasks/presentation/add.dart';
import 'package:sphere/features/tasks/presentation/progress_task.dart';
import 'package:sphere/features/user/presentation/users_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/detailed.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/utility/info_list.dart';
import 'package:sphere/shared/widgets/utility/skeleton.dart';

@RoutePage()
class ProjectScreen extends StatefulWidget {
  const ProjectScreen({
    super.key,
    @PathParam('id') this.id,
  });

  final String? id;

  @override
  State<ProjectScreen> createState() => _ProjectScreenState();
}

class _ProjectScreenState extends State<ProjectScreen>
    with TickerProviderStateMixin {
  ProjectViewModel _view = ProjectViewModel();
  bool _isLoading = false;

  void _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final result = await ProjectRepository.view(widget.id ?? '');
    // await Future.delayed(const Duration(milliseconds: 20000));

    setState(() {
      _view = result?.data ?? ProjectViewModel();
      _isLoading = false;
    });
  }

  Future<File?> _pickFolder() async {
    final folderPath = await pickFolder();
    if (folderPath == null) return null;
    final files = await scanFolder(folderPath);
    final archive = await zipFolder(files, folderPath);
    return archive;
  }

  void _handleImport() async {
    if (_view.id == null) return;
    setState(() {
      _isLoading = true;
    });
    final file = await _pickFolder();
    if (file != null) {
      ProjectRepository.importStructure(_view.id!, file);
    }
    setState(() {
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 900;
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    Widget buildInfo() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12.0),
          Row(
            children: [
              Expanded(
                child: Skeleton(
                  isEnabled: _isLoading,
                  child: Text(
                    _view.name ?? 'NAME_OF_PROJECT',
                    style: Fonts.titleLarge,
                  ),
                ),
              ),
            ],
          ),
          // if (_isLoading) const SizedBox(height: 8.0),
          // Row(
          //   children: [
          //     Expanded(
          //       child: Skeleton(
          //         isEnabled: _isLoading,
          //         child: Text(
          //           _view.status?.getName() ?? 'STATUS_OF_PROJECT',
          //           style: Fonts.bodyMedium.merge(
          //             const TextStyle(color: AppColors.lightSecondary),
          //           ),
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
          if (_isLoading && _view.description != null)
            const SizedBox(height: 8.0),
          if (_view.description != null)
            Row(
              children: [
                Expanded(
                  child: Skeleton(
                    isEnabled: _isLoading,
                    child: Text(
                      _view.description ?? 'DESCRIPTION_OF_PROJECT',
                      style: Fonts.bodySmall.merge(
                        const TextStyle(color: AppColors.medium),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          const SizedBox(height: 24.0),
          InfoList(isLoading: _isLoading, project: _view.toProjectModel()),
          const SizedBox(height: 24.0),
          const Divider(),
          const SizedBox(height: 24.0),
          // Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
          //   InfoBlock(
          //     icon: Assets.icons.weight,
          //     text: '${view.parameters?.mass ?? '?'} кг',
          //   ),
          //   InfoBlock(
          //     icon: Assets.icons.fitHeigth,
          //     text: '${view.parameters?.length ?? '?'} мм',
          //   ),
          //   InfoBlock(
          //     icon: Assets.icons.fitWidth,
          //     text: '${view.parameters?.width ?? '?'} мм',
          //   ),
          // ]),
          if (_view.blueprint?.preview?.url != null)
            Row(children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return Dialog(
                          backgroundColor: Colors.black,
                          insetPadding: const EdgeInsets.all(10),
                          child: InteractiveViewer(
                            clipBehavior: Clip.none,
                            child: Image.network(
                              FilesRepository.getPath(
                                _view.blueprint!.preview!.url!,
                              ),
                              fit: BoxFit.contain,
                            ),
                          ),
                        );
                      },
                    );
                  },
                  child: Container(
                    clipBehavior: Clip.hardEdge,
                    decoration: BoxDecoration(
                      // borderRadius: BorderRadius.circular(20.0),
                      border: Border.all(
                        strokeAlign: BorderSide.strokeAlignOutside,
                        color: isDarkTheme
                            ? AppColors.darkStroke
                            : AppColors.lightStroke,
                      ),
                    ),
                    child: Image.network(
                      FilesRepository.getPath(
                        _view.blueprint!.preview!.url!,
                      ),
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ]),
        ],
      );
    }

    List<Widget> buildDetailCards() {
      return [
        DetailedCard(
          isLoading: _isLoading,
          onTap: () {
            context.router.pushNamed(
              '/project/${widget.id}/structure',
            );
          },
          title: 'Структура',
          description: 'Структура проекта: “${_view.name}”',
          icon: Assets.icons.widgets,
          count: _view.productsCount,
          height: 200.0,
        ),
        DetailedCard(
          isLoading: _isLoading,
          onTap: () {
            if (widget.id == null) return;
            context.router.push(PurchaseListRoute(
              id: widget.id!,
              projectName: _view.name ?? '',
            ));
          },
          title: 'Закупки',
          description:
              'Список материалов и деталей к закупке для проекта: “${_view.name}”',
          // icon: Assets.icons.sell,
          // count: 0,
          height: 200.0,
        ),
        DetailedCard(
          isLoading: _isLoading,
          onTap: () {
            if (widget.id == null) return;
            context.router.push(
              InnerStorageRoute(
                id: widget.id!,
                name: _view.name,
              ),
            );
          },
          title: 'Склад',
          description: 'Склад проекта: “${_view.name}”',
          // icon: Assets.icons.widgets,
          // count: 22,
          height: 200.0,
        ),
        DetailedCard(
          isLoading: _isLoading,
          onTap: () {
            context.router.push(ProductionRoute(
              projectId: widget.id,
              projectName: _view.name,
            ));
          },
          title: 'Производство',
          // description: 'Структура проекта: “${_view.name}”',
          // icon: Assets.icons.widgets,
          // count: _view.productsCount,
          height: 200.0,
        ),
        DetailedCard(
          isLoading: _isLoading,
          onTap: () {
            context.router.push(FilesRoute(
              key: UniqueKey(),
              id: widget.id,
              idType: FileScreenTypeId.fromProject.toJson(),
              isRoot: true,
              projectName: _view.name,
            ));
          },
          title: 'Документы',
          description:
              'Папки с документами, относящиеся к проекту: “${_view.name}”',
          icon: Assets.icons.description,
          count: _view.documentsCount,
          height: 200.0,
        ),
      ];
    }

    final detailCards = buildDetailCards();

    return Wrapper(
      appBar: CustomAppBar(
        title: _view.name ?? 'NAME_OF_PROJECT',
        isLoading: _isLoading,
        height: 48.0,
        rightPart: CustomAppBarFeatures.getPopupMenu(
          context: context,
          children: [
            CustomDropdownMenuItem(
              icon: Assets.icons.import,
              text: 'Импортировать проект',
              onTap: () {
                CustomDropdownMenu.instance.hide();
                _handleImport();
                // _showImportPopup();
              },
            ),
            CustomDropdownMenuItem(
              icon: Assets.icons.edit,
              text: 'Редактировать',
              onTap: () {
                CustomDrawer.instance.show(
                  context: context,
                  vsync: this,
                  child: ProjectEdit(
                    project: _view.toProjectModel(),
                    onSave: (project) {
                      setState(() {
                        _view = project;
                      });
                      CustomDrawer.instance.hide();
                    },
                  ),
                );
              },
            ),
            CustomDropdownMenuItem(
              // disabled: true,
              icon: Assets.icons.edit,
              text: 'Редактировать параметры',
              onTap: () {
                CustomDrawer.instance.show(
                  context: context,
                  vsync: this,
                  child: ProjectEditParameters(
                    project: _view.toProjectModel(),
                    onSave: (project) {
                      setState(() {
                        _view = project;
                      });
                      CustomDrawer.instance.hide();
                    },
                  ),
                );
              },
            ),
            CustomDropdownMenuItem(
              disabled: true,
              icon: Assets.icons.description,
              text: 'Согласовать',
              onTap: () {},
            ),
            CustomDropdownMenuItem(
              disabled: true,
              onTap: () {},
              icon: Assets.icons.notifications,
              text: 'Подписаться на изменения',
            ),
            CustomDropdownMenuItem(
              icon: Assets.icons.personAdd,
              text: 'Добавить задачу',
              onTap: () {
                CustomDropdownMenu.instance.hide();
                CustomDrawer.instance.show(
                  context: context,
                  vsync: this,
                  child: AddTaskDrawer(
                    projectId: widget.id,
                    onSuccess: _loadData,
                    departments: [Department.ogk],
                  ),
                );
              },
            ),
          ],
        ),
      ),
      body: Row(children: [
        Expanded(
          child: ListView(
              padding: const EdgeInsets.all(12.0),
              clipBehavior: Clip.none,
              children: [
                // ConstrainedBox(
                //   constraints: const BoxConstraints(maxHeight: 200),
                //   child: CustomCard(
                //     isLoading: _isLoading,
                //     child: const Center(
                //       child: Text(
                //         'Статистика / Аналитика',
                //         style: Fonts.bodyMedium,
                //       ),
                //     ),
                //   ),
                // ),
                if (isMobile) buildInfo(),
                if (isMobile) const SizedBox(height: 32.0),
                if (!isMobile)
                  Row(
                    spacing: 12.0,
                    children: List.generate(detailCards.length, (index) {
                      final detailCard = detailCards[index];
                      return Expanded(child: detailCard);
                    }),
                  ),
                if (isMobile)
                  Column(
                    spacing: 12.0,
                    children: List.generate(detailCards.length, (index) {
                      final detailCard = detailCards[index];
                      return Row(children: [Expanded(child: detailCard)]);
                    }),
                  ),
                const SizedBox(height: 12.0),
                UsersCard(
                  onTap: () {
                    CustomDrawer.instance.show(
                      context: context,
                      vsync: this,
                      child: ProjectViewWorkersDrawer(
                        projectId: widget.id,
                      ),
                    );
                  },
                  isLoading: _isLoading,
                  onSecondaryTapDown: (details) {
                    CustomDropdownMenu.instance.hide();
                    CustomDropdownMenu.instance.show(
                      context: context,
                      items: [
                        CustomDropdownMenuItem(
                          icon: Assets.icons.personRemove,
                          text: 'Убрать исполнителей',
                          onTap: () {
                            CustomDrawer.instance.show(
                              context: context,
                              vsync: this,
                              child: ProjectRemoveWorkersDrawer(
                                projectId: widget.id,
                                onSuccess: (project) {
                                  setState(() {
                                    _view = project;
                                  });
                                  CustomDrawer.instance.hide();
                                },
                              ),
                            );
                            CustomDropdownMenu.instance.hide();
                          },
                        ),
                        CustomDropdownMenuItem(
                          icon: Assets.icons.personAdd,
                          text: 'Добавить исполнителей',
                          onTap: () {
                            CustomDrawer.instance.show(
                              context: context,
                              vsync: this,
                              child: ProjectAddWorkersDrawer(
                                projectId: widget.id,
                                project: _view.toProjectModel(),
                                onSuccess: (project) {
                                  setState(() {
                                    _view = project;
                                  });
                                  CustomDrawer.instance.hide();
                                },
                              ),
                            );
                            CustomDropdownMenu.instance.hide();
                          },
                        ),
                      ],
                      position: details.globalPosition,
                    );
                  },
                  users: [
                    if (_view.headUser != null) _view.headUser!,
                    ...(_view.managers ?? []),
                  ],
                ),
                if (_view.progressTask != null) const SizedBox(height: 12.0),
                if (_view.progressTask != null)
                  ProgressTask(
                    isLoading: _isLoading,
                    progressTask: _view.progressTask!,
                    refresher: _loadData,
                    isSingle: true,
                  ),
                // Text(_view.progressTask.toString()),
                // const SizedBox(height: 12.0),
                // TechnologicalMapCard(
                //   isLoading: _isLoading,
                //   onTap: () {
                //     CustomDrawer.instance.show(
                //       context: context,
                //       vsync: this,
                //       child: ProjectEditOperationsBody(
                //         project: _view,
                //         refresher: _loadData,
                //       ),
                //     );
                //   },
                //   operations: _view.operations ?? [],
                // ),
              ]),
        ),
        if (!isMobile)
          SizedBox(
            width: MediaQuery.of(context).size.width / 3.00,
            child: buildInfo(),
          ),
        if (!isMobile) const SizedBox(width: 12.0),
      ]),
    );
  }
}
