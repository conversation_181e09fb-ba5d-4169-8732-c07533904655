import 'package:flutter/material.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/client/data/repositories/index.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/models/view.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class ProjectEdit extends StatefulWidget {
  const ProjectEdit({super.key, this.project, this.onSave});

  final ProjectModel? project;
  final void Function(ProjectViewModel)? onSave;

  @override
  State<ProjectEdit> createState() => _ProjectEditState();
}

class _ProjectEditState extends State<ProjectEdit> {
  final _nameController = TextEditingController();
  final _numberController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _clientController = TextEditingController();
  ClientModel _selectedClient = const ClientModel();
  List<ClientModel> _clients = [];
  // String _headUserId = '';
  DateTime _releaseDate = DateTime.now();
  bool _calendarIsVisible = false;

  ProjectViewModel _project = ProjectViewModel();

  bool _isLoading = false;
  Future<void> _getClients() async {
    if (_clientController.text.isEmpty) return;
    setState(() {
      _isLoading = true;
    });
    final result = await ClientRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _clientController.text,
      ),
    ));

    if (result?.data != null && result!.data?.items != null) {
      final clients = result.data!.items!;
      setState(() {
        _clients = [
          ...clients,
        ];
        _isLoading = false;
      });
    }
  }

  void _onSelectClient(ClientModel client) {
    setState(() {
      _selectedClient = client;
      _clientController.text = client.name ?? '';
      _clients = [];
    });
    // widget.refresher?.call();
  }

  Future<void> _save() async {
    if (widget.project?.id == null) return;
    setState(() {
      _isLoading = true;
    });

    final result = await ProjectRepository.edit(
      projectId: widget.project!.id!,
      name: _nameController.text,
      number: _numberController.text,
      description: _descriptionController.text,

      clientId: _selectedClient.id,
      clientName: _selectedClient.id == null ? _clientController.text : null,
      releaseDate: _releaseDate,
      // headUserId: '',
    );

    setState(() {
      _project = result?.data ?? ProjectViewModel();
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    if (widget.project != null) {
      _nameController.text = widget.project!.name ?? '';
      _numberController.text = widget.project!.number ?? '';
      _descriptionController.text = widget.project!.description ?? '';
      _selectedClient = widget.project!.client ?? const ClientModel();
      _releaseDate = widget.project!.releaseDate ?? DateTime.now();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0.0),
            itemCount: _getFormItemCount(),
            itemBuilder: (context, index) {
              switch (index) {
                case 0:
                  return Text(
                    'Редактирование проекта',
                    style: Fonts.titleMedium.merge(
                      const TextStyle(height: 1.6),
                    ),
                  );
                case 1:
                  return const SizedBox(height: 10.0);
                case 2:
                  return Text(
                    'Название проекта',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 3:
                  return const SizedBox(height: 10.0);
                case 4:
                  return TextField(
                    controller: _nameController,
                    style: Fonts.labelSmall,
                    decoration: const InputDecoration(
                      hintText: 'Имя',
                    ),
                  );
                case 5:
                  return const SizedBox(height: 24.0);
                case 6:
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Реализация проекта:',
                        style: Fonts.labelSmall,
                      ),
                      GhostButton(
                        onTap: () async {
                          setState(() {
                            _calendarIsVisible = !_calendarIsVisible;
                          });
                        },
                        child: Text(
                          getDateString(_releaseDate),
                          style: Fonts.labelSmall.merge(
                            TextStyle(
                              color: isDarkTheme
                                  ? AppColors.darkSecondary
                                  : AppColors.lightSecondary,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                case 7:
                  if (_calendarIsVisible) return const SizedBox(height: 8.0);
                  break;
                case 8:
                  if (_calendarIsVisible) return const Divider(height: 1.0);
                  break;
                case 9:
                  if (_calendarIsVisible) {
                    return CalendarDatePicker(
                      initialDate: _releaseDate,
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(Duration(days: 730)),
                      currentDate: _releaseDate,
                      onDateChanged: (DateTime newDate) {
                        setState(() {
                          _releaseDate = newDate;
                          _calendarIsVisible = false;
                        });
                      },
                    );
                  }
                  break;
                case 10:
                  if (_calendarIsVisible) return const Divider(height: 1.0);
                  break;
                case 11:
                  return const SizedBox(height: 24.0);
                case 12:
                  return Text(
                    'Номер проекта',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 13:
                  return const SizedBox(height: 10.0);
                case 14:
                  return TextField(
                    controller: _numberController,
                    style: Fonts.labelSmall,
                    decoration: const InputDecoration(
                      hintText: 'Номер',
                    ),
                  );
                case 15:
                  return const SizedBox(height: 24.0);
                case 16:
                  return Text(
                    'Какой клиент?',
                    style: Fonts.bodyMedium.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 17:
                  return const SizedBox(height: 10.0);
                case 18:
                  return TextField(
                    controller: _clientController,
                    style: Fonts.labelSmall,
                    onChanged: (value) {
                      setState(() {
                        _selectedClient =
                            widget.project?.client ?? const ClientModel();
                      });
                      _getClients();
                    },
                    decoration: const InputDecoration(
                      hintText: 'Имя клиента',
                    ),
                  );
                case 19:
                  if (_clients.isNotEmpty || _selectedClient.name != null) {
                    return const SizedBox(height: 10.0);
                  }
                  break;
                case 20:
                  return Row(children: [
                    CustomChip(
                      selected: true,
                      text: _selectedClient.name,
                      onTap: () {
                        setState(() {
                          _selectedClient =
                              widget.project?.client ?? const ClientModel();
                          _clientController.text = '';
                        });
                      },
                    ),
                  ]);
                case 21:
                  return const SizedBox(height: 8.0);
                case 22:
                  if (_clients.isNotEmpty) {
                    return Wrap(
                      spacing: 6.0,
                      runSpacing: 6.0,
                      children: _clients
                          .where((client) => client != _selectedClient)
                          .map((client) {
                        return CustomChip(
                          selected: _selectedClient == client,
                          text: client.name,
                          onTap: () => _onSelectClient(client),
                        );
                      }).toList(),
                    );
                  }
                  break;
                case 23:
                  return const SizedBox(height: 24.0);
                case 24:
                  return Text(
                    'Описание проекта',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 25:
                  return const SizedBox(height: 10.0);
                case 26:
                  return TextField(
                    controller: _descriptionController,
                    style: Fonts.labelSmall,
                    decoration: const InputDecoration(
                      hintText: 'Описание',
                    ),
                  );
              }
              return const SizedBox.shrink();
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () async {
              await _save();
              widget.onSave?.call(_project);
            },
            text: 'Сохранить',
          ),
        ),
      ]),
    ]);
  }

  /// Calculate total item count for ListView.builder
  int _getFormItemCount() {
    int count = 27; // Base form items

    // Adjust for conditional calendar items
    if (!_calendarIsVisible) {
      count -= 3; // Remove calendar spacing, divider, and calendar widget
    }

    // Adjust for conditional client items
    if (_clients.isEmpty && _selectedClient.name == null) {
      count -= 1; // Remove client spacing
    }

    if (_clients.isEmpty) {
      count -= 1; // Remove client chips
    }

    return count;
  }
}
