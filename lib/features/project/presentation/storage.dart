import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/repositories/index.dart';
import 'package:sphere/features/_initial/storage/presentation/edit_material.dart';
import 'package:sphere/features/_initial/storage/presentation/material_card.dart';
import 'package:sphere/features/_initial/storage/presentation/write_off_material.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class InnerStorageScreen extends StatefulWidget {
  const InnerStorageScreen({
    super.key,
    required this.id,
    this.name,
    this.isProject = true,
  });

  final String id;
  final String? name;
  final bool isProject;

  @override
  State<InnerStorageScreen> createState() => _InnerStorageScreenState();
}

class _InnerStorageScreenState extends State<InnerStorageScreen>
    with
        AutoRouteAwareStateMixin<InnerStorageScreen>,
        TickerProviderStateMixin {
  final _scrollController = ScrollController();
  bool _isLoading = false;
  List<MaterialModel> _materials = [];

  Future<void> _loadData({String searchQuery = ''}) async {
    setState(() {
      _isLoading = true;
    });

    final result = await StorageRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: searchQuery.isEmpty ? null : searchQuery,
        projectId: widget.id,
      ),
    ));
    final items = result?.data?.items;
    // await Future.delayed(const Duration(milliseconds: 20000));

    setState(() {
      _materials = items ?? [];
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 700;
    final isLarge = MediaQuery.of(context).size.width >= 1200;
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Wrapper(
      appBar: CustomAppBar(
        title: widget.name ?? 'Проект',
        description: 'Склад',
        // rightPart: CustomAppBarFeatures.getPopupMenu(
        //   context: context,
        //   children: [
        //     // CustomDropdownMenuItem(
        //     //   onTap: () {},
        //     //   icon: Assets.icons.add,
        //     //   text: 'Добавить материал',
        //     // ),
        //     // CustomDropdownMenuItem(
        //     //   onTap: () {},
        //     //   icon: Assets.icons.repeat,
        //     //   text: 'Обновить',
        //     // ),
        //     // CustomDropdownMenuItem(
        //     //   disabled: true,
        //     //   onTap: () {},
        //     //   icon: Assets.icons.notifications,
        //     //   text: 'Подписаться на изменения',
        //     // ),
        //   ],
        // ),
        isLoading: _isLoading,
        height: 48.0,
      ),
      body: Column(children: [
        Padding(
          padding: EdgeInsets.all(12),
          child: TextField(
            onChanged: (value) {
              _loadData(searchQuery: value);
            },
            style: Fonts.labelSmall,
            decoration: InputDecoration(
              hintText: 'Поиск',
              suffixIcon: Align(
                widthFactor: 1.0,
                alignment: Alignment.center,
                child: SVG(
                  Assets.icons.search,
                  color: AppColors.medium,
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: MasonryGridView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 32.0),
            gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: isDesktop
                  ? isLarge
                      ? 3
                      : 2
                  : 1,
            ),
            mainAxisSpacing: 12.0,
            crossAxisSpacing: 12.0,
            itemCount: _materials.length,
            itemBuilder: (context, index) {
              final material = _materials[index];

              return MaterialCard(
                isLoading: _isLoading,
                material: material,
                onSecondaryTapDown: (details) {
                  CustomDropdownMenu.instance.hide();
                  CustomDropdownMenu.instance.show(
                    context: context,
                    items: [
                      CustomDropdownMenuItem(
                        icon: Assets.icons.edit,
                        text: 'Редактировать',
                        description: 'Корректировка значений',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          CustomDrawer.instance.show(
                            context: context,
                            vsync: this,
                            child: EditMaterialBody(
                              material: material,
                              refresher: _loadData,
                            ),
                          );
                        },
                      ),
                      CustomDropdownMenuItem(
                        icon: Assets.icons.delete,
                        text: 'Списать',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          CustomDrawer.instance.show(
                            context: context,
                            vsync: this,
                            child: WriteOffMaterialBody(
                              material: material,
                              refresher: _loadData,
                            ),
                          );
                        },
                      ),
                    ],
                    position: details.globalPosition,
                  );
                },
                // onTap: () {
                //   // context.router.push(ProjectRoute(id: project.id ?? '0'));
                // },
              );
            },
          ),
        ),
      ]),
    );
  }
}
