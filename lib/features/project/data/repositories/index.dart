import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:sphere/features/project/data/models/index.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/models/view.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class ProjectRepository {
  static Future<Response<ProjectSearchOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProjectSearchOutputModel>(
      url: '/projects/search',
      body: body,
      method: 'POST',
      fromJson: ProjectSearchOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectModel>?> create(
    ProjectCreateInputModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProjectModel>(
      url: '/projects/create',
      body: body,
      method: 'POST',
      fromJson: ProjectModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectViewModel>?> edit({
    required String projectId,
    String? name,
    String? number,
    String? description,
    String? clientId,
    String? clientName,
    String? headUserId,
    DateTime? releaseDate,
  }) async {
    final body = jsonEncode({
      if (name != null) 'name': name,
      if (number != null) 'number': number,
      if (description != null) 'description': description,
      if (clientId != null) 'clientId': clientId,
      if (clientName != null) 'clientName': clientName,
      if (headUserId != null) 'headUserId': headUserId,
      if (releaseDate != null) 'releaseDate': releaseDate.toIso8601String(),
    });

    final request = await API.request<ProjectViewModel>(
      url: '/projects/edit?projectId=$projectId',
      body: body,
      method: 'POST',
      fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectViewModel>?> editOperations({
    required String projectId,
    required List<String> operations,
  }) async {
    final body = jsonEncode({'operations': operations});

    final request = await API.request<ProjectViewModel>(
      url: '/projects/edit?projectId=$projectId',
      body: body,
      method: 'POST',
      fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectViewModel>?> updateParameters(
    String projectId,
    ParametersModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProjectViewModel>(
      url: '/projects/updateParameters?projectId=$projectId',
      body: body,
      method: 'POST',
      fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectViewModel>?> view(
    String id,
  ) async {
    // final body = {'projectId': id};

    final request = await API.request<ProjectViewModel>(
      url: '/projects/view?projectId=$id',
      // body: jsonEncode(body),
      method: 'POST',
      fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectViewModel>?> addManagers(
    String projectId,
    List<String> userIds,
  ) async {
    final body = jsonEncode({
      'userIds': userIds,
    });

    final request = await API.request<ProjectViewModel>(
      url: '/projects/addManagers?projectId=$projectId',
      body: body,
      method: 'POST',
      fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectViewModel>?> removeManagers(
    String projectId,
    List<String> userIds,
  ) async {
    final body = jsonEncode({
      'userIds': userIds,
    });

    final request = await API.request<ProjectViewModel>(
      url: '/projects/removeManagers?projectId=$projectId',
      body: body,
      method: 'POST',
      fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }

  static Future<Response?> importStructure(
    String projectId,
    File file,
  ) async {
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(
        file.path,
        filename: file.path.split('/').last,
        contentType: DioMediaType('application', 'zip'),
      )
    });

    final request = await API.request(
      url: '/projects/importStructure?projectId=$projectId',
      body: formData,
      method: 'POST',
      options: Options(contentType: 'multipart/form-data'),
      // fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProjectModel>?> delete(
    String projectId, {
    bool forceDocument = true,
    bool forceProduct = true,
  }) async {
    final body = jsonEncode({
      'forceDocument': forceDocument,
      'forceProduct': forceProduct,
    });

    final request = await API.request<ProjectModel>(
      url: '/projects/delete?projectId=$projectId',
      body: body,
      method: 'POST',
      fromJson: ProjectModel.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>?> archive({
    required String projectId,
    required ArchiveInputModel data,
  }) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/projects/archive?projectId=$projectId',
      body: body,
      method: 'POST',
      // fromJson: ProjectViewModel.fromJson,
    );

    return request;
  }
}
