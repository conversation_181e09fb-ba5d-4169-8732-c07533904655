// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ArchiveInputModel _$ArchiveInputModelFromJson(Map<String, dynamic> json) {
  return _ArchiveInputModel.fromJson(json);
}

/// @nodoc
mixin _$ArchiveInputModel {
  List<ArchiveUnitModel>? get transferMaterials =>
      throw _privateConstructorUsedError;
  List<ArchiveUnitModel>? get writeOffMaterials =>
      throw _privateConstructorUsedError;
  bool? get force => throw _privateConstructorUsedError;

  /// Serializes this ArchiveInputModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ArchiveInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ArchiveInputModelCopyWith<ArchiveInputModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArchiveInputModelCopyWith<$Res> {
  factory $ArchiveInputModelCopyWith(
          ArchiveInputModel value, $Res Function(ArchiveInputModel) then) =
      _$ArchiveInputModelCopyWithImpl<$Res, ArchiveInputModel>;
  @useResult
  $Res call(
      {List<ArchiveUnitModel>? transferMaterials,
      List<ArchiveUnitModel>? writeOffMaterials,
      bool? force});
}

/// @nodoc
class _$ArchiveInputModelCopyWithImpl<$Res, $Val extends ArchiveInputModel>
    implements $ArchiveInputModelCopyWith<$Res> {
  _$ArchiveInputModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ArchiveInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferMaterials = freezed,
    Object? writeOffMaterials = freezed,
    Object? force = freezed,
  }) {
    return _then(_value.copyWith(
      transferMaterials: freezed == transferMaterials
          ? _value.transferMaterials
          : transferMaterials // ignore: cast_nullable_to_non_nullable
              as List<ArchiveUnitModel>?,
      writeOffMaterials: freezed == writeOffMaterials
          ? _value.writeOffMaterials
          : writeOffMaterials // ignore: cast_nullable_to_non_nullable
              as List<ArchiveUnitModel>?,
      force: freezed == force
          ? _value.force
          : force // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ArchiveInputModelImplCopyWith<$Res>
    implements $ArchiveInputModelCopyWith<$Res> {
  factory _$$ArchiveInputModelImplCopyWith(_$ArchiveInputModelImpl value,
          $Res Function(_$ArchiveInputModelImpl) then) =
      __$$ArchiveInputModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ArchiveUnitModel>? transferMaterials,
      List<ArchiveUnitModel>? writeOffMaterials,
      bool? force});
}

/// @nodoc
class __$$ArchiveInputModelImplCopyWithImpl<$Res>
    extends _$ArchiveInputModelCopyWithImpl<$Res, _$ArchiveInputModelImpl>
    implements _$$ArchiveInputModelImplCopyWith<$Res> {
  __$$ArchiveInputModelImplCopyWithImpl(_$ArchiveInputModelImpl _value,
      $Res Function(_$ArchiveInputModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ArchiveInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferMaterials = freezed,
    Object? writeOffMaterials = freezed,
    Object? force = freezed,
  }) {
    return _then(_$ArchiveInputModelImpl(
      transferMaterials: freezed == transferMaterials
          ? _value._transferMaterials
          : transferMaterials // ignore: cast_nullable_to_non_nullable
              as List<ArchiveUnitModel>?,
      writeOffMaterials: freezed == writeOffMaterials
          ? _value._writeOffMaterials
          : writeOffMaterials // ignore: cast_nullable_to_non_nullable
              as List<ArchiveUnitModel>?,
      force: freezed == force
          ? _value.force
          : force // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ArchiveInputModelImpl implements _ArchiveInputModel {
  _$ArchiveInputModelImpl(
      {final List<ArchiveUnitModel>? transferMaterials,
      final List<ArchiveUnitModel>? writeOffMaterials,
      this.force})
      : _transferMaterials = transferMaterials,
        _writeOffMaterials = writeOffMaterials;

  factory _$ArchiveInputModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ArchiveInputModelImplFromJson(json);

  final List<ArchiveUnitModel>? _transferMaterials;
  @override
  List<ArchiveUnitModel>? get transferMaterials {
    final value = _transferMaterials;
    if (value == null) return null;
    if (_transferMaterials is EqualUnmodifiableListView)
      return _transferMaterials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ArchiveUnitModel>? _writeOffMaterials;
  @override
  List<ArchiveUnitModel>? get writeOffMaterials {
    final value = _writeOffMaterials;
    if (value == null) return null;
    if (_writeOffMaterials is EqualUnmodifiableListView)
      return _writeOffMaterials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? force;

  @override
  String toString() {
    return 'ArchiveInputModel(transferMaterials: $transferMaterials, writeOffMaterials: $writeOffMaterials, force: $force)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ArchiveInputModelImpl &&
            const DeepCollectionEquality()
                .equals(other._transferMaterials, _transferMaterials) &&
            const DeepCollectionEquality()
                .equals(other._writeOffMaterials, _writeOffMaterials) &&
            (identical(other.force, force) || other.force == force));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_transferMaterials),
      const DeepCollectionEquality().hash(_writeOffMaterials),
      force);

  /// Create a copy of ArchiveInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ArchiveInputModelImplCopyWith<_$ArchiveInputModelImpl> get copyWith =>
      __$$ArchiveInputModelImplCopyWithImpl<_$ArchiveInputModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ArchiveInputModelImplToJson(
      this,
    );
  }
}

abstract class _ArchiveInputModel implements ArchiveInputModel {
  factory _ArchiveInputModel(
      {final List<ArchiveUnitModel>? transferMaterials,
      final List<ArchiveUnitModel>? writeOffMaterials,
      final bool? force}) = _$ArchiveInputModelImpl;

  factory _ArchiveInputModel.fromJson(Map<String, dynamic> json) =
      _$ArchiveInputModelImpl.fromJson;

  @override
  List<ArchiveUnitModel>? get transferMaterials;
  @override
  List<ArchiveUnitModel>? get writeOffMaterials;
  @override
  bool? get force;

  /// Create a copy of ArchiveInputModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ArchiveInputModelImplCopyWith<_$ArchiveInputModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ArchiveUnitModel _$ArchiveUnitModelFromJson(Map<String, dynamic> json) {
  return _ArchiveUnitModel.fromJson(json);
}

/// @nodoc
mixin _$ArchiveUnitModel {
  String? get storageItemId => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this ArchiveUnitModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ArchiveUnitModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ArchiveUnitModelCopyWith<ArchiveUnitModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArchiveUnitModelCopyWith<$Res> {
  factory $ArchiveUnitModelCopyWith(
          ArchiveUnitModel value, $Res Function(ArchiveUnitModel) then) =
      _$ArchiveUnitModelCopyWithImpl<$Res, ArchiveUnitModel>;
  @useResult
  $Res call({String? storageItemId, double? quantity, String? comment});
}

/// @nodoc
class _$ArchiveUnitModelCopyWithImpl<$Res, $Val extends ArchiveUnitModel>
    implements $ArchiveUnitModelCopyWith<$Res> {
  _$ArchiveUnitModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ArchiveUnitModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storageItemId = freezed,
    Object? quantity = freezed,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      storageItemId: freezed == storageItemId
          ? _value.storageItemId
          : storageItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ArchiveUnitModelImplCopyWith<$Res>
    implements $ArchiveUnitModelCopyWith<$Res> {
  factory _$$ArchiveUnitModelImplCopyWith(_$ArchiveUnitModelImpl value,
          $Res Function(_$ArchiveUnitModelImpl) then) =
      __$$ArchiveUnitModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? storageItemId, double? quantity, String? comment});
}

/// @nodoc
class __$$ArchiveUnitModelImplCopyWithImpl<$Res>
    extends _$ArchiveUnitModelCopyWithImpl<$Res, _$ArchiveUnitModelImpl>
    implements _$$ArchiveUnitModelImplCopyWith<$Res> {
  __$$ArchiveUnitModelImplCopyWithImpl(_$ArchiveUnitModelImpl _value,
      $Res Function(_$ArchiveUnitModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ArchiveUnitModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storageItemId = freezed,
    Object? quantity = freezed,
    Object? comment = freezed,
  }) {
    return _then(_$ArchiveUnitModelImpl(
      storageItemId: freezed == storageItemId
          ? _value.storageItemId
          : storageItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ArchiveUnitModelImpl implements _ArchiveUnitModel {
  _$ArchiveUnitModelImpl({this.storageItemId, this.quantity, this.comment});

  factory _$ArchiveUnitModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ArchiveUnitModelImplFromJson(json);

  @override
  final String? storageItemId;
  @override
  final double? quantity;
  @override
  final String? comment;

  @override
  String toString() {
    return 'ArchiveUnitModel(storageItemId: $storageItemId, quantity: $quantity, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ArchiveUnitModelImpl &&
            (identical(other.storageItemId, storageItemId) ||
                other.storageItemId == storageItemId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, storageItemId, quantity, comment);

  /// Create a copy of ArchiveUnitModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ArchiveUnitModelImplCopyWith<_$ArchiveUnitModelImpl> get copyWith =>
      __$$ArchiveUnitModelImplCopyWithImpl<_$ArchiveUnitModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ArchiveUnitModelImplToJson(
      this,
    );
  }
}

abstract class _ArchiveUnitModel implements ArchiveUnitModel {
  factory _ArchiveUnitModel(
      {final String? storageItemId,
      final double? quantity,
      final String? comment}) = _$ArchiveUnitModelImpl;

  factory _ArchiveUnitModel.fromJson(Map<String, dynamic> json) =
      _$ArchiveUnitModelImpl.fromJson;

  @override
  String? get storageItemId;
  @override
  double? get quantity;
  @override
  String? get comment;

  /// Create a copy of ArchiveUnitModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ArchiveUnitModelImplCopyWith<_$ArchiveUnitModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
