import 'package:json_annotation/json_annotation.dart';

enum Department {
  @JsonValue('ogk')
  ogk,
  @JsonValue('ogt')
  ogt,
  @JsonValue('osivk')
  osivk,
  @JsonValue('sh')
  sh,
  @JsonValue('otk')
  otk,
  @JsonValue('prd')
  prd;

  String getName() {
    switch (this) {
      case Department.ogk:
        return 'ОГК';
      case Department.ogt:
        return 'ОГТ';
      case Department.osivk:
        return 'Отдел снабжения';
      case Department.sh:
        return 'Складское хозяйство';
      case Department.otk:
        return 'ОТК';
      case Department.prd:
        return 'Производство';
    }
  }

  static Department fromString(String value) {
    return Department.values.firstWhere(
      (e) => e.toString().split('.').last == value,
      orElse: () => Department.ogk,
    );
  }

  String toJson() => toString().split('.').last;
}

enum DocumentDepartment {
  @JsonValue('management')
  management,
  @JsonValue('ogs')
  ogs,
  @JsonValue('general')
  general,
  // old
  @JsonValue('ogk')
  ogk,
  @JsonValue('ogt')
  ogt,
  @JsonValue('osivk')
  osivk,
  @JsonValue('sh')
  sh,
  @JsonValue('otk')
  otk,
  @JsonValue('prd')
  prd;

  String getName() {
    switch (this) {
      case DocumentDepartment.ogk:
        return 'ОГК';
      case DocumentDepartment.ogt:
        return 'ОГТ';
      case DocumentDepartment.osivk:
        return 'Отдел снабжения';
      case DocumentDepartment.sh:
        return 'Складское хозяйство';
      case DocumentDepartment.otk:
        return 'ОТК';
      case DocumentDepartment.prd:
        return 'Производство';
      case DocumentDepartment.management:
        return 'Руководство';
      case DocumentDepartment.ogs:
        return 'ОГС';
      case DocumentDepartment.general:
        return 'Общее';
    }
  }

  static DocumentDepartment fromString(String value) {
    return DocumentDepartment.values.firstWhere(
      (e) => e.toString().split('.').last == value,
      orElse: () => DocumentDepartment.ogk,
    );
  }

  String toJson() => toString().split('.').last;
}
