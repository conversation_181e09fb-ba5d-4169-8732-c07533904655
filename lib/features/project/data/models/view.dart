import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/files/data/models/file.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/user/data/models/user.dart';

part 'view.g.dart';

@JsonSerializable(includeIfNull: false)
class ProjectViewModel extends ProjectModel {
  // TODO: added type
  final UserModel? headUser;
  final List<UserModel>? workers;
  final List<UserModel>? managersHistory;
  final int? productsCount;
  final int? documentsCount;
  @JsonKey(name: 'bluePrint')
  final FileModel? blueprint;

  ProjectViewModel({
    this.headUser,
    this.workers,
    this.managersHistory,
    this.productsCount,
    this.documentsCount,
    this.blueprint,
    // initial
    super.id,
    super.name,
    super.number,
    super.description,
    // super.status,
    super.managers,
    super.client,
    super.parameters,
    super.department,
    super.headUserId,
    super.workerIds,
    super.branchId,
    super.operations,
    super.progressTask,
    super.releaseDate,
    super.createdAt,
    super.updatedAt,
  });

  factory ProjectViewModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectViewModelFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$ProjectViewModelToJson(this);

  ProjectModel toProjectModel() {
    return ProjectModel(
      id: id,
      name: name,
      number: number,
      description: description,
      managers: managers,
      // status: status,
      client: client,
      parameters: parameters,
      department: department,
      headUserId: headUserId,
      workerIds: workerIds,
      branchId: branchId,
      progressTask: progressTask,
      releaseDate: releaseDate,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
