// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'parameters.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ParametersModel _$ParametersModelFromJson(Map<String, dynamic> json) {
  return _ParametersModel.fromJson(json);
}

/// @nodoc
mixin _$ParametersModel {
  double? get mass => throw _privateConstructorUsedError;
  double? get width => throw _privateConstructorUsedError;
  double? get length => throw _privateConstructorUsedError;
  double? get height => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  String? get materialId => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;
  @JsonKey(includeIfNull: false)
  NomenclatureModel? get material => throw _privateConstructorUsedError;
  List<ParametersFeatureType>? get featureTypes =>
      throw _privateConstructorUsedError;
  String? get itemRequirements => throw _privateConstructorUsedError;
  String? get materialRequirements => throw _privateConstructorUsedError;
  double? get pureMass => throw _privateConstructorUsedError;
  String? get surfaceCoating => throw _privateConstructorUsedError;
  String? get note => throw _privateConstructorUsedError;
  UnitType? get unitType => throw _privateConstructorUsedError;
  bool? get needMaterial => throw _privateConstructorUsedError;
  DateTime? get releaseDate => throw _privateConstructorUsedError;

  /// Serializes this ParametersModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ParametersModelCopyWith<ParametersModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ParametersModelCopyWith<$Res> {
  factory $ParametersModelCopyWith(
          ParametersModel value, $Res Function(ParametersModel) then) =
      _$ParametersModelCopyWithImpl<$Res, ParametersModel>;
  @useResult
  $Res call(
      {double? mass,
      double? width,
      double? length,
      double? height,
      double? quantity,
      @JsonKey(includeIfNull: false) String? materialId,
      String? materialName,
      @JsonKey(includeIfNull: false) NomenclatureModel? material,
      List<ParametersFeatureType>? featureTypes,
      String? itemRequirements,
      String? materialRequirements,
      double? pureMass,
      String? surfaceCoating,
      String? note,
      UnitType? unitType,
      bool? needMaterial,
      DateTime? releaseDate});
}

/// @nodoc
class _$ParametersModelCopyWithImpl<$Res, $Val extends ParametersModel>
    implements $ParametersModelCopyWith<$Res> {
  _$ParametersModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mass = freezed,
    Object? width = freezed,
    Object? length = freezed,
    Object? height = freezed,
    Object? quantity = freezed,
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? material = freezed,
    Object? featureTypes = freezed,
    Object? itemRequirements = freezed,
    Object? materialRequirements = freezed,
    Object? pureMass = freezed,
    Object? surfaceCoating = freezed,
    Object? note = freezed,
    Object? unitType = freezed,
    Object? needMaterial = freezed,
    Object? releaseDate = freezed,
  }) {
    return _then(_value.copyWith(
      mass: freezed == mass
          ? _value.mass
          : mass // ignore: cast_nullable_to_non_nullable
              as double?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as double?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      featureTypes: freezed == featureTypes
          ? _value.featureTypes
          : featureTypes // ignore: cast_nullable_to_non_nullable
              as List<ParametersFeatureType>?,
      itemRequirements: freezed == itemRequirements
          ? _value.itemRequirements
          : itemRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      pureMass: freezed == pureMass
          ? _value.pureMass
          : pureMass // ignore: cast_nullable_to_non_nullable
              as double?,
      surfaceCoating: freezed == surfaceCoating
          ? _value.surfaceCoating
          : surfaceCoating // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      needMaterial: freezed == needMaterial
          ? _value.needMaterial
          : needMaterial // ignore: cast_nullable_to_non_nullable
              as bool?,
      releaseDate: freezed == releaseDate
          ? _value.releaseDate
          : releaseDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ParametersModelImplCopyWith<$Res>
    implements $ParametersModelCopyWith<$Res> {
  factory _$$ParametersModelImplCopyWith(_$ParametersModelImpl value,
          $Res Function(_$ParametersModelImpl) then) =
      __$$ParametersModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? mass,
      double? width,
      double? length,
      double? height,
      double? quantity,
      @JsonKey(includeIfNull: false) String? materialId,
      String? materialName,
      @JsonKey(includeIfNull: false) NomenclatureModel? material,
      List<ParametersFeatureType>? featureTypes,
      String? itemRequirements,
      String? materialRequirements,
      double? pureMass,
      String? surfaceCoating,
      String? note,
      UnitType? unitType,
      bool? needMaterial,
      DateTime? releaseDate});
}

/// @nodoc
class __$$ParametersModelImplCopyWithImpl<$Res>
    extends _$ParametersModelCopyWithImpl<$Res, _$ParametersModelImpl>
    implements _$$ParametersModelImplCopyWith<$Res> {
  __$$ParametersModelImplCopyWithImpl(
      _$ParametersModelImpl _value, $Res Function(_$ParametersModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mass = freezed,
    Object? width = freezed,
    Object? length = freezed,
    Object? height = freezed,
    Object? quantity = freezed,
    Object? materialId = freezed,
    Object? materialName = freezed,
    Object? material = freezed,
    Object? featureTypes = freezed,
    Object? itemRequirements = freezed,
    Object? materialRequirements = freezed,
    Object? pureMass = freezed,
    Object? surfaceCoating = freezed,
    Object? note = freezed,
    Object? unitType = freezed,
    Object? needMaterial = freezed,
    Object? releaseDate = freezed,
  }) {
    return _then(_$ParametersModelImpl(
      mass: freezed == mass
          ? _value.mass
          : mass // ignore: cast_nullable_to_non_nullable
              as double?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as double?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      featureTypes: freezed == featureTypes
          ? _value._featureTypes
          : featureTypes // ignore: cast_nullable_to_non_nullable
              as List<ParametersFeatureType>?,
      itemRequirements: freezed == itemRequirements
          ? _value.itemRequirements
          : itemRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      pureMass: freezed == pureMass
          ? _value.pureMass
          : pureMass // ignore: cast_nullable_to_non_nullable
              as double?,
      surfaceCoating: freezed == surfaceCoating
          ? _value.surfaceCoating
          : surfaceCoating // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      unitType: freezed == unitType
          ? _value.unitType
          : unitType // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      needMaterial: freezed == needMaterial
          ? _value.needMaterial
          : needMaterial // ignore: cast_nullable_to_non_nullable
              as bool?,
      releaseDate: freezed == releaseDate
          ? _value.releaseDate
          : releaseDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ParametersModelImpl implements _ParametersModel {
  const _$ParametersModelImpl(
      {this.mass,
      this.width,
      this.length,
      this.height,
      this.quantity,
      @JsonKey(includeIfNull: false) this.materialId,
      this.materialName,
      @JsonKey(includeIfNull: false) this.material,
      final List<ParametersFeatureType>? featureTypes,
      this.itemRequirements,
      this.materialRequirements,
      this.pureMass,
      this.surfaceCoating,
      this.note,
      this.unitType,
      this.needMaterial,
      this.releaseDate})
      : _featureTypes = featureTypes;

  factory _$ParametersModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ParametersModelImplFromJson(json);

  @override
  final double? mass;
  @override
  final double? width;
  @override
  final double? length;
  @override
  final double? height;
  @override
  final double? quantity;
  @override
  @JsonKey(includeIfNull: false)
  final String? materialId;
  @override
  final String? materialName;
  @override
  @JsonKey(includeIfNull: false)
  final NomenclatureModel? material;
  final List<ParametersFeatureType>? _featureTypes;
  @override
  List<ParametersFeatureType>? get featureTypes {
    final value = _featureTypes;
    if (value == null) return null;
    if (_featureTypes is EqualUnmodifiableListView) return _featureTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? itemRequirements;
  @override
  final String? materialRequirements;
  @override
  final double? pureMass;
  @override
  final String? surfaceCoating;
  @override
  final String? note;
  @override
  final UnitType? unitType;
  @override
  final bool? needMaterial;
  @override
  final DateTime? releaseDate;

  @override
  String toString() {
    return 'ParametersModel(mass: $mass, width: $width, length: $length, height: $height, quantity: $quantity, materialId: $materialId, materialName: $materialName, material: $material, featureTypes: $featureTypes, itemRequirements: $itemRequirements, materialRequirements: $materialRequirements, pureMass: $pureMass, surfaceCoating: $surfaceCoating, note: $note, unitType: $unitType, needMaterial: $needMaterial, releaseDate: $releaseDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ParametersModelImpl &&
            (identical(other.mass, mass) || other.mass == mass) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.material, material) ||
                other.material == material) &&
            const DeepCollectionEquality()
                .equals(other._featureTypes, _featureTypes) &&
            (identical(other.itemRequirements, itemRequirements) ||
                other.itemRequirements == itemRequirements) &&
            (identical(other.materialRequirements, materialRequirements) ||
                other.materialRequirements == materialRequirements) &&
            (identical(other.pureMass, pureMass) ||
                other.pureMass == pureMass) &&
            (identical(other.surfaceCoating, surfaceCoating) ||
                other.surfaceCoating == surfaceCoating) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.unitType, unitType) ||
                other.unitType == unitType) &&
            (identical(other.needMaterial, needMaterial) ||
                other.needMaterial == needMaterial) &&
            (identical(other.releaseDate, releaseDate) ||
                other.releaseDate == releaseDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      mass,
      width,
      length,
      height,
      quantity,
      materialId,
      materialName,
      material,
      const DeepCollectionEquality().hash(_featureTypes),
      itemRequirements,
      materialRequirements,
      pureMass,
      surfaceCoating,
      note,
      unitType,
      needMaterial,
      releaseDate);

  /// Create a copy of ParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ParametersModelImplCopyWith<_$ParametersModelImpl> get copyWith =>
      __$$ParametersModelImplCopyWithImpl<_$ParametersModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ParametersModelImplToJson(
      this,
    );
  }
}

abstract class _ParametersModel implements ParametersModel {
  const factory _ParametersModel(
      {final double? mass,
      final double? width,
      final double? length,
      final double? height,
      final double? quantity,
      @JsonKey(includeIfNull: false) final String? materialId,
      final String? materialName,
      @JsonKey(includeIfNull: false) final NomenclatureModel? material,
      final List<ParametersFeatureType>? featureTypes,
      final String? itemRequirements,
      final String? materialRequirements,
      final double? pureMass,
      final String? surfaceCoating,
      final String? note,
      final UnitType? unitType,
      final bool? needMaterial,
      final DateTime? releaseDate}) = _$ParametersModelImpl;

  factory _ParametersModel.fromJson(Map<String, dynamic> json) =
      _$ParametersModelImpl.fromJson;

  @override
  double? get mass;
  @override
  double? get width;
  @override
  double? get length;
  @override
  double? get height;
  @override
  double? get quantity;
  @override
  @JsonKey(includeIfNull: false)
  String? get materialId;
  @override
  String? get materialName;
  @override
  @JsonKey(includeIfNull: false)
  NomenclatureModel? get material;
  @override
  List<ParametersFeatureType>? get featureTypes;
  @override
  String? get itemRequirements;
  @override
  String? get materialRequirements;
  @override
  double? get pureMass;
  @override
  String? get surfaceCoating;
  @override
  String? get note;
  @override
  UnitType? get unitType;
  @override
  bool? get needMaterial;
  @override
  DateTime? get releaseDate;

  /// Create a copy of ParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ParametersModelImplCopyWith<_$ParametersModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProjectParametersModel _$ProjectParametersModelFromJson(
    Map<String, dynamic> json) {
  return _ProjectParametersModel.fromJson(json);
}

/// @nodoc
mixin _$ProjectParametersModel {
  double? get mass => throw _privateConstructorUsedError;
  double? get width => throw _privateConstructorUsedError;
  double? get length => throw _privateConstructorUsedError;
  double? get height => throw _privateConstructorUsedError;
  double? get quantity => throw _privateConstructorUsedError;

  /// Serializes this ProjectParametersModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProjectParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProjectParametersModelCopyWith<ProjectParametersModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProjectParametersModelCopyWith<$Res> {
  factory $ProjectParametersModelCopyWith(ProjectParametersModel value,
          $Res Function(ProjectParametersModel) then) =
      _$ProjectParametersModelCopyWithImpl<$Res, ProjectParametersModel>;
  @useResult
  $Res call(
      {double? mass,
      double? width,
      double? length,
      double? height,
      double? quantity});
}

/// @nodoc
class _$ProjectParametersModelCopyWithImpl<$Res,
        $Val extends ProjectParametersModel>
    implements $ProjectParametersModelCopyWith<$Res> {
  _$ProjectParametersModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProjectParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mass = freezed,
    Object? width = freezed,
    Object? length = freezed,
    Object? height = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_value.copyWith(
      mass: freezed == mass
          ? _value.mass
          : mass // ignore: cast_nullable_to_non_nullable
              as double?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as double?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProjectParametersModelImplCopyWith<$Res>
    implements $ProjectParametersModelCopyWith<$Res> {
  factory _$$ProjectParametersModelImplCopyWith(
          _$ProjectParametersModelImpl value,
          $Res Function(_$ProjectParametersModelImpl) then) =
      __$$ProjectParametersModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? mass,
      double? width,
      double? length,
      double? height,
      double? quantity});
}

/// @nodoc
class __$$ProjectParametersModelImplCopyWithImpl<$Res>
    extends _$ProjectParametersModelCopyWithImpl<$Res,
        _$ProjectParametersModelImpl>
    implements _$$ProjectParametersModelImplCopyWith<$Res> {
  __$$ProjectParametersModelImplCopyWithImpl(
      _$ProjectParametersModelImpl _value,
      $Res Function(_$ProjectParametersModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProjectParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mass = freezed,
    Object? width = freezed,
    Object? length = freezed,
    Object? height = freezed,
    Object? quantity = freezed,
  }) {
    return _then(_$ProjectParametersModelImpl(
      mass: freezed == mass
          ? _value.mass
          : mass // ignore: cast_nullable_to_non_nullable
              as double?,
      width: freezed == width
          ? _value.width
          : width // ignore: cast_nullable_to_non_nullable
              as double?,
      length: freezed == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as double?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProjectParametersModelImpl implements _ProjectParametersModel {
  const _$ProjectParametersModelImpl(
      {this.mass, this.width, this.length, this.height, this.quantity});

  factory _$ProjectParametersModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProjectParametersModelImplFromJson(json);

  @override
  final double? mass;
  @override
  final double? width;
  @override
  final double? length;
  @override
  final double? height;
  @override
  final double? quantity;

  @override
  String toString() {
    return 'ProjectParametersModel(mass: $mass, width: $width, length: $length, height: $height, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProjectParametersModelImpl &&
            (identical(other.mass, mass) || other.mass == mass) &&
            (identical(other.width, width) || other.width == width) &&
            (identical(other.length, length) || other.length == length) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, mass, width, length, height, quantity);

  /// Create a copy of ProjectParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProjectParametersModelImplCopyWith<_$ProjectParametersModelImpl>
      get copyWith => __$$ProjectParametersModelImplCopyWithImpl<
          _$ProjectParametersModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProjectParametersModelImplToJson(
      this,
    );
  }
}

abstract class _ProjectParametersModel implements ProjectParametersModel {
  const factory _ProjectParametersModel(
      {final double? mass,
      final double? width,
      final double? length,
      final double? height,
      final double? quantity}) = _$ProjectParametersModelImpl;

  factory _ProjectParametersModel.fromJson(Map<String, dynamic> json) =
      _$ProjectParametersModelImpl.fromJson;

  @override
  double? get mass;
  @override
  double? get width;
  @override
  double? get length;
  @override
  double? get height;
  @override
  double? get quantity;

  /// Create a copy of ProjectParametersModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProjectParametersModelImplCopyWith<_$ProjectParametersModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
