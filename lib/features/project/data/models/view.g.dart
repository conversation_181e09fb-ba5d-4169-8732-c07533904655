// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'view.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectViewModel _$ProjectViewModelFromJson(Map<String, dynamic> json) =>
    ProjectViewModel(
      headUser: json['headUser'] == null
          ? null
          : UserModel.fromJson(json['headUser'] as Map<String, dynamic>),
      workers: (json['workers'] as List<dynamic>?)
          ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      managersHistory: (json['managersHistory'] as List<dynamic>?)
          ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      productsCount: (json['productsCount'] as num?)?.toInt(),
      documentsCount: (json['documentsCount'] as num?)?.toInt(),
      blueprint: json['bluePrint'] == null
          ? null
          : FileModel.fromJson(json['bluePrint'] as Map<String, dynamic>),
      id: json['_id'] as String?,
      name: json['name'] as String?,
      number: json['number'] as String?,
      description: json['description'] as String?,
      managers: (json['managers'] as List<dynamic>?)
          ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      client: json['client'] == null
          ? null
          : ClientModel.fromJson(json['client'] as Map<String, dynamic>),
      parameters: json['parameters'] == null
          ? null
          : ParametersModel.fromJson(
              json['parameters'] as Map<String, dynamic>),
      department:
          $enumDecodeNullable(_$DepartmentEnumMap, json['currentDepartment']),
      headUserId: json['headUserId'] as String?,
      workerIds: (json['workerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      branchId: json['branchId'] as String?,
      operations: (json['operations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      progressTask: json['progressTask'] == null
          ? null
          : TaskProgressModel.fromJson(
              json['progressTask'] as Map<String, dynamic>),
      releaseDate: json['releaseDate'] == null
          ? null
          : DateTime.parse(json['releaseDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ProjectViewModelToJson(ProjectViewModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.number case final value?) 'number': value,
      if (instance.description case final value?) 'description': value,
      if (instance.client case final value?) 'client': value,
      if (instance.parameters case final value?) 'parameters': value,
      if (instance.department case final value?) 'currentDepartment': value,
      if (instance.headUserId case final value?) 'headUserId': value,
      if (instance.managers case final value?) 'managers': value,
      if (instance.workerIds case final value?) 'workerIds': value,
      if (instance.branchId case final value?) 'branchId': value,
      if (instance.operations case final value?) 'operations': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.progressTask case final value?) 'progressTask': value,
      if (instance.releaseDate?.toIso8601String() case final value?)
        'releaseDate': value,
      if (instance.headUser case final value?) 'headUser': value,
      if (instance.workers case final value?) 'workers': value,
      if (instance.managersHistory case final value?) 'managersHistory': value,
      if (instance.productsCount case final value?) 'productsCount': value,
      if (instance.documentsCount case final value?) 'documentsCount': value,
      if (instance.blueprint case final value?) 'bluePrint': value,
    };

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};
