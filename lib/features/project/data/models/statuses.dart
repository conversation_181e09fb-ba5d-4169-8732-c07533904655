import 'package:json_annotation/json_annotation.dart';

enum ProjectStatus {
  @JsonValue('new')
  newProject,
  @JsonValue('work')
  work,
  @JsonValue('archive')
  archive;

  String getName() {
    switch (this) {
      case ProjectStatus.newProject:
        return 'Новый проект';
      case ProjectStatus.work:
        return 'В работе';
      case ProjectStatus.archive:
        return 'В архиве';
    }
  }
}
