// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';

part 'parameters.freezed.dart';
part 'parameters.g.dart';

@freezed
class ParametersModel with _$ParametersModel {
  const factory ParametersModel({
    double? mass,
    double? width,
    double? length,
    double? height,
    double? quantity,
    @JsonKey(includeIfNull: false) String? materialId,
    String? materialName,
    @JsonKey(includeIfNull: false) NomenclatureModel? material,
    List<ParametersFeatureType>? featureTypes,
    String? itemRequirements,
    String? materialRequirements,
    double? pureMass,
    String? surfaceCoating,
    String? note,
    UnitType? unitType,
    bool? needMaterial,
    DateTime? releaseDate,
  }) = _ParametersModel;

  factory ParametersModel.fromJson(Map<String, dynamic> json) =>
      _$ParametersModelFromJson(json);
}

@freezed
class ProjectParametersModel with _$ProjectParametersModel {
  const factory ProjectParametersModel({
    double? mass,
    double? width,
    double? length,
    double? height,
    double? quantity,
  }) = _ProjectParametersModel;

  factory ProjectParametersModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectParametersModelFromJson(json);
}

enum ParametersFeatureType {
  @JsonValue('MECHANICS')
  mechanics,
  @JsonValue('BENDING')
  bending,
  @JsonValue('CUTTING')
  cutting,
  @JsonValue('COATING')
  coating,
  @JsonValue('HEAT_TREATMENT')
  heatTreatment,
  @JsonValue('ROLLING')
  rolling,
  @JsonValue('WELDING')
  welding;

  String getName() {
    switch (this) {
      case ParametersFeatureType.mechanics:
        return 'Механическая обработка';
      case ParametersFeatureType.bending:
        return 'Гибка';
      case ParametersFeatureType.cutting:
        return 'Резка';
      case ParametersFeatureType.coating:
        return 'Покрытие';
      case ParametersFeatureType.heatTreatment:
        return 'Термообработка';
      case ParametersFeatureType.rolling:
        return 'Вальцовка';
      case ParametersFeatureType.welding:
        return 'Сварка';
    }
  }

  String getJsonnify() {
    switch (this) {
      case ParametersFeatureType.mechanics:
        return 'MECHANICS';
      case ParametersFeatureType.bending:
        return 'BENDING';
      case ParametersFeatureType.cutting:
        return 'CUTTING';
      case ParametersFeatureType.coating:
        return 'COATING';
      case ParametersFeatureType.heatTreatment:
        return 'HEAT_TREATMENT';
      case ParametersFeatureType.rolling:
        return 'ROLLING';
      case ParametersFeatureType.welding:
        return 'WELDING';
    }
  }
}
