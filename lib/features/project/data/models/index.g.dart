// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectSearchOutputModel _$ProjectSearchOutputModelFromJson(
        Map<String, dynamic> json) =>
    ProjectSearchOutputModel(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ProjectSearchOutputModelToJson(
        ProjectSearchOutputModel instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

ProjectCreateInputModel _$ProjectCreateInputModelFromJson(
        Map<String, dynamic> json) =>
    ProjectCreateInputModel(
      name: json['name'] as String?,
      number: json['number'] as String?,
      description: json['description'] as String?,
      clientId: json['clientId'] as String?,
      clientName: json['clientName'] as String?,
      headUserId: json['headUserId'] as String?,
      managerIds: (json['managerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      branchId: json['branchId'] as String?,
      releaseDate: json['releaseDate'] == null
          ? null
          : DateTime.parse(json['releaseDate'] as String),
    );

Map<String, dynamic> _$ProjectCreateInputModelToJson(
        ProjectCreateInputModel instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (instance.number case final value?) 'number': value,
      if (instance.description case final value?) 'description': value,
      if (instance.clientId case final value?) 'clientId': value,
      if (instance.clientName case final value?) 'clientName': value,
      if (instance.headUserId case final value?) 'headUserId': value,
      if (instance.managerIds case final value?) 'managerIds': value,
      if (instance.branchId case final value?) 'branchId': value,
      if (instance.releaseDate?.toIso8601String() case final value?)
        'releaseDate': value,
    };

_$ArchiveInputModelImpl _$$ArchiveInputModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ArchiveInputModelImpl(
      transferMaterials: (json['transferMaterials'] as List<dynamic>?)
          ?.map((e) => ArchiveUnitModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      writeOffMaterials: (json['writeOffMaterials'] as List<dynamic>?)
          ?.map((e) => ArchiveUnitModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      force: json['force'] as bool?,
    );

Map<String, dynamic> _$$ArchiveInputModelImplToJson(
        _$ArchiveInputModelImpl instance) =>
    <String, dynamic>{
      if (instance.transferMaterials case final value?)
        'transferMaterials': value,
      if (instance.writeOffMaterials case final value?)
        'writeOffMaterials': value,
      if (instance.force case final value?) 'force': value,
    };

_$ArchiveUnitModelImpl _$$ArchiveUnitModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ArchiveUnitModelImpl(
      storageItemId: json['storageItemId'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble(),
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$$ArchiveUnitModelImplToJson(
        _$ArchiveUnitModelImpl instance) =>
    <String, dynamic>{
      if (instance.storageItemId case final value?) 'storageItemId': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.comment case final value?) 'comment': value,
    };
