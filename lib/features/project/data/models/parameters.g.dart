// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parameters.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ParametersModelImpl _$$ParametersModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ParametersModelImpl(
      mass: (json['mass'] as num?)?.toDouble(),
      width: (json['width'] as num?)?.toDouble(),
      length: (json['length'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num?)?.toDouble(),
      materialId: json['materialId'] as String?,
      materialName: json['materialName'] as String?,
      material: json['material'] == null
          ? null
          : NomenclatureModel.fromJson(
              json['material'] as Map<String, dynamic>),
      featureTypes: (json['featureTypes'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ParametersFeatureTypeEnumMap, e))
          .toList(),
      itemRequirements: json['itemRequirements'] as String?,
      materialRequirements: json['materialRequirements'] as String?,
      pureMass: (json['pureMass'] as num?)?.toDouble(),
      surfaceCoating: json['surfaceCoating'] as String?,
      note: json['note'] as String?,
      unitType: $enumDecodeNullable(_$UnitTypeEnumMap, json['unitType']),
      needMaterial: json['needMaterial'] as bool?,
      releaseDate: json['releaseDate'] == null
          ? null
          : DateTime.parse(json['releaseDate'] as String),
    );

Map<String, dynamic> _$$ParametersModelImplToJson(
        _$ParametersModelImpl instance) =>
    <String, dynamic>{
      'mass': instance.mass,
      'width': instance.width,
      'length': instance.length,
      'height': instance.height,
      'quantity': instance.quantity,
      if (instance.materialId case final value?) 'materialId': value,
      'materialName': instance.materialName,
      if (instance.material case final value?) 'material': value,
      'featureTypes': instance.featureTypes
          ?.map((e) => _$ParametersFeatureTypeEnumMap[e]!)
          .toList(),
      'itemRequirements': instance.itemRequirements,
      'materialRequirements': instance.materialRequirements,
      'pureMass': instance.pureMass,
      'surfaceCoating': instance.surfaceCoating,
      'note': instance.note,
      'unitType': _$UnitTypeEnumMap[instance.unitType],
      'needMaterial': instance.needMaterial,
      'releaseDate': instance.releaseDate?.toIso8601String(),
    };

const _$ParametersFeatureTypeEnumMap = {
  ParametersFeatureType.mechanics: 'MECHANICS',
  ParametersFeatureType.bending: 'BENDING',
  ParametersFeatureType.cutting: 'CUTTING',
  ParametersFeatureType.coating: 'COATING',
  ParametersFeatureType.heatTreatment: 'HEAT_TREATMENT',
  ParametersFeatureType.rolling: 'ROLLING',
  ParametersFeatureType.welding: 'WELDING',
};

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};

_$ProjectParametersModelImpl _$$ProjectParametersModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProjectParametersModelImpl(
      mass: (json['mass'] as num?)?.toDouble(),
      width: (json['width'] as num?)?.toDouble(),
      length: (json['length'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ProjectParametersModelImplToJson(
        _$ProjectParametersModelImpl instance) =>
    <String, dynamic>{
      'mass': instance.mass,
      'width': instance.width,
      'length': instance.length,
      'height': instance.height,
      'quantity': instance.quantity,
    };
