import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/project/data/models/project.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@JsonSerializable(includeIfNull: false)
class ProjectSearchOutputModel {
  final List<ProjectModel>? items;
  final int? totalItems;

  const ProjectSearchOutputModel({
    this.items,
    this.totalItems,
  });

  factory ProjectSearchOutputModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectSearchOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectSearchOutputModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class ProjectCreateInputModel {
  final String? name;
  final String? number;
  final String? description;
  final String? clientId;
  final String? clientName;
  final String? headUserId;
  final List<String>? managerIds;
  final String? branchId;
  final DateTime? releaseDate;

  ProjectCreateInputModel({
    this.name,
    this.number,
    this.description,
    this.clientId,
    this.clientName,
    this.headUserId,
    this.managerIds,
    this.branchId,
    this.releaseDate,
  });

  factory ProjectCreateInputModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectCreateInputModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectCreateInputModelToJson(this);
}

@freezed
class ArchiveInputModel with _$ArchiveInputModel {
  @JsonSerializable(includeIfNull: false)
  factory ArchiveInputModel({
    List<ArchiveUnitModel>? transferMaterials,
    List<ArchiveUnitModel>? writeOffMaterials,
    bool? force,
  }) = _ArchiveInputModel;

  factory ArchiveInputModel.fromJson(Map<String, dynamic> json) =>
      _$ArchiveInputModelFromJson(json);
}

@freezed
class ArchiveUnitModel with _$ArchiveUnitModel {
  @JsonSerializable(includeIfNull: false)
  factory ArchiveUnitModel({
    String? storageItemId,
    double? quantity,
    String? comment,
  }) = _ArchiveUnitModel;

  factory ArchiveUnitModel.fromJson(Map<String, dynamic> json) =>
      _$ArchiveUnitModelFromJson(json);
}
