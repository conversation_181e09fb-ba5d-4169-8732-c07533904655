// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectModel _$ProjectModelFromJson(Map<String, dynamic> json) => ProjectModel(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      number: json['number'] as String?,
      description: json['description'] as String?,
      status: $enumDecodeNullable(_$ProjectStatusEnumMap, json['status']),
      client: json['client'] == null
          ? null
          : ClientModel.fromJson(json['client'] as Map<String, dynamic>),
      parameters: json['parameters'] == null
          ? null
          : ParametersModel.fromJson(
              json['parameters'] as Map<String, dynamic>),
      department:
          $enumDecodeNullable(_$DepartmentEnumMap, json['currentDepartment']),
      headUserId: json['headUserId'] as String?,
      workerIds: (json['workerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      managers: (json['managers'] as List<dynamic>?)
          ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      branchId: json['branchId'] as String?,
      operations: (json['operations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      progressTask: json['progressTask'] == null
          ? null
          : TaskProgressModel.fromJson(
              json['progressTask'] as Map<String, dynamic>),
      releaseDate: json['releaseDate'] == null
          ? null
          : DateTime.parse(json['releaseDate'] as String),
    );

Map<String, dynamic> _$ProjectModelToJson(ProjectModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.number case final value?) 'number': value,
      if (instance.description case final value?) 'description': value,
      if (_$ProjectStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.client case final value?) 'client': value,
      if (instance.parameters case final value?) 'parameters': value,
      if (instance.department case final value?) 'currentDepartment': value,
      if (instance.headUserId case final value?) 'headUserId': value,
      if (instance.managers case final value?) 'managers': value,
      if (instance.workerIds case final value?) 'workerIds': value,
      if (instance.branchId case final value?) 'branchId': value,
      if (instance.operations case final value?) 'operations': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.progressTask case final value?) 'progressTask': value,
      if (instance.releaseDate?.toIso8601String() case final value?)
        'releaseDate': value,
    };

const _$ProjectStatusEnumMap = {
  ProjectStatus.newProject: 'new',
  ProjectStatus.work: 'work',
  ProjectStatus.archive: 'archive',
};

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};
