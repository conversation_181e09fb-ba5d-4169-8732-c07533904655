import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/project/data/models/statuses.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/user/data/models/user.dart';

part 'project.g.dart';

@JsonSerializable(includeIfNull: false)
class ProjectModel {
  @JsonKey(name: '_id')
  final String? id;
  final String? name;
  final String? number;
  final String? description;
  final ProjectStatus? status;
  final ClientModel? client;
  final ParametersModel? parameters;
  @J<PERSON><PERSON><PERSON>(name: 'currentDepartment')
  final Department? department;
  final String? headUserId;
  final List<UserModel>? managers;
  final List<String>? workerIds;
  final String? branchId;
  final List<String>? operations;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final TaskProgressModel? progressTask;
  final DateTime? releaseDate;

  const ProjectModel({
    this.id,
    this.name,
    this.number,
    this.description,
    this.status,
    this.client,
    this.parameters,
    this.department,
    this.headUserId,
    this.workerIds,
    this.managers,
    this.branchId,
    this.operations,
    this.createdAt,
    this.updatedAt,
    this.progressTask,
    this.releaseDate,
  });

  factory ProjectModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectModelFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectModelToJson(this);
}
