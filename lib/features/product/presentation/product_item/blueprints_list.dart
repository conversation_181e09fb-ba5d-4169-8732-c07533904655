import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/files/data/models/file.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class BlueprintsList extends StatelessWidget {
  const BlueprintsList({super.key, required this.versions});

  final List<BlueprintVersion> versions;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final firstColor =
        isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary;
    final foreground =
        isDarkTheme ? AppColors.darkPrimary : AppColors.lightBackground;

    return ListView.separated(
      padding: const EdgeInsets.all(16.0),
      itemBuilder: (context, index) {
        final doc = versions[index];
        final isFirst = index == 0;

        return CustomCard(
          color: isFirst ? firstColor : null,
          onTap: () {
            if (doc.path == null) {
              return;
            }
            // final path = FilesRepository.getPath(
            //   doc.path!,
            // );
            context.router.push(PdfViewerRoute(pdfPath: doc.path!));
            // launchUrl(Uri.parse(path));
          },
          child: Row(children: [
            SVG(Assets.icons.description, color: isFirst ? foreground : null),
            const SizedBox(width: 12.0),
            Expanded(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      doc.name ?? 'NAME',
                      style: Fonts.labelMedium.merge(
                        TextStyle(color: isFirst ? foreground : null),
                      ),
                    ),
                    const SizedBox(height: 2.0),
                    Text(
                      'Версия: ${doc.version}',
                      style: Fonts.bodySmall.merge(
                        TextStyle(color: isFirst ? foreground : null),
                      ),
                    ),
                  ]),
            ),
          ]),
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: 12.0);
      },
      itemCount: versions.length,
    );
  }
}
