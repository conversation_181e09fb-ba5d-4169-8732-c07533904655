import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class StructureItemArrow extends StatelessWidget {
  const StructureItemArrow({
    super.key,
    required this.openAnimationController,
    required this.childrensCount,
  });

  final AnimationController openAnimationController;
  final int childrensCount;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final arrowUpAnimation = CurvedAnimation(
      parent: openAnimationController,
      curve: Curves.easeInOut,
    );

    return SizedBox(
      width: 24.0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          RotationTransition(
            turns: arrowUpAnimation.drive(Tween(begin: 0.25, end: 0.5)),
            child: SVG(Assets.icons.keyboardArrowUp),
          ),
          Text(
            '$childrensCount',
            style: Fonts.bodySmall.merge(TextStyle(
              color: isDarkTheme
                  ? AppColors.darkDescription
                  : AppColors.lightDescription,
              height: 1.0,
              fontSize: 12.0,
            )),
          ),
        ],
      ),
    );
  }
}
