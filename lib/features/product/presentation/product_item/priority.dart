import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class ProductItemPriority extends StatelessWidget {
  const ProductItemPriority({super.key, this.priority});

  final int? priority;

  @override
  Widget build(BuildContext context) {
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: 24,
      height: 24,
      decoration: const BoxDecoration(
          // borderRadius: BorderRadius.circular(1000.0),
          // border: Border.all(
          //   color: AppColors.lightSecondary,
          //   width: 2.0,
          // ),
          // color: isDarkTheme ? AppColors.darkSurface : AppColors.lightSurface,
          ),
      child: Text(
        priority.toString(),
        textAlign: TextAlign.center,
        style: Fonts.labelSmall.merge(
          const TextStyle(
            fontFamily: Fonts.mono,
            color: AppColors.lightSecondary,
            height: 1.5,
            fontVariations: [FontVariation.weight(600)],
          ),
        ),
      ),
    );
  }
}
