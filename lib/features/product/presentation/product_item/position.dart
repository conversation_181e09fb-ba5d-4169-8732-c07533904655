import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class StructureItemPosition extends StatelessWidget {
  const StructureItemPosition({
    super.key,
    required this.position,
  });

  final int position;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Row(
      children: List.generate(position, (index) {
        return Row(children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 24.0,
                height: 24.0,
                child: Divider(
                  height: 1.0,
                  color: isDarkTheme
                      ? AppColors.darkDescription.withValues(alpha: 0.1)
                      : AppColors.lightDescription.withValues(alpha: 0.1),
                ),
              ),
              if ((position - 1) == index)
                Positioned(
                  right: 0.0,
                  width: 1.0,
                  child: SizedBox(
                    height: 24.0,
                    width: 1.0,
                    child: VerticalDivider(
                      width: 1.0,
                      color: isDarkTheme
                          ? AppColors.darkDescription
                          : AppColors.lightDescription,
                    ),
                  ),
                ),
              if ((position - 1) == index)
                Positioned(
                  top: 0.0,
                  child: Text(
                    (index + 1).toString(),
                    style: Fonts.bodySmall.merge(
                      TextStyle(
                        fontSize: 12.0,
                        height: 1.0,
                        color: isDarkTheme
                            ? AppColors.darkSecondary.withValues(alpha: 0.33)
                            : AppColors.lightSecondary.withValues(alpha: 0.33),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 10.0),
        ]);
      }),
    );
  }
}
