import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/pick_file.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/files/data/repositories/index.dart';
import 'package:sphere/features/product/data/models/index.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/product/presentation/edit.dart';
import 'package:sphere/features/product/presentation/edit_parameters/edit_parameters.dart';
import 'package:sphere/features/product/presentation/product_item/arrow.dart';
import 'package:sphere/features/product/presentation/product_item/blueprints_list.dart';
import 'package:sphere/features/product/presentation/product_item/change_priority.dart';
import 'package:sphere/features/product/presentation/product_item/position.dart';
import 'package:sphere/features/product/presentation/product_item/priority.dart';
import 'package:sphere/features/structure/presentation/bloc/bloc.dart';
import 'package:sphere/features/structure/presentation/create_product.dart';
import 'package:sphere/features/tasks/presentation/add.dart';
import 'package:sphere/features/tasks/presentation/progress_task.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:sphere/shared/widgets/utility/skeleton.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

class ProductItem extends StatefulWidget {
  const ProductItem({
    super.key,
    this.isLoading,
    this.onTap,
    this.onSecondaryTapDown,
    this.position,
    required this.data,
    this.projectId,
    this.setProductData,
    this.refresher,
    this.onSelect,
    this.selecting,
    this.selectedProducts,
    this.forceSelected = false,
    this.copied,
  });

  final bool? isLoading;
  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final ProductModel data;
  final String? projectId;
  final int? position;
  final void Function(String, ProductModel)? setProductData;
  final void Function()? refresher;

  final void Function(ProductModel id)? onSelect;
  final bool? selecting;
  final List<ProductModel>? selectedProducts;
  final List<ProductModel>? copied;
  final bool forceSelected;

  @override
  State<ProductItem> createState() => _ProductItemState();
}

class _ProductItemState extends State<ProductItem>
    with TickerProviderStateMixin {
  ProductModel _innerData = ProductModel();
  bool _isLoading = false;
  bool _isOpened = false;
  List<ProductModel> _children = [];
  AnimationController? _openAnimationController;

  // tasks
  // final _commentController = TextEditingController();
  // final TaskStatus _newStatus = TaskStatus.inProgress;

  Future<void> _toggleOpen() async {
    if (_isLoading) return;
    if (!_isOpened) {
      await _openAnimationController!.forward();
      setState(() {
        _isOpened = true;
      });
      await _loadChildrens();
    } else {
      await _openAnimationController!.reverse();
      setState(() {
        _isOpened = false;
      });
    }
  }

  Future<void> _loadChildrens() async {
    setState(() {
      _isLoading = true;
    });

    final result = await ProductRepository.search(SearchModel(
      filters: SearchFiltersModel(
        parentProductId: _innerData.id,
      ),
    ));
    // await Future.delayed(const Duration(milliseconds: 200000));

    if (result?.data?.message != null) {
      ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
        text: result!.data!.message,
      ).toSnackBar(context));
      setState(() {
        _isLoading = false;
      });
      return;
    }
    setState(() {
      final data = result?.data ?? SearchProductOutputModel();
      if (data.items != null) {
        _children = [...data.items!];
      }
      _isLoading = false;
    });
  }

  // Future<void> _removeWorkers(List<UserModel> users) async {
  //   if (_innerData.id == null) return;
  //   final list =
  //       users.where((user) => user.id != null).map((user) => user.id!).toList();

  //   final result = await ProductRepository.removeWorkers(_innerData.id!, list);
  //   if (result?.data != null) {
  //     setState(() {
  //       _innerData = result!.data!;
  //     });
  //   }
  // }

  Future<void> _uploadBlueprint(File file) async {
    setState(() {
      _isLoading = true;
    });

    final result = await FilesRepository.uploadBlueprint(
      file: file,
      productId: _innerData.id,
    );

    // TODO: check, why the state is not updated
    setState(() {
      _isLoading = false;
      if (result?.data != null) {
        final newData = _innerData.copyWith(blueprint: result!.data);
        if (_innerData.id == null) return;
        widget.setProductData?.call(_innerData.id!, newData);
        _innerData = widget.data;
      }
      widget.refresher?.call();
    });
  }

  Future<void> _delete() async {
    if (_innerData.id == null) return;
    setState(() {
      _isLoading = true;
    });

    final result = await ProductRepository.delete(
      _innerData.id!,
    );

    setState(() {
      _isLoading = false;
      if (result?.data != null) {
        if (_innerData.id == null) return;
        widget.setProductData?.call(_innerData.id!, ProductModel());
        _innerData = widget.data;
      }
      widget.refresher?.call();
    });
  }

  Future<void> _changePriority(int priority) async {
    if (_innerData.id == null) return;
    setState(() {
      _isLoading = true;
    });

    final result =
        await ProductRepository.changePriority(_innerData.id!, priority);

    setState(() {
      if (result?.data?.name != null) _innerData = result!.data!;
      _isLoading = false;
    });
  }

  void _showChangePriority() {
    showDialog(
      context: context,
      builder: (context) {
        return ChangePriorityProductItem(
          oldPriority: _innerData.priority,
          onSave: _changePriority,
        );
      },
    );
  }

  // void _changeStatus(TaskStatus status) async {
  //   setState(() {
  //     _isLoading = true;
  //   });

  //   final result = await TasksRepository.updateTaskProgressStatus(
  //     UpdateTaskProgressStatusModel(
  //       taskProgressIds: widget.selectedProducts
  //               ?.expand((product) =>
  //                   product.progressTasks
  //                       ?.map((task) => task.id)
  //                       .whereType<String>() ??
  //                   [])
  //               .toList()
  //               .cast<String>() ??
  //           [],
  //       status: status,
  //       comment:
  //           _commentController.text.isNotEmpty ? _commentController.text : null,
  //     ),
  //   );

  //   setState(() {
  //     _isLoading = false;
  //   });
  // }

  Future<void> _paste() async {
    if (_innerData.id == null) return;
    setState(() {
      _isLoading = true;
    });
    final copied = widget.copied?.map((product) => product.id!).toList() ?? [];
    final selected =
        widget.selectedProducts?.map((product) => product.id!).toList() ?? [];

    final result = await ProductRepository.copy(
      productIds: copied,
      parentIds: selected.isNotEmpty ? selected : null,
    );
    if (result?.data != null) {
      // setState(() {
      //   _innerData = result!.data!;
      // });
      // widget.refresher?.call();
      _loadChildrens();
    }
    setState(() {
      _isLoading = false;
    });
    context.read<BlocStructure>().add(ClearSelectionsInStructure());
    context.read<BlocStructure>().add(SetCopiedInStructure([]));
  }

  @override
  void initState() {
    super.initState();
    _openAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _innerData = widget.data;
  }

  @override
  void didUpdateWidget(ProductItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      setState(() {
        _innerData = widget.data;
      });
    }
  }

  @override
  void dispose() {
    _openAnimationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = context.read<BlocStructure>().state;
    // Создаем Set из id выбранных продуктов
    final selectedProductIds = widget.selectedProducts
        ?.map((product) => product.id)
        .where((id) => id != null)
        .toSet();

    bool getIsSelected() {
      if (selectedProductIds == null || _innerData.id == null) {
        return false;
      }
      return selectedProductIds.contains(_innerData.id);
    }

    final isSelected = getIsSelected();
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    int getItemCount() {
      int result = 1;
      if (_isOpened) {
        if (_isLoading) {
          result += _innerData.rootChildrenCount ?? 0;
        } else {
          result += _children.length;
        }
      }
      return result;
    }

    final itemCount = getItemCount();

    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context, index) {
        if (index == 0) {
          return Skeleton(
              isEnabled: widget.isLoading ?? false,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _innerData.hasChildren == true ? _toggleOpen : null,
                  splashFactory: InkSparkle.splashFactory,
                  child: Ink(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        if (widget.position != null)
                          StructureItemPosition(position: widget.position!),
                        if (_innerData.hasChildren == true &&
                            _openAnimationController != null)
                          StructureItemArrow(
                            openAnimationController: _openAnimationController!,
                            childrensCount: _innerData.rootChildrenCount ?? 0,
                          ),
                        if (_innerData.hasChildren == true)
                          const SizedBox(width: 10.0),
                        Expanded(
                          child: Row(
                            children: [
                              if (widget.selecting == true)
                                SizedBox(
                                  width: 24.0,
                                  height: 24.0,
                                  child: Checkbox.adaptive(
                                    value: widget.forceSelected == true ||
                                        isSelected,
                                    onChanged: (_) {
                                      widget.onSelect?.call(_innerData);
                                    },
                                    visualDensity: VisualDensity.compact,
                                  ),
                                ),
                              if (widget.selecting == true)
                                const SizedBox(width: 10.0),
                              Tooltip(
                                message: 'Приоритет: ${_innerData.priority}',
                                child: GhostButton(
                                  onTap: _showChangePriority,
                                  child: ProductItemPriority(
                                    priority: _innerData.priority,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 10.0),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Wrap(children: [
                                      GhostButton(
                                        onTap: widget.onTap,
                                        onSecondaryTapDown: widget
                                                .onSecondaryTapDown ??
                                            (details) {
                                              CustomDropdownMenu.instance.show(
                                                context: context,
                                                items: [
                                                  // Условие 1: Если нет выбранных продуктов
                                                  if ((widget.selectedProducts ==
                                                              null ||
                                                          widget
                                                              .selectedProducts!
                                                              .isEmpty) &&
                                                      (widget.copied == null ||
                                                          widget.copied!
                                                              .isEmpty)) ...[
                                                    if (_innerData.type ==
                                                        ProductType.assembly)
                                                      CustomDropdownMenuItem(
                                                        icon: Assets.icons.add,
                                                        text: 'Добавить...',
                                                        description:
                                                            'В "${_innerData.name}"',
                                                        onTap: () {
                                                          CustomDropdownMenu
                                                              .instance
                                                              .hide();
                                                          CustomDrawer.instance
                                                              .show(
                                                            context: context,
                                                            vsync: this,
                                                            child:
                                                                CreateProductDrawer(
                                                              projectId: widget
                                                                  .projectId,
                                                              parentId:
                                                                  _innerData.id,
                                                              onSuccess: () {
                                                                widget.refresher
                                                                    ?.call();
                                                                _loadChildrens();
                                                              },
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    if (widget.data.type !=
                                                        ProductType.materials)
                                                      CustomDropdownMenuItem(
                                                        icon: Assets.icons.edit,
                                                        text: 'Редактировать',
                                                        onTap: () {
                                                          CustomDrawer.instance
                                                              .show(
                                                            context: context,
                                                            vsync: this,
                                                            child: ProductEdit(
                                                              product:
                                                                  _innerData,
                                                              onSave:
                                                                  (product) {
                                                                setState(() =>
                                                                    _innerData =
                                                                        product);
                                                                CustomDrawer
                                                                    .instance
                                                                    .hide();
                                                              },
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    CustomDropdownMenuItem(
                                                      icon: Assets.icons.edit,
                                                      text:
                                                          'Редактировать параметры',
                                                      onTap: () {
                                                        CustomDrawer.instance
                                                            .show(
                                                          context: context,
                                                          vsync: this,
                                                          child:
                                                              ProductEditParameters(
                                                            product: _innerData,
                                                            onSave: (product) {
                                                              setState(() =>
                                                                  _innerData =
                                                                      product);
                                                              CustomDrawer
                                                                  .instance
                                                                  .hide();
                                                            },
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                    CustomDropdownMenuItem(
                                                      icon: Assets
                                                          .icons.description,
                                                      text:
                                                          'Добавить новый чертёж',
                                                      description:
                                                          'Появится новая версия, если уже есть',
                                                      onTap: () async {
                                                        final file =
                                                            await pickFile();
                                                        if (file == null) {
                                                          return;
                                                        }
                                                        await _uploadBlueprint(
                                                            file);
                                                      },
                                                    ),
                                                    CustomDropdownMenuItem(
                                                      icon: Assets.icons.delete,
                                                      text: 'Удалить',
                                                      onTap: () {
                                                        CustomDropdownMenu
                                                            .instance
                                                            .hide();
                                                        showDialog(
                                                          context: context,
                                                          builder: (context) {
                                                            return AlertDialog(
                                                              title: const Text(
                                                                'Удаление изделия',
                                                                style: Fonts
                                                                    .titleSmall,
                                                              ),
                                                              content: Text(
                                                                'Вы уверены удалить изделие: "${_innerData.name}", а также все документы и дочерние продукты?',
                                                                style: Fonts
                                                                    .bodyMedium,
                                                              ),
                                                              actionsAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceAround,
                                                              actions: [
                                                                TextButton(
                                                                  onPressed: () =>
                                                                      context
                                                                          .router
                                                                          .popForced(),
                                                                  child:
                                                                      const Text(
                                                                    'Отмена',
                                                                    style: Fonts
                                                                        .labelMedium,
                                                                  ),
                                                                ),
                                                                TextButton(
                                                                  onPressed:
                                                                      () {
                                                                    context
                                                                        .router
                                                                        .popForced();
                                                                    _delete();
                                                                  },
                                                                  child: Text(
                                                                    'Да, удалить',
                                                                    style: Fonts
                                                                        .labelMedium
                                                                        .merge(
                                                                      TextStyle(
                                                                        color: isDarkTheme
                                                                            ? AppColors.darkError
                                                                            : AppColors.lightError,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            );
                                                          },
                                                        );
                                                      },
                                                    ),
                                                  ],

                                                  // Условие 2: Если есть выбранные продукты, но нет скопированных
                                                  if ((widget.selectedProducts !=
                                                              null &&
                                                          widget
                                                              .selectedProducts!
                                                              .isNotEmpty) &&
                                                      (widget.copied == null ||
                                                          widget.copied!
                                                              .isEmpty)) ...[
                                                    CustomDropdownMenuItem(
                                                      icon: Assets.icons.copy,
                                                      text: 'Копировать',
                                                      onTap: () {
                                                        CustomDropdownMenu
                                                            .instance
                                                            .hide();
                                                        context
                                                            .read<
                                                                BlocStructure>()
                                                            .add(
                                                              SetCopiedInStructure(
                                                                  widget.selectedProducts ??
                                                                      []),
                                                            );
                                                        context
                                                            .read<
                                                                BlocStructure>()
                                                            .add(
                                                                ClearSelectionsInStructure());
                                                      },
                                                    ),
                                                    CustomDropdownMenuItem(
                                                      icon: Assets
                                                          .icons.personAdd,
                                                      text: 'Добавить задачу',
                                                      onTap: () {
                                                        CustomDropdownMenu
                                                            .instance
                                                            .hide();
                                                        CustomDrawer.instance
                                                            .show(
                                                          context: context,
                                                          vsync: this,
                                                          child: AddTaskDrawer(
                                                            projectId: widget
                                                                .projectId,
                                                            productId:
                                                                _innerData.id,
                                                            onSuccess: () {
                                                              if (_innerData
                                                                      .id ==
                                                                  null) {
                                                                return;
                                                              }
                                                              _loadChildrens();
                                                              widget.refresher
                                                                  ?.call();
                                                            },
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                    // CustomDropdownMenuItem(
                                                    //   icon: Assets.icons.edit,
                                                    //   text: 'Изменить состояние подзадачи',
                                                    //   onTap: () {
                                                    //     CustomDropdownMenu.instance.hide();
                                                    //     _showChangeStatusDialog();
                                                    //   },
                                                    // ),
                                                  ],

                                                  // Условие 3: Если есть скопированные данные и выделенные
                                                  if (widget.selectedProducts !=
                                                          null &&
                                                      widget.selectedProducts!
                                                          .isNotEmpty &&
                                                      widget.copied != null &&
                                                      widget
                                                          .copied!.isNotEmpty &&
                                                      _innerData.type ==
                                                          ProductType.assembly)
                                                    CustomDropdownMenuItem(
                                                      icon: Assets.icons.paste,
                                                      text: 'Вставить',
                                                      onTap: () {
                                                        CustomDropdownMenu
                                                            .instance
                                                            .hide();
                                                        _paste();
                                                      },
                                                    ),

                                                  // Общие элементы (без условий)
                                                  CustomDropdownMenuItem(
                                                    disabled: true,
                                                    icon: Assets
                                                        .icons.description,
                                                    text: 'Согласовать',
                                                    onTap: () {},
                                                  ),

                                                  CustomDropdownMenuItem(
                                                    disabled: true,
                                                    icon: Assets
                                                        .icons.notifications,
                                                    text:
                                                        'Подписаться на изменения',
                                                    onTap: () {},
                                                  ),
                                                ],
                                                position:
                                                    details.globalPosition,
                                              );
                                            },
                                        backgroundOffset: const Offset(-6, -4),
                                        borderRadius: 8.0,
                                        color: isDarkTheme
                                            ? AppColors.darkPrimary
                                            : AppColors.lightPrimary,
                                        child: Text(
                                          _innerData.name ?? '',
                                          style: Fonts.labelMedium,
                                        ),
                                      ),
                                      const SizedBox(width: 12.0),
                                      Text(
                                        'x${_innerData.parameters?.quantity ?? 1}',
                                        style: Fonts.bodyMedium.merge(
                                          TextStyle(
                                            color: isDarkTheme
                                                ? AppColors.darkDescription
                                                : AppColors.lightDescription,
                                          ),
                                        ),
                                      ),
                                      if (_innerData.department != null)
                                        SizedBox(width: 12.0),
                                      if (_innerData.department != null)
                                        Text(
                                          _innerData.department!.getName(),
                                          style: Fonts.labelSmall.merge(
                                            TextStyle(
                                              color: isDarkTheme
                                                  ? AppColors.darkSecondary
                                                  : AppColors.lightSecondary,
                                            ),
                                          ),
                                        ),
                                    ]),
                                    if (_innerData.number != null)
                                      const SizedBox(height: 4.0),
                                    if (_innerData.number != null)
                                      Text(
                                        _innerData.number!,
                                        style: Fonts.bodyMedium.merge(
                                          TextStyle(
                                            height: 1.2,
                                            color: isDarkTheme
                                                ? AppColors.darkSecondary
                                                : AppColors.lightSecondary,
                                          ),
                                        ),
                                      ),
                                    if (_innerData.type != null)
                                      Text(
                                        _innerData.type!.getName(),
                                        style: Fonts.bodyMedium.merge(
                                          TextStyle(
                                            height: 1.2,
                                            color: isDarkTheme
                                                ? AppColors.darkDescription
                                                : AppColors.lightDescription,
                                          ),
                                        ),
                                      ),
                                    if (_innerData.progressTasks != null &&
                                        _innerData.progressTasks!.isNotEmpty)
                                      const SizedBox(height: 12.0),
                                    if (_innerData.progressTasks != null &&
                                        _innerData.progressTasks!.isNotEmpty)
                                      Wrap(
                                        spacing: 16.0,
                                        runSpacing: 16.0,
                                        children: List.generate(
                                            _innerData.progressTasks!.length,
                                            (index) {
                                          final progressTask =
                                              _innerData.progressTasks![index];

                                          final selectedFromInitial = state
                                              .selectedProgressTasks
                                              .any((progressTaskSelected) {
                                            return progressTaskSelected.id ==
                                                progressTask.id;
                                          });

                                          final selectedFromParent = state
                                              .selectedProgressTasks
                                              .any((selectedProgressTask) {
                                            return progressTask
                                                    .parentProgressTaskIds
                                                    ?.contains(
                                                        selectedProgressTask
                                                            .id) ??
                                                false;
                                          });

                                          final selected =
                                              selectedFromInitial ||
                                                  selectedFromParent;

                                          return ProgressTask(
                                            progressTask: progressTask,
                                            refresher: widget.refresher,
                                            progressTaskForceSelected: false,
                                            selected: selected,
                                          );
                                        }),
                                      ),
                                  ],
                                ),
                              ),
                              if (widget.data.blueprint != null &&
                                  (widget.data.blueprint?.versions?.length ??
                                          0) >
                                      0)
                                Row(children: [
                                  GhostButton(
                                    backgroundOffset: const Offset(-6.0, -6.0),
                                    onTap: () {
                                      if (widget.data.blueprint?.path == null) {
                                        return;
                                      }
                                      // final path = FilesRepository.getPath(
                                      //   widget.data.blueprint!.path!,
                                      // );
                                      context.router.push(
                                        PdfViewerRoute(
                                          pdfPath: widget.data.blueprint!.path!,
                                        ),
                                      );
                                      // launchUrl(Uri.parse(path));
                                    },
                                    child: SVG(
                                      Assets.icons.description,
                                      color: isDarkTheme
                                          ? AppColors.darkSecondary
                                          : AppColors.lightSecondary,
                                    ),
                                  ),
                                  if ((widget
                                          .data.blueprint!.versions!.length) >
                                      1)
                                    const SizedBox(width: 18.0),
                                  if ((widget
                                          .data.blueprint!.versions!.length) >
                                      1)
                                    GhostButton(
                                      backgroundOffset:
                                          const Offset(-6.0, -6.0),
                                      color: isDarkTheme
                                          ? AppColors.darkDescription
                                          : AppColors.lightDescription,
                                      onTap: () {
                                        CustomDrawer.instance.show(
                                          context: context,
                                          vsync: this,
                                          child: BlueprintsList(
                                            versions: widget.data.blueprint!
                                                .versions!.reversed
                                                .toList(),
                                          ),
                                        );
                                      },
                                      child: Row(
                                        children: [
                                          SVG(
                                            Assets.icons.description,
                                            color: isDarkTheme
                                                ? AppColors.darkDescription
                                                : AppColors.lightDescription,
                                          ),
                                          Text(
                                            'x${widget.data.blueprint!.versions!.length - 1}',
                                            style: Fonts.bodyMedium.merge(
                                              TextStyle(
                                                height: 1.2,
                                                color: isDarkTheme
                                                    ? AppColors.darkDescription
                                                    : AppColors
                                                        .lightDescription,
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  const SizedBox(width: 6.0),
                                ]),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ));
        } else {
          final childIndex = index - 1;
          ProductModel data = _isLoading
              ? ProductModel(
                  name: 'Loading...',
                  description: 'Loading...',
                  type: ProductType.other,
                )
              : _children[childIndex];
          return ProductItem(
            isLoading: _isLoading,
            onTap: () {
              context.router.pushNamed(
                '/project/${widget.projectId}/product/${data.id}',
              );
            },
            copied: widget.copied,
            projectId: widget.projectId,
            data: data,
            position: (widget.position ?? 0) + 1,
            refresher: _loadChildrens,
            selectedProducts: widget.selectedProducts,
            onSelect: isSelected ? null : widget.onSelect,
            selecting: widget.selecting,
            forceSelected: isSelected || widget.forceSelected == true,
          );
        }
      },
      separatorBuilder: (context, index) => const Divider(
        height: 1.0,
      ),
      itemCount: itemCount,
    );
  }
}
