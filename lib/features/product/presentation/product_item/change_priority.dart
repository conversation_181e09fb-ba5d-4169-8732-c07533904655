import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class ChangePriorityProductItem extends StatefulWidget {
  const ChangePriorityProductItem({
    super.key,
    this.productId,
    this.oldPriority,
    this.onSave,
  });

  final String? productId;
  final int? oldPriority;
  final Function(int priority)? onSave;

  @override
  State<ChangePriorityProductItem> createState() =>
      _ChangePriorityProductItemState();
}

class _ChangePriorityProductItemState extends State<ChangePriorityProductItem> {
  final _priorityController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.oldPriority != null) {
      _priorityController.text = widget.oldPriority.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return AlertDialog(
      backgroundColor:
          isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground,
      title: const Text(
        'Изменение приоритета',
        style: Fonts.titleSmall,
      ),
      content: TextField(
        controller: _priorityController,
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        decoration: const InputDecoration(
          hintText: 'Приоритет',
        ),
        style: Fonts.labelSmall,
      ),
      actions: [
        TextButton(
          onPressed: () {
            context.router.popForced();
          },
          child: const Text(
            'Отмена',
            style: Fonts.labelSmall,
          ),
        ),
        TextButton(
          onPressed: () {
            widget.onSave?.call(int.tryParse(_priorityController.text) ?? 5);
            context.router.popForced();
          },
          child: const Text(
            'Сохранить',
            style: Fonts.labelSmall,
          ),
        ),
      ],
    );
  }
}
