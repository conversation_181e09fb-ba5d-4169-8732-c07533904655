import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class ProductRemoveWorkersDrawer extends StatefulWidget {
  const ProductRemoveWorkersDrawer({
    super.key,
    this.productId,
    this.onSuccess,
  });

  final String? productId;
  final void Function(ProductModel)? onSuccess;

  @override
  State<ProductRemoveWorkersDrawer> createState() =>
      _ProductRemoveWorkersDrawerState();
}

class _ProductRemoveWorkersDrawerState
    extends State<ProductRemoveWorkersDrawer> {
  final ScrollController _scrollController = ScrollController();
  bool _showTopGradient = false;
  bool _showBottomGradient = true;
  List<UserModel> _users = [
    const UserModel(),
    const UserModel(),
    const UserModel(),
  ];
  final List<UserModel> _selectedUsers = [];
  bool _isLoading = false;
  final _searchController = TextEditingController(text: '');

  final _debouncer = Debouncer();

  void _loadUsers() async {
    setState(() {
      _isLoading = true;
    });
    final result = await UserRepository.search(SearchModel(
      filters: SearchFiltersModel(
        // TODO: add filters
        // department: Department.ogk,
        query: _searchController.text == '' ? null : _searchController.text,
        productId: widget.productId,
        roles: [UserRole.worker],
      ),
    ));

    setState(() {
      if (result?.data != null && result!.data?.items != null) {
        final users = result.data!.items!;
        _users = users;
      }
      _isLoading = false;
    });
  }

  void _onSelectUser(UserModel user) {
    setState(() {
      if (_selectedUsers.contains(user)) {
        _selectedUsers.remove(user);
      } else {
        _selectedUsers.add(user);
      }
    });
  }

  void _removeSelectedUsers() async {
    setState(() {
      _isLoading = true;
    });
    if (widget.productId == null) {
      print('product id is not found');
      return;
    }
    final selectedUsersIds = _selectedUsers
        .where((user) => user.id != null)
        .map((user) => user.id!)
        .toList();

    final result = await ProductRepository.removeWorkers(
      widget.productId!,
      selectedUsersIds,
    );
    if (result?.data == null) return;
    widget.onSuccess?.call(result!.data!);

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
    _searchController.addListener(_debouncer.run(_loadUsers));
    _loadUsers();
  }

  void _handleScroll() {
    setState(() {
      _showTopGradient = _scrollController.offset > 0;
      _showBottomGradient = _scrollController.position.pixels <
          _scrollController.position.maxScrollExtent;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Calculate total item count for ListView.builder
  int _getTotalItemCount() {
    final selectedUsersCount = _selectedUsers.length;
    final availableUsersCount =
        _users.where((user) => !_selectedUsers.contains(user)).length;

    // Base items: initial spacing + selected users + available users
    int total = 1 + selectedUsersCount + availableUsersCount;

    // Add divider and spacing if there are selected users
    if (selectedUsersCount > 0) {
      total += 2; // Divider + spacing
    }

    return total;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0),
            child: Text(
              'Удаление работников',
              style: Fonts.titleMedium.merge(
                const TextStyle(height: 1.6),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: TextField(
              controller: _searchController,
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                hintText: 'Поиск',
                suffixIcon: Align(
                  widthFactor: 1.0,
                  alignment: Alignment.center,
                  child: SVG(
                    Assets.icons.search,
                    color: AppColors.medium,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: ShaderMask(
              shaderCallback: (Rect rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    if (_showTopGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                    isDarkTheme
                        ? AppColors.darkBackground.withValues(alpha: 0)
                        : AppColors.lightBackground.withValues(alpha: 0),
                    isDarkTheme
                        ? AppColors.darkBackground.withValues(alpha: 0)
                        : AppColors.lightBackground.withValues(alpha: 0),
                    if (_showBottomGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                  ],
                  stops: const [0.0, 0.05, 0.95, 1.0],
                ).createShader(rect);
              },
              blendMode: BlendMode.dstOut,
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                itemCount: _getTotalItemCount(),
                itemBuilder: (context, index) {
                  if (index == 0) {
                    return const SizedBox(height: 12.0);
                  }

                  final adjustedIndex = index - 1;
                  final selectedUsersCount = _selectedUsers.length;

                  if (adjustedIndex < selectedUsersCount) {
                    // Selected users section
                    final user = _selectedUsers[adjustedIndex];
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        UserCard(
                          selected: true,
                          isLoading: _isLoading,
                          onTap: () {
                            _onSelectUser(user);
                          },
                          user: user,
                        ),
                        const SizedBox(height: 12.0),
                      ],
                    );
                  } else if (adjustedIndex == selectedUsersCount &&
                      selectedUsersCount > 0) {
                    // Divider after selected users
                    return const Divider(height: 1.0);
                  } else if (adjustedIndex == selectedUsersCount + 1 &&
                      selectedUsersCount > 0) {
                    // Spacing after divider
                    return const SizedBox(height: 12.0);
                  } else {
                    // Available users section
                    final availableUsers = _users
                        .where((user) => !_selectedUsers.contains(user))
                        .toList();
                    final userIndex = adjustedIndex -
                        selectedUsersCount -
                        (selectedUsersCount > 0 ? 2 : 0);

                    if (userIndex < availableUsers.length) {
                      final user = availableUsers[userIndex];
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          UserCard(
                            isLoading: _isLoading,
                            onTap: () {
                              _onSelectUser(user);
                            },
                            user: user,
                          ),
                          const SizedBox(height: 12.0),
                        ],
                      );
                    }
                  }

                  return const SizedBox.shrink();
                },
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: CustomElevatedButton(
              type: CustomElevatedButtonTypes.attention,
              onPressed: _removeSelectedUsers,
              text: 'Удалить исполнителей',
            ),
          )
        ],
      ),
    ]);
  }
}
