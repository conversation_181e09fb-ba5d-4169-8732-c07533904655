import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class ProductEditOperationsBody extends StatefulWidget {
  const ProductEditOperationsBody({
    super.key,
    required this.product,
    this.refresher,
  });

  final ProductModel product;
  final VoidCallback? refresher;

  @override
  State<ProductEditOperationsBody> createState() =>
      _ProductEditOperationsBodyState();
}

class _ProductEditOperationsBodyState extends State<ProductEditOperationsBody> {
  bool _isLoading = false;
  final List<TextEditingController> _controllers = [];

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _initialize() {
    _controllers.addAll(widget.product.operations
            ?.map((op) => TextEditingController(text: op)) ??
        []);
  }

  Future<void> _editOperations() async {
    if (widget.product.id == null) return;

    setState(() => _isLoading = true);

    try {
      final result = await ProductRepository.editOperations(
        productId: widget.product.id!,
        operations: _controllers.map((c) => c.text).toList(),
      );
      Logger().d(result?.data);
      widget.refresher?.call();
      CustomDrawer.instance.hide();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _addOperation() {
    setState(() {
      _controllers.add(TextEditingController(text: ''));
    });
  }

  void _removeOperation(int index) {
    setState(() {
      _controllers[index].dispose();
      _controllers.removeAt(index);
    });
  }

  void _onReorder(int oldIndex, int newIndex) {
    if (newIndex > oldIndex) {
      newIndex -= 1; // Корректируем индекс, если элемент перемещается вниз
    }
    setState(() {
      final controller = _controllers.removeAt(oldIndex);
      _controllers.insert(newIndex, controller);
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(
      children: [
        if (_isLoading) LoadingIndicator(isDarkTheme: isDarkTheme),
        Column(
          children: [
            Expanded(
              child: ContentSection(
                controllers: _controllers,
                addOperation: _addOperation,
                removeOperation: _removeOperation,
                onReorder: _onReorder,
              ),
            ),
            SaveButton(onPressed: _editOperations),
          ],
        ),
      ],
    );
  }
}

class ContentSection extends StatelessWidget {
  const ContentSection({
    super.key,
    required this.controllers,
    required this.addOperation,
    required this.removeOperation,
    required this.onReorder,
  });

  final List<TextEditingController> controllers;
  final VoidCallback addOperation;
  final void Function(int index) removeOperation;
  final void Function(int oldIndex, int newIndex) onReorder;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Изменение технологической карты',
            style: Fonts.titleMedium.merge(const TextStyle(height: 1.6)),
          ),
          const SizedBox(height: 24.0),
          Expanded(
            child: OperationsSection(
              controllers: controllers,
              addOperation: addOperation,
              removeOperation: removeOperation,
              onReorder: onReorder,
            ),
          ),
        ],
      ),
    );
  }
}

class OperationsSection extends StatelessWidget {
  const OperationsSection({
    super.key,
    required this.controllers,
    required this.addOperation,
    required this.removeOperation,
    required this.onReorder,
  });

  final List<TextEditingController> controllers;
  final VoidCallback addOperation;
  final void Function(int index) removeOperation;
  final void Function(int oldIndex, int newIndex) onReorder;

  @override
  Widget build(BuildContext context) {
    return ReorderableListView(
      onReorder: onReorder,
      footer: AddOperationButton(onPressed: addOperation),
      buildDefaultDragHandles: false,
      children: [
        for (var i = 0; i < controllers.length; i++)
          OperationItem(
            key: Key(i.toString()),
            index: i,
            controller: controllers[i],
            removeOperation: removeOperation,
          ),
      ],
    );
  }
}

class OperationItem extends StatelessWidget {
  const OperationItem({
    super.key,
    required this.index,
    required this.controller,
    required this.removeOperation,
  });

  final int index;
  final TextEditingController controller;
  final void Function(int index) removeOperation;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        spacing: 8.0,
        children: [
          Expanded(
            child: TextField(
              controller: controller,
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                labelText: '${index + 1}',
              ),
            ),
          ),
          Expanded(
            child: Opacity(
              opacity: 0.5,
              child: TextField(
                enabled: false,
                style: Fonts.labelSmall,
                decoration: InputDecoration(
                  labelText: 'н/ч',
                ),
              ),
            ),
          ),
          IconButton(
            icon: SVG(Assets.icons.delete),
            onPressed: () => removeOperation(index),
          ),
          ReorderableDragStartListener(
            index: index,
            child: const Icon(Icons.drag_handle_rounded),
          ),
        ],
      ),
    );
  }
}

class AddOperationButton extends StatelessWidget {
  const AddOperationButton({super.key, required this.onPressed});

  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 8.0),
        CustomElevatedButton(
          type: CustomElevatedButtonTypes.common,
          onPressed: onPressed,
          text: 'Добавить операцию',
        ),
      ],
    );
  }
}

class LoadingIndicator extends StatelessWidget {
  const LoadingIndicator({super.key, required this.isDarkTheme});

  final bool isDarkTheme;

  @override
  Widget build(BuildContext context) {
    return LinearProgressIndicator(
      minHeight: 2.0,
      valueColor: AlwaysStoppedAnimation(
        isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
      ),
    );
  }
}

class SaveButton extends StatelessWidget {
  const SaveButton({super.key, required this.onPressed});

  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: CustomElevatedButton(
        type: CustomElevatedButtonTypes.accent,
        onPressed: onPressed,
        text: 'Сохранить',
      ),
    );
  }
}
