import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class FeatureTypesBlock extends StatelessWidget {
  const FeatureTypesBlock({
    super.key,
    required this.selectedFeatureTypes,
    required this.onAddFeatureType,
    required this.onDeleteFeatureType,
  });

  final List<ParametersFeatureType> selectedFeatureTypes;
  final void Function(ParametersFeatureType) onAddFeatureType;
  final void Function(int) onDeleteFeatureType;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 4.0,
      runSpacing: 4.0,
      children: [
        ...selectedFeatureTypes.asMap().entries.map((entry) {
          final index = entry.key;
          final selectedFeatureType = entry.value;

          return CustomChip(
            onTap: () => onDeleteFeatureType(index),
            text: selectedFeatureType.getName(),
            deleteIcon: SVG(Assets.icons.close),
          );
        }),
        CustomChip(
          onTapDown: (details) {
            CustomDropdownMenu.instance.show(
              context: context,
              items: ParametersFeatureType.values.map((value) {
                return CustomDropdownMenuItem(
                  text: value.getName(),
                  onTap: () {
                    onAddFeatureType(value);
                    CustomDropdownMenu.instance.hide();
                  },
                );
              }).toList(),
              position: details.globalPosition,
            );
          },
          child: SVG(Assets.icons.add),
        ),
      ],
    );
  }
}
