import 'package:flutter/material.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/repositories/index.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/product/presentation/edit_parameters/feature_types_block.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/dropdown_button.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class ProductEditParameters extends StatefulWidget {
  const ProductEditParameters({super.key, this.product, this.onSave});

  final ProductModel? product;
  final void Function(ProductModel)? onSave;

  @override
  State<ProductEditParameters> createState() => _ProductEditParametersState();
}

class _ProductEditParametersState extends State<ProductEditParameters> {
  final _massController = TextEditingController();
  final _widthController = TextEditingController();
  final _lengthController = TextEditingController();
  final _heightController = TextEditingController();
  final _quantityController = TextEditingController();
  final _materialController = TextEditingController();
  final _itemRequirementsController = TextEditingController();
  final _materialRequirementsController = TextEditingController();
  final _pureMassController = TextEditingController();
  final _surfaceCoatingController = TextEditingController();
  final _noteController = TextEditingController();
  // final bool _calendarIsVisible = false;
  // final DateTime _releaseDate = DateTime.now();
  List<ParametersFeatureType> _selectedFeatureTypes = [];
  String? _selectedMaterialId;
  List<NomenclatureModel> materialFromSearch = [];
  bool _unitTypeDropdownIsOpened = false;
  Future<void> Function()? toggleDropdown;
  Future<void> Function()? toggleUnitTypeDropdown;
  UnitType _selectedUnitType = UnitType.kg;
  final _materialQuantityController = TextEditingController();
  // TODO: without nomenclature
  NomenclatureModel? _selectedNomenclature;
  // final List<NomenclatureModel> _materialsFromSearch = [];

  ProductModel _product = ProductModel();

  final _debouncer = Debouncer();

  bool _isLoading = false;

  Future<void> _save() async {
    if (widget.product?.id == null) return;
    setState(() {
      _isLoading = true;
    });

    final result = await ProductRepository.updateParameters(
      widget.product!.id!,
      ParametersModel(
        mass: double.tryParse(_massController.text.replaceAll(r',', '.')),
        width: double.tryParse(_widthController.text.replaceAll(r',', '.')),
        length: double.tryParse(_lengthController.text.replaceAll(r',', '.')),
        height: double.tryParse(_heightController.text.replaceAll(r',', '.')),
        quantity: widget.product?.type == ProductType.materials
            ? double.tryParse(
                _materialQuantityController.text.replaceAll(r',', '.'))
            : double.tryParse(_quantityController.text),
        materialName: _selectedMaterialId == null
            ? _materialController.text.isEmpty
                ? null
                : _materialController.text
            : null,
        itemRequirements: _itemRequirementsController.text,
        materialRequirements: _materialRequirementsController.text,
        featureTypes: _selectedFeatureTypes,
        materialId: _selectedMaterialId,
        pureMass:
            double.tryParse(_pureMassController.text.replaceAll(r',', '.')),
        surfaceCoating: _surfaceCoatingController.text,
        note: _noteController.text,
        unitType: widget.product?.type == ProductType.materials
            ? _selectedUnitType
            : null,
        // releaseDate: _releaseDate,
        // materialQuantity: widget.product?.type == ProductType.materials
        //     ? double.tryParse(
        //         _materialQuantityController.text.replaceAll(r',', '.'))
        //     : null,
      ),
    );

    setState(() {
      _product = result?.data ?? ProductModel();
      _isLoading = false;
    });
  }

  Future<void> _searchMaterial() async {
    if (_materialController.text.characters.isEmpty) {
      materialFromSearch.clear();
      return;
    }
    setState(() {
      _isLoading = true;
    });

    final result = await NomenclaturesRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _materialController.text,
      ),
    ));

    setState(() {
      if (result?.data?.items != null) {
        materialFromSearch = result!.data!.items!;
      }
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    if (widget.product?.parameters != null) {
      final params = widget.product!.parameters;

      if (params!.mass != null) _massController.text = params.mass.toString();
      if (params.width != null) _widthController.text = params.width.toString();
      if (params.length != null) {
        _lengthController.text = params.length.toString();
      }
      if (params.height != null) {
        _heightController.text = params.height.toString();
      }
      if (params.quantity != null) {
        _quantityController.text = params.quantity.toString();
      }
      if (params.materialName != null) {
        _materialController.text = params.materialName ?? '';
      }
      if (params.materialId != null) _selectedMaterialId = params.materialId;
      if (params.itemRequirements != null) {
        _itemRequirementsController.text = params.itemRequirements ?? '';
      }
      if (params.materialRequirements != null) {
        _materialRequirementsController.text =
            params.materialRequirements ?? '';
      }
      if (params.featureTypes != null) {
        _selectedFeatureTypes = [...(params.featureTypes ?? [])];
      }
      if (params.pureMass != null) {
        _pureMassController.text = params.pureMass.toString();
      }
      if (params.surfaceCoating != null) {
        _surfaceCoatingController.text = params.surfaceCoating.toString();
      }
      if (params.note != null) {
        _noteController.text = params.note.toString();
      }
      if (params.unitType != null) {
        _selectedUnitType = params.unitType!;
      }
      // if (params.materialQuantity != null) {
      //   _materialQuantityController.text = params.materialQuantity.toString();
      // }
      if (params.material != null) {
        _selectedNomenclature = params.material;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(children: [
        Expanded(
          child: ListView(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0.0),
            children: [
              Text(
                'Редактирование основных параметров',
                style: Fonts.titleMedium.merge(
                  const TextStyle(height: 1.6),
                ),
              ),
              if (widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard &&
                  widget.product?.type != ProductType.materials)
                const SizedBox(height: 24.0),
              if (widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard &&
                  widget.product?.type != ProductType.materials)
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _massController,
                        style: Fonts.labelSmall,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText:
                              'Масса ${widget.product?.type != ProductType.assembly ? 'заготовки' : 'изделия'}',
                        ),
                      ),
                    ),
                    if (widget.product?.type == ProductType.part)
                      const SizedBox(width: 10.0),
                    if (widget.product?.type == ProductType.part)
                      Expanded(
                        child: TextField(
                          controller: _pureMassController,
                          style: Fonts.labelSmall,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'Чистый вес детали',
                          ),
                        ),
                      ),
                    const SizedBox(width: 10.0),
                    Text(
                      'кг',
                      style: Fonts.bodyMedium.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        ),
                      ),
                    ),
                  ],
                ),
              if (widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard &&
                  widget.product?.type != ProductType.materials)
                const SizedBox(height: 24.0),
              if (widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard &&
                  widget.product?.type != ProductType.materials)
                Row(children: [
                  Expanded(
                    child: TextField(
                      controller: _widthController,
                      style: Fonts.labelSmall,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Ширина',
                      ),
                    ),
                  ),
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: TextField(
                      controller: _lengthController,
                      style: Fonts.labelSmall,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Длина',
                      ),
                    ),
                  ),
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: TextField(
                      controller: _heightController,
                      style: Fonts.labelSmall,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Высота (диаметр)',
                      ),
                    ),
                  ),
                  const SizedBox(width: 10.0),
                  Text(
                    'мм',
                    style: Fonts.bodyMedium.merge(
                      TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                      ),
                    ),
                  ),
                ]),
              // const SizedBox(height: 24.0),

              const SizedBox(height: 16.0),
              Text(
                'Количество${widget.product?.type == ProductType.assembly ? ' сборок' : widget.product?.type == ProductType.materials ? '' : ' деталей в сборке'}',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 10.0),
              Row(children: [
                if (widget.product?.type != ProductType.materials)
                  Expanded(
                    child: TextField(
                      controller: _quantityController,
                      style: Fonts.labelSmall,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: 'Количество',
                      ),
                    ),
                  ),
                if (widget.product?.type != ProductType.materials)
                  const SizedBox(width: 12.0),
                if (widget.product?.type != ProductType.materials)
                  Text(
                    'шт',
                    style: Fonts.bodyMedium.merge(
                      TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                      ),
                    ),
                  ),
                if (widget.product?.type == ProductType.materials)
                  Expanded(
                    child: TextField(
                      controller: _materialQuantityController,
                      style: Fonts.labelSmall,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: 'Количество материала',
                      ),
                    ),
                  ),
                if (widget.product?.type == ProductType.materials)
                  const SizedBox(width: 12.0),
                if (widget.product?.type == ProductType.materials)
                  CustomDropDownButton(
                      isOpened: _unitTypeDropdownIsOpened,
                      setIsOpened: (value) {
                        setState(() {
                          _unitTypeDropdownIsOpened = value;
                        });
                      },
                      onToggleOpen: (toggleFunction) {
                        toggleUnitTypeDropdown = toggleFunction;
                      },
                      text: _selectedUnitType.getName(),
                      children: [
                        if (_selectedNomenclature?.baseUnit != null)
                          CustomDropdownMenuItem(
                            text: _selectedNomenclature!.baseUnit!.getName(),
                            onTap: () {
                              toggleUnitTypeDropdown?.call();
                              setState(() {
                                _selectedUnitType =
                                    _selectedNomenclature!.baseUnit!;
                              });
                            },
                          ),
                        ...(_selectedNomenclature?.alternativeUnits
                                    ?.map((unit) => unit.unit)
                                    .whereType<UnitType>() ??
                                UnitType.values)
                            .map((value) {
                          return CustomDropdownMenuItem(
                            text: value.getName(),
                            onTap: () {
                              toggleUnitTypeDropdown?.call();
                              setState(() {
                                _selectedUnitType = value;
                              });
                            },
                          );
                        })
                      ]),
              ]),
              if (widget.product?.type == ProductType.part)
                const SizedBox(height: 24.0),
              if (widget.product?.type == ProductType.part)
                Text(
                  'Признаки',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
              if (widget.product?.type == ProductType.part)
                const SizedBox(height: 10.0),
              if (widget.product?.type == ProductType.part)
                FeatureTypesBlock(
                  selectedFeatureTypes: _selectedFeatureTypes,
                  onAddFeatureType: (value) {
                    setState(() {
                      _selectedFeatureTypes.add(value);
                    });
                  },
                  onDeleteFeatureType: (index) {
                    setState(() {
                      _selectedFeatureTypes.removeAt(index);
                    });
                  },
                ),
              // Row(
              //   children: [
              //     CustomDropDownButton(
              //       text: _selectedFeatureType.getName(),
              //       children: ParametersFeatureType.values.map((value) {
              //         return CustomDropdownMenuItem(
              //           text: value.getName(),
              //           onTap: () {
              //             setState(() {
              //               _selectedFeatureType = value;
              //             });
              //             CustomDropdownMenu.instance.hide();
              //           },
              //         );
              //       }).toList(),
              //     ),
              //   ],
              // ),
              if (widget.product?.type == ProductType.part)
                const SizedBox(height: 24.0),
              if (widget.product?.type == ProductType.part)
                Text(
                  'Материал',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
              if (widget.product?.type == ProductType.part)
                const SizedBox(height: 10.0),
              if (widget.product?.type == ProductType.part)
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _materialController,
                        style: Fonts.labelSmall,
                        onChanged: (_) {
                          setState(() {
                            _selectedMaterialId = null;
                          });
                          _debouncer.run(_searchMaterial);
                        },
                        decoration: InputDecoration(
                          hintText: _selectedMaterialId != null
                              ? widget.product?.parameters?.material?.name ??
                                  ' (уже выбран)'
                              : 'Название материала',
                        ),
                      ),
                    ),
                  ],
                ),
              if (widget.product?.type == ProductType.part)
                const SizedBox(height: 6.0),
              if (widget.product?.type == ProductType.part)
                Column(
                    children: materialFromSearch.map((material) {
                  return ListTile(
                    title: Text(
                      material.name ?? 'NAME',
                      style: Fonts.labelMedium,
                    ),
                    onTap: () {
                      _materialController.text = material.name ?? 'NAME';
                      setState(() {
                        _selectedMaterialId = material.id;
                        materialFromSearch.clear();
                      });
                    },
                  );
                }).toList()),
              if (_selectedMaterialId == null &&
                  widget.product?.type == ProductType.part)
                Text(
                  'Если вы не выбрали материал из списка, он будет создан и применён к этому изделию',
                  style: Fonts.bodySmall.merge(
                    const TextStyle(
                      height: 1.5,
                      color: AppColors.medium,
                    ),
                  ),
                ),
              if (widget.product?.type == ProductType.assembly)
                const SizedBox(height: 24.0),
              if (widget.product?.type == ProductType.assembly)
                Text(
                  'Покрытие',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
              if (widget.product?.type == ProductType.assembly)
                const SizedBox(height: 10.0),
              if (widget.product?.type == ProductType.assembly)
                TextField(
                  controller: _surfaceCoatingController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    hintText: 'Название покрытия',
                  ),
                ),
              if (widget.product?.type != ProductType.materials)
                const SizedBox(height: 24.0),
              if (widget.product?.type != ProductType.materials)
                Text(
                  'Дополнительные требования',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
              if (widget.product?.type != ProductType.materials)
                const SizedBox(height: 10.0),
              if (widget.product?.type != ProductType.materials)
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _itemRequirementsController,
                        style: Fonts.labelSmall,
                        decoration: const InputDecoration(
                          hintText: 'Требования',
                        ),
                      ),
                    ),
                  ],
                ),
              if (widget.product?.type != ProductType.assembly &&
                  widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard)
                const SizedBox(height: 24.0),
              if (widget.product?.type != ProductType.assembly &&
                  widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard)
                Text(
                  'Дополнительные требования к материалу',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
              if (widget.product?.type != ProductType.assembly &&
                  widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard)
                const SizedBox(height: 10.0),
              if (widget.product?.type != ProductType.assembly &&
                  widget.product?.type != ProductType.other &&
                  widget.product?.type != ProductType.standard)
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _materialRequirementsController,
                        style: Fonts.labelSmall,
                        decoration: const InputDecoration(
                          hintText: 'Требования',
                        ),
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 24.0),
              Text(
                'Примечание',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 10.0),
              TextField(
                controller: _noteController,
                style: Fonts.labelSmall,
                decoration: const InputDecoration(
                  hintText: 'Примечание',
                ),
              ),
              if (widget.product?.type == ProductType.other)
                const SizedBox(height: 24.0),
              if (widget.product?.type == ProductType.other)
                TextField(
                  controller: _noteController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    labelText: 'Ссылка на образец',
                  ),
                ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () async {
              // if (_selectedMaterialId == null &&
              //     _materialController.text.isEmpty) return;
              await _save();
              widget.onSave?.call(_product);
            },
            text: 'Сохранить',
          ),
        ),
      ]),
    ]);
  }
}
