import 'package:flutter/material.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/dropdown_button.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class ProductEdit extends StatefulWidget {
  const ProductEdit({super.key, this.product, this.onSave});

  final ProductModel? product;
  final void Function(ProductModel)? onSave;

  @override
  State<ProductEdit> createState() => _ProductEditState();
}

class _ProductEditState extends State<ProductEdit> {
  final _nameController = TextEditingController();
  final _numberController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _designationController = TextEditingController();
  ProductType _selectedType = ProductType.assembly;
  bool dropDownIsOpened = false;
  Future<void> Function()? toggleDropdown;

  ProductModel _product = ProductModel();

  bool _isLoading = false;

  Future<void> _save() async {
    if (widget.product?.id == null) return;
    setState(() {
      _isLoading = true;
    });

    final result = await ProductRepository.edit(
      widget.product!.id!,
      name: _nameController.text,
      number: _numberController.text,
      description: _descriptionController.text,
      type: _selectedType,
      designation: _designationController.text,
    );

    setState(() {
      _product = result?.data ?? ProductModel();
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    if (widget.product != null) {
      _nameController.text = widget.product!.name ?? '';
      _numberController.text = widget.product!.number ?? '';
      _descriptionController.text = widget.product!.description ?? '';
      _designationController.text = widget.product!.designation ?? '';
      _selectedType = widget.product!.type ?? ProductType.assembly;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0.0),
            itemCount: _getFormItemCount(),
            itemBuilder: (context, index) {
              switch (index) {
                case 0:
                  return Text(
                    'Редактирование позици',
                    style: Fonts.titleMedium.merge(
                      const TextStyle(height: 1.6),
                    ),
                  );
                case 1:
                  return const SizedBox(height: 10.0);
                case 2:
                  return Text(
                    'Название позици',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 3:
                  return const SizedBox(height: 10.0);
                case 4:
                  return TextField(
                    controller: _nameController,
                    style: Fonts.labelSmall,
                    decoration: const InputDecoration(
                      hintText: 'Имя',
                    ),
                  );
                case 5:
                  if (_selectedType == ProductType.materials ||
                      _selectedType == ProductType.standard) {
                    return const SizedBox(height: 24.0);
                  }
                  break;
                case 6:
                  if (_selectedType == ProductType.materials ||
                      _selectedType == ProductType.standard) {
                    return Text(
                      'Номер чертежа',
                      style: Fonts.labelSmall.merge(
                        const TextStyle(height: 1.5),
                      ),
                    );
                  }
                  break;
                case 7:
                  if (_selectedType == ProductType.materials ||
                      _selectedType == ProductType.standard) {
                    return const SizedBox(height: 10.0);
                  }
                  break;
                case 8:
                  if (_selectedType == ProductType.materials ||
                      _selectedType == ProductType.standard) {
                    return TextField(
                      controller: _numberController,
                      style: Fonts.labelSmall,
                      decoration: const InputDecoration(
                        hintText: 'Номер',
                      ),
                    );
                  }
                  break;
                case 9:
                  return const SizedBox(height: 24.0);
                case 10:
                  return Text(
                    'Тип позиции',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 11:
                  return const SizedBox(height: 10.0);
                case 12:
                  return Row(
                    children: [
                      CustomDropDownButton(
                        isOpened: dropDownIsOpened,
                        setIsOpened: (value) {
                          setState(() {
                            dropDownIsOpened = value;
                          });
                        },
                        onToggleOpen: (toggleFunction) {
                          toggleDropdown = toggleFunction;
                        },
                        text: _selectedType.getName(),
                        children: ProductType.values.map((value) {
                          return CustomDropdownMenuItem(
                            text: value.getName(),
                            onTap: () {
                              setState(() {
                                _selectedType = value;
                              });
                              toggleDropdown?.call();
                            },
                          );
                        }).toList(),
                      ),
                    ],
                  );
                case 13:
                  return const SizedBox(height: 24.0);
                case 14:
                  return Text(
                    'Описание изделия',
                    style: Fonts.labelSmall.merge(
                      const TextStyle(height: 1.5),
                    ),
                  );
                case 15:
                  return const SizedBox(height: 10.0);
                case 16:
                  return TextField(
                    controller: _descriptionController,
                    style: Fonts.labelSmall,
                    decoration: const InputDecoration(
                      hintText: 'Описание',
                    ),
                  );
              }
              return const SizedBox.shrink();
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () async {
              await _save();
              widget.onSave?.call(_product);
              CustomDrawer.instance.hide();
            },
            text: 'Сохранить',
          ),
        ),
      ]),
    ]);
  }

  /// Calculate total item count for ListView.builder
  int _getFormItemCount() {
    int count = 17; // Base form items

    // Adjust for conditional fields
    if (_selectedType == ProductType.materials ||
        _selectedType == ProductType.standard) {
      count -= 4; // Remove drawing number related items
    }

    return count;
  }
}
