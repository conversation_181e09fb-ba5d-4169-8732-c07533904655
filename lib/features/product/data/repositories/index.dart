import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sphere/features/product/data/models/index.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class ProductRepository {
  static Future<Response<ProductModel>?> create(
    CreateProductInput data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProductModel>(
      url: '/products/create',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<SearchProductOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<SearchProductOutputModel>(
      url: '/products/search',
      body: body,
      method: 'POST',
      fromJson: SearchProductOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> removeWorkers(
    String productId,
    List<String> userIds,
  ) async {
    final body = jsonEncode({
      'userIds': userIds,
    });

    final request = await API.request<ProductModel>(
      url: '/products/removeWorkers?productId=$productId',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> addWorkers(
    String productId,
    List<String> userIds,
  ) async {
    final body = jsonEncode({
      'userIds': userIds,
    });

    final request = await API.request<ProductModel>(
      url: '/products/addWorkers?productId=$productId',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> view(
    String productId,
  ) async {
    // final body = jsonEncode({
    //   'userIds': userIds,
    // });

    final request = await API.request<ProductModel>(
      url: '/products/view?productId=$productId',
      // body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> updateParameters(
    String productId,
    ParametersModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProductModel>(
      url: '/products/updateParameters?productId=$productId',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> delete(
    String productId, {
    bool forceDocument = true,
    bool forceChildProduct = true,
  }) async {
    final body = jsonEncode({
      'forceDocument': forceDocument,
      'forceChildProduct': forceChildProduct,
    });

    final request = await API.request<ProductModel>(
      url: '/products/delete?productId=$productId',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> edit(
    String productId, {
    String? name,
    String? number,
    String? description,
    ProductType? type,
    String? designation,
  }) async {
    final body = jsonEncode({
      if (name != null) 'name': name,
      if (number != null && number.isNotEmpty) 'number': number,
      if (description != null && description.isNotEmpty)
        'description': description,
      if (type != null) 'type': type.name,
      if (designation != null) 'designation': designation
    });

    final request = await API.request<ProductModel>(
      url: '/products/edit?productId=$productId',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> editOperations({
    required String productId,
    required List<String> operations,
  }) async {
    final body = jsonEncode({'operations': operations});

    final request = await API.request<ProductModel>(
      url: '/products/edit?productId=$productId',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<ProductModel>?> changePriority(
    String productId,
    int newPriority,
  ) async {
    final body = jsonEncode({
      'productId': productId,
      'newPriority': newPriority,
    });

    final request = await API.request<ProductModel>(
      url: '/products/priorityChange',
      body: body,
      method: 'POST',
      fromJson: ProductModel.fromJson,
    );

    return request;
  }

  static Future<Response<CopyProductOuputModel>?> copy({
    required List<String> productIds,
    List<String>? parentIds,
    String? projectId,
  }) async {
    final body = jsonEncode({
      'productIds': productIds,
      if (parentIds != null) 'parentIds': parentIds,
      if (projectId != null) 'projectId': projectId,
    });

    final request = await API.request<CopyProductOuputModel>(
      url: '/products/copy',
      body: body,
      method: 'POST',
      fromJson: CopyProductOuputModel.fromJson,
    );

    return request;
  }
}
