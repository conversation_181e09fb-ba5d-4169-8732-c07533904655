import 'package:json_annotation/json_annotation.dart';

enum ProductType {
  @JsonValue('assembly')
  assembly,
  @JsonValue('standard')
  standard,
  @JsonValue('materials')
  materials,
  @JsonValue('part')
  part,
  @JsonValue('other')
  other;

  String getName() {
    switch (this) {
      case ProductType.assembly:
        return 'Сборочная единица';
      case ProductType.standard:
        return 'Стандартное изделие';
      case ProductType.materials:
        return 'Материал';
      case ProductType.part:
        return 'Деталь';
      case ProductType.other:
        return 'Прочее изделие';
    }
  }
}
