import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/project/data/models/project.dart';

part 'index.g.dart';

@JsonSerializable(includeIfNull: false)
class CreateProductInput {
  final String? name;
  final String? number;
  final String? description;
  final ProductType? type;
  final String? projectId;
  final String? parentId;
  final String? designation;
  final int? quantity;
  final ParametersModel? parameters;

  CreateProductInput({
    this.name,
    this.number,
    this.description,
    this.type,
    this.projectId,
    this.parentId,
    this.designation,
    this.quantity,
    this.parameters,
  });

  factory CreateProductInput.fromJson(Map<String, dynamic> json) =>
      _$CreateProductInputFromJson(json);
  Map<String, dynamic> toJson() => _$CreateProductInputToJson(this);
}

@JsonSerializable(includeIfNull: false)
class SearchProductOutputModel {
  final List<ProductModel>? items;
  final int? totalItems;
  final List<ProjectModel>? projects;
  final ProductModel? project;
  final String? message;

  SearchProductOutputModel({
    this.items,
    this.totalItems,
    this.projects,
    this.project,
    this.message,
  });

  factory SearchProductOutputModel.fromJson(Map<String, dynamic> json) =>
      _$SearchProductOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$SearchProductOutputModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class CopyProductOuputModel {
  final List<ProductModel>? items;

  CopyProductOuputModel({
    this.items,
  });

  factory CopyProductOuputModel.fromJson(Map<String, dynamic> json) =>
      _$CopyProductOuputModelFromJson(json);
  Map<String, dynamic> toJson() => _$CopyProductOuputModelToJson(this);
}
