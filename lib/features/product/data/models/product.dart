import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/files/data/models/file.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';

part 'product.freezed.dart';
part 'product.g.dart';

@freezed
class ProductModel with _$ProductModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductModel({
    @Json<PERSON>ey(name: '_id') String? id,
    String? name,
    String? description,
    String? number,
    String? projectId,
    String? parentId,
    bool? hasChildren,
    Department? department,
    @JsonKey(name: 'bluePrint') FileModel? blueprint,
    int? documentsCount,
    int? rootChildrenCount,
    int? allChildrenCount,
    ProductType? type,
    int? priority,
    ParametersModel? parameters,
    String? designation,
    List<TaskProgressModel>? progressTasks,
    List<String>? operations,
    List<ProvisionLotModel>? provisionInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ProductModel;

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);
}
