// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateProductInput _$CreateProductInputFromJson(Map<String, dynamic> json) =>
    CreateProductInput(
      name: json['name'] as String?,
      number: json['number'] as String?,
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$ProductTypeEnumMap, json['type']),
      projectId: json['projectId'] as String?,
      parentId: json['parentId'] as String?,
      designation: json['designation'] as String?,
      quantity: (json['quantity'] as num?)?.toInt(),
      parameters: json['parameters'] == null
          ? null
          : ParametersModel.fromJson(
              json['parameters'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CreateProductInputToJson(CreateProductInput instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (instance.number case final value?) 'number': value,
      if (instance.description case final value?) 'description': value,
      if (_$ProductTypeEnumMap[instance.type] case final value?) 'type': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.parentId case final value?) 'parentId': value,
      if (instance.designation case final value?) 'designation': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.parameters case final value?) 'parameters': value,
    };

const _$ProductTypeEnumMap = {
  ProductType.assembly: 'assembly',
  ProductType.standard: 'standard',
  ProductType.materials: 'materials',
  ProductType.part: 'part',
  ProductType.other: 'other',
};

SearchProductOutputModel _$SearchProductOutputModelFromJson(
        Map<String, dynamic> json) =>
    SearchProductOutputModel(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
      projects: (json['projects'] as List<dynamic>?)
          ?.map((e) => ProjectModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      project: json['project'] == null
          ? null
          : ProductModel.fromJson(json['project'] as Map<String, dynamic>),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SearchProductOutputModelToJson(
        SearchProductOutputModel instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
      if (instance.projects case final value?) 'projects': value,
      if (instance.project case final value?) 'project': value,
      if (instance.message case final value?) 'message': value,
    };

CopyProductOuputModel _$CopyProductOuputModelFromJson(
        Map<String, dynamic> json) =>
    CopyProductOuputModel(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CopyProductOuputModelToJson(
        CopyProductOuputModel instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
    };
