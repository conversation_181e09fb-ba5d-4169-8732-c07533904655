// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) {
  return _ProductModel.fromJson(json);
}

/// @nodoc
mixin _$ProductModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get number => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  String? get parentId => throw _privateConstructorUsedError;
  bool? get hasChildren => throw _privateConstructorUsedError;
  Department? get department => throw _privateConstructorUsedError;
  @JsonKey(name: 'bluePrint')
  FileModel? get blueprint => throw _privateConstructorUsedError;
  int? get documentsCount => throw _privateConstructorUsedError;
  int? get rootChildrenCount => throw _privateConstructorUsedError;
  int? get allChildrenCount => throw _privateConstructorUsedError;
  ProductType? get type => throw _privateConstructorUsedError;
  int? get priority => throw _privateConstructorUsedError;
  ParametersModel? get parameters => throw _privateConstructorUsedError;
  String? get designation => throw _privateConstructorUsedError;
  List<TaskProgressModel>? get progressTasks =>
      throw _privateConstructorUsedError;
  List<String>? get operations => throw _privateConstructorUsedError;
  List<ProvisionLotModel>? get provisionInfo =>
      throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ProductModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductModelCopyWith<ProductModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductModelCopyWith<$Res> {
  factory $ProductModelCopyWith(
          ProductModel value, $Res Function(ProductModel) then) =
      _$ProductModelCopyWithImpl<$Res, ProductModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      String? description,
      String? number,
      String? projectId,
      String? parentId,
      bool? hasChildren,
      Department? department,
      @JsonKey(name: 'bluePrint') FileModel? blueprint,
      int? documentsCount,
      int? rootChildrenCount,
      int? allChildrenCount,
      ProductType? type,
      int? priority,
      ParametersModel? parameters,
      String? designation,
      List<TaskProgressModel>? progressTasks,
      List<String>? operations,
      List<ProvisionLotModel>? provisionInfo,
      DateTime? createdAt,
      DateTime? updatedAt});

  $ParametersModelCopyWith<$Res>? get parameters;
}

/// @nodoc
class _$ProductModelCopyWithImpl<$Res, $Val extends ProductModel>
    implements $ProductModelCopyWith<$Res> {
  _$ProductModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? number = freezed,
    Object? projectId = freezed,
    Object? parentId = freezed,
    Object? hasChildren = freezed,
    Object? department = freezed,
    Object? blueprint = freezed,
    Object? documentsCount = freezed,
    Object? rootChildrenCount = freezed,
    Object? allChildrenCount = freezed,
    Object? type = freezed,
    Object? priority = freezed,
    Object? parameters = freezed,
    Object? designation = freezed,
    Object? progressTasks = freezed,
    Object? operations = freezed,
    Object? provisionInfo = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String?,
      hasChildren: freezed == hasChildren
          ? _value.hasChildren
          : hasChildren // ignore: cast_nullable_to_non_nullable
              as bool?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      blueprint: freezed == blueprint
          ? _value.blueprint
          : blueprint // ignore: cast_nullable_to_non_nullable
              as FileModel?,
      documentsCount: freezed == documentsCount
          ? _value.documentsCount
          : documentsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rootChildrenCount: freezed == rootChildrenCount
          ? _value.rootChildrenCount
          : rootChildrenCount // ignore: cast_nullable_to_non_nullable
              as int?,
      allChildrenCount: freezed == allChildrenCount
          ? _value.allChildrenCount
          : allChildrenCount // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ProductType?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      parameters: freezed == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as ParametersModel?,
      designation: freezed == designation
          ? _value.designation
          : designation // ignore: cast_nullable_to_non_nullable
              as String?,
      progressTasks: freezed == progressTasks
          ? _value.progressTasks
          : progressTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskProgressModel>?,
      operations: freezed == operations
          ? _value.operations
          : operations // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      provisionInfo: freezed == provisionInfo
          ? _value.provisionInfo
          : provisionInfo // ignore: cast_nullable_to_non_nullable
              as List<ProvisionLotModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ParametersModelCopyWith<$Res>? get parameters {
    if (_value.parameters == null) {
      return null;
    }

    return $ParametersModelCopyWith<$Res>(_value.parameters!, (value) {
      return _then(_value.copyWith(parameters: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductModelImplCopyWith<$Res>
    implements $ProductModelCopyWith<$Res> {
  factory _$$ProductModelImplCopyWith(
          _$ProductModelImpl value, $Res Function(_$ProductModelImpl) then) =
      __$$ProductModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? name,
      String? description,
      String? number,
      String? projectId,
      String? parentId,
      bool? hasChildren,
      Department? department,
      @JsonKey(name: 'bluePrint') FileModel? blueprint,
      int? documentsCount,
      int? rootChildrenCount,
      int? allChildrenCount,
      ProductType? type,
      int? priority,
      ParametersModel? parameters,
      String? designation,
      List<TaskProgressModel>? progressTasks,
      List<String>? operations,
      List<ProvisionLotModel>? provisionInfo,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $ParametersModelCopyWith<$Res>? get parameters;
}

/// @nodoc
class __$$ProductModelImplCopyWithImpl<$Res>
    extends _$ProductModelCopyWithImpl<$Res, _$ProductModelImpl>
    implements _$$ProductModelImplCopyWith<$Res> {
  __$$ProductModelImplCopyWithImpl(
      _$ProductModelImpl _value, $Res Function(_$ProductModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? number = freezed,
    Object? projectId = freezed,
    Object? parentId = freezed,
    Object? hasChildren = freezed,
    Object? department = freezed,
    Object? blueprint = freezed,
    Object? documentsCount = freezed,
    Object? rootChildrenCount = freezed,
    Object? allChildrenCount = freezed,
    Object? type = freezed,
    Object? priority = freezed,
    Object? parameters = freezed,
    Object? designation = freezed,
    Object? progressTasks = freezed,
    Object? operations = freezed,
    Object? provisionInfo = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ProductModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as String?,
      hasChildren: freezed == hasChildren
          ? _value.hasChildren
          : hasChildren // ignore: cast_nullable_to_non_nullable
              as bool?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      blueprint: freezed == blueprint
          ? _value.blueprint
          : blueprint // ignore: cast_nullable_to_non_nullable
              as FileModel?,
      documentsCount: freezed == documentsCount
          ? _value.documentsCount
          : documentsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rootChildrenCount: freezed == rootChildrenCount
          ? _value.rootChildrenCount
          : rootChildrenCount // ignore: cast_nullable_to_non_nullable
              as int?,
      allChildrenCount: freezed == allChildrenCount
          ? _value.allChildrenCount
          : allChildrenCount // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ProductType?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      parameters: freezed == parameters
          ? _value.parameters
          : parameters // ignore: cast_nullable_to_non_nullable
              as ParametersModel?,
      designation: freezed == designation
          ? _value.designation
          : designation // ignore: cast_nullable_to_non_nullable
              as String?,
      progressTasks: freezed == progressTasks
          ? _value._progressTasks
          : progressTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskProgressModel>?,
      operations: freezed == operations
          ? _value._operations
          : operations // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      provisionInfo: freezed == provisionInfo
          ? _value._provisionInfo
          : provisionInfo // ignore: cast_nullable_to_non_nullable
              as List<ProvisionLotModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductModelImpl implements _ProductModel {
  const _$ProductModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.name,
      this.description,
      this.number,
      this.projectId,
      this.parentId,
      this.hasChildren,
      this.department,
      @JsonKey(name: 'bluePrint') this.blueprint,
      this.documentsCount,
      this.rootChildrenCount,
      this.allChildrenCount,
      this.type,
      this.priority,
      this.parameters,
      this.designation,
      final List<TaskProgressModel>? progressTasks,
      final List<String>? operations,
      final List<ProvisionLotModel>? provisionInfo,
      this.createdAt,
      this.updatedAt})
      : _progressTasks = progressTasks,
        _operations = operations,
        _provisionInfo = provisionInfo;

  factory _$ProductModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? name;
  @override
  final String? description;
  @override
  final String? number;
  @override
  final String? projectId;
  @override
  final String? parentId;
  @override
  final bool? hasChildren;
  @override
  final Department? department;
  @override
  @JsonKey(name: 'bluePrint')
  final FileModel? blueprint;
  @override
  final int? documentsCount;
  @override
  final int? rootChildrenCount;
  @override
  final int? allChildrenCount;
  @override
  final ProductType? type;
  @override
  final int? priority;
  @override
  final ParametersModel? parameters;
  @override
  final String? designation;
  final List<TaskProgressModel>? _progressTasks;
  @override
  List<TaskProgressModel>? get progressTasks {
    final value = _progressTasks;
    if (value == null) return null;
    if (_progressTasks is EqualUnmodifiableListView) return _progressTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _operations;
  @override
  List<String>? get operations {
    final value = _operations;
    if (value == null) return null;
    if (_operations is EqualUnmodifiableListView) return _operations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProvisionLotModel>? _provisionInfo;
  @override
  List<ProvisionLotModel>? get provisionInfo {
    final value = _provisionInfo;
    if (value == null) return null;
    if (_provisionInfo is EqualUnmodifiableListView) return _provisionInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, description: $description, number: $number, projectId: $projectId, parentId: $parentId, hasChildren: $hasChildren, department: $department, blueprint: $blueprint, documentsCount: $documentsCount, rootChildrenCount: $rootChildrenCount, allChildrenCount: $allChildrenCount, type: $type, priority: $priority, parameters: $parameters, designation: $designation, progressTasks: $progressTasks, operations: $operations, provisionInfo: $provisionInfo, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.number, number) || other.number == number) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.hasChildren, hasChildren) ||
                other.hasChildren == hasChildren) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.blueprint, blueprint) ||
                other.blueprint == blueprint) &&
            (identical(other.documentsCount, documentsCount) ||
                other.documentsCount == documentsCount) &&
            (identical(other.rootChildrenCount, rootChildrenCount) ||
                other.rootChildrenCount == rootChildrenCount) &&
            (identical(other.allChildrenCount, allChildrenCount) ||
                other.allChildrenCount == allChildrenCount) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.parameters, parameters) ||
                other.parameters == parameters) &&
            (identical(other.designation, designation) ||
                other.designation == designation) &&
            const DeepCollectionEquality()
                .equals(other._progressTasks, _progressTasks) &&
            const DeepCollectionEquality()
                .equals(other._operations, _operations) &&
            const DeepCollectionEquality()
                .equals(other._provisionInfo, _provisionInfo) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        name,
        description,
        number,
        projectId,
        parentId,
        hasChildren,
        department,
        blueprint,
        documentsCount,
        rootChildrenCount,
        allChildrenCount,
        type,
        priority,
        parameters,
        designation,
        const DeepCollectionEquality().hash(_progressTasks),
        const DeepCollectionEquality().hash(_operations),
        const DeepCollectionEquality().hash(_provisionInfo),
        createdAt,
        updatedAt
      ]);

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      __$$ProductModelImplCopyWithImpl<_$ProductModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductModelImplToJson(
      this,
    );
  }
}

abstract class _ProductModel implements ProductModel {
  const factory _ProductModel(
      {@JsonKey(name: '_id') final String? id,
      final String? name,
      final String? description,
      final String? number,
      final String? projectId,
      final String? parentId,
      final bool? hasChildren,
      final Department? department,
      @JsonKey(name: 'bluePrint') final FileModel? blueprint,
      final int? documentsCount,
      final int? rootChildrenCount,
      final int? allChildrenCount,
      final ProductType? type,
      final int? priority,
      final ParametersModel? parameters,
      final String? designation,
      final List<TaskProgressModel>? progressTasks,
      final List<String>? operations,
      final List<ProvisionLotModel>? provisionInfo,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ProductModelImpl;

  factory _ProductModel.fromJson(Map<String, dynamic> json) =
      _$ProductModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get name;
  @override
  String? get description;
  @override
  String? get number;
  @override
  String? get projectId;
  @override
  String? get parentId;
  @override
  bool? get hasChildren;
  @override
  Department? get department;
  @override
  @JsonKey(name: 'bluePrint')
  FileModel? get blueprint;
  @override
  int? get documentsCount;
  @override
  int? get rootChildrenCount;
  @override
  int? get allChildrenCount;
  @override
  ProductType? get type;
  @override
  int? get priority;
  @override
  ParametersModel? get parameters;
  @override
  String? get designation;
  @override
  List<TaskProgressModel>? get progressTasks;
  @override
  List<String>? get operations;
  @override
  List<ProvisionLotModel>? get provisionInfo;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ProductModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductModelImplCopyWith<_$ProductModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
