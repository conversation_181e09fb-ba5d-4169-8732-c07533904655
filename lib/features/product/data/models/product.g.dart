// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductModelImpl _$$ProductModelImplFromJson(Map<String, dynamic> json) =>
    _$ProductModelImpl(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      description: json['description'] as String?,
      number: json['number'] as String?,
      projectId: json['projectId'] as String?,
      parentId: json['parentId'] as String?,
      hasChildren: json['hasChildren'] as bool?,
      department: $enumDecodeNullable(_$DepartmentEnumMap, json['department']),
      blueprint: json['bluePrint'] == null
          ? null
          : FileModel.fromJson(json['bluePrint'] as Map<String, dynamic>),
      documentsCount: (json['documentsCount'] as num?)?.toInt(),
      rootChildrenCount: (json['rootChildrenCount'] as num?)?.toInt(),
      allChildrenCount: (json['allChildrenCount'] as num?)?.toInt(),
      type: $enumDecodeNullable(_$ProductTypeEnumMap, json['type']),
      priority: (json['priority'] as num?)?.toInt(),
      parameters: json['parameters'] == null
          ? null
          : ParametersModel.fromJson(
              json['parameters'] as Map<String, dynamic>),
      designation: json['designation'] as String?,
      progressTasks: (json['progressTasks'] as List<dynamic>?)
          ?.map((e) => TaskProgressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      operations: (json['operations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      provisionInfo: (json['provisionInfo'] as List<dynamic>?)
          ?.map((e) => ProvisionLotModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ProductModelImplToJson(_$ProductModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.description case final value?) 'description': value,
      if (instance.number case final value?) 'number': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.parentId case final value?) 'parentId': value,
      if (instance.hasChildren case final value?) 'hasChildren': value,
      if (instance.department case final value?) 'department': value,
      if (instance.blueprint case final value?) 'bluePrint': value,
      if (instance.documentsCount case final value?) 'documentsCount': value,
      if (instance.rootChildrenCount case final value?)
        'rootChildrenCount': value,
      if (instance.allChildrenCount case final value?)
        'allChildrenCount': value,
      if (_$ProductTypeEnumMap[instance.type] case final value?) 'type': value,
      if (instance.priority case final value?) 'priority': value,
      if (instance.parameters case final value?) 'parameters': value,
      if (instance.designation case final value?) 'designation': value,
      if (instance.progressTasks case final value?) 'progressTasks': value,
      if (instance.operations case final value?) 'operations': value,
      if (instance.provisionInfo case final value?) 'provisionInfo': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};

const _$ProductTypeEnumMap = {
  ProductType.assembly: 'assembly',
  ProductType.standard: 'standard',
  ProductType.materials: 'materials',
  ProductType.part: 'part',
  ProductType.other: 'other',
};
