part of 'bloc.dart';

@freezed
class BlocWarehousesEvents with _$BlocWarehousesEvents {
  const factory BlocWarehousesEvents.loadWarehouses() = LoadWarehouses;
  
  const factory BlocWarehousesEvents.searchWarehouses(String query) = SearchWarehouses;
  
  const factory BlocWarehousesEvents.filterWarehousesByType(WarehouseType? type) = FilterWarehousesByType;
  
  const factory BlocWarehousesEvents.addWarehouse(Warehouse warehouse) = AddWarehouse;
  
  const factory BlocWarehousesEvents.updateWarehouse(Warehouse warehouse) = UpdateWarehouse;
  
  const factory BlocWarehousesEvents.deleteWarehouse(String warehouseId) = DeleteWarehouse;
  
  const factory BlocWarehousesEvents.clearError() = ClearError;
}
