import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';

part 'bloc.freezed.dart';
part 'events.dart';
part 'state.dart';

class BlocWarehouses extends Bloc<BlocWarehousesEvents, BlocWarehousesState> {
  BlocWarehouses()
      : super(const BlocWarehousesState(
          warehouses: [],
          filteredWarehouses: [],
          isLoading: false,
          searchQuery: '',
          selectedType: null,
          error: null,
        )) {
    on<LoadWarehouses>((event, emit) async {
      emit(state.copyWith(isLoading: true, error: null));

      try {
        final response = await PurchaseListRepositoryV2.searchWarehouses();

        emit(state.copyWith(
          warehouses: response.data!.warehouses ?? [],
          filteredWarehouses: _filterWarehouses(
            response.data!.warehouses ?? [],
            state.searchQuery,
            state.selectedType,
          ),
          isLoading: false,
        ));
      } catch (e) {
        emit(state.copyWith(
          isLoading: false,
          error: e.toString(),
        ));
      }
    });

    on<SearchWarehouses>((event, emit) {
      final filteredWarehouses = _filterWarehouses(
        state.warehouses,
        event.query,
        state.selectedType,
      );

      emit(state.copyWith(
        searchQuery: event.query,
        filteredWarehouses: filteredWarehouses,
      ));
    });

    on<FilterWarehousesByType>((event, emit) {
      final filteredWarehouses = _filterWarehouses(
        state.warehouses,
        state.searchQuery,
        event.type,
      );

      emit(state.copyWith(
        selectedType: event.type,
        filteredWarehouses: filteredWarehouses,
      ));
    });

    on<AddWarehouse>((event, emit) {
      final updatedWarehouses = [...state.warehouses, event.warehouse];
      final filteredWarehouses = _filterWarehouses(
        updatedWarehouses,
        state.searchQuery,
        state.selectedType,
      );

      emit(state.copyWith(
        warehouses: updatedWarehouses,
        filteredWarehouses: filteredWarehouses,
      ));
    });

    on<UpdateWarehouse>((event, emit) {
      final updatedWarehouses = state.warehouses.map((warehouse) {
        return warehouse.id == event.warehouse.id ? event.warehouse : warehouse;
      }).toList();

      final filteredWarehouses = _filterWarehouses(
        updatedWarehouses,
        state.searchQuery,
        state.selectedType,
      );

      emit(state.copyWith(
        warehouses: updatedWarehouses,
        filteredWarehouses: filteredWarehouses,
      ));
    });

    on<DeleteWarehouse>((event, emit) async {
      try {
        // Вызываем API для удаления склада
        final deleteInput =
            WarehouseDeleteInput(warehouseId: event.warehouseId);
        await PurchaseListRepositoryV2.deleteWarehouse(deleteInput);

        // Обновляем локальное состояние
        final updatedWarehouses = state.warehouses
            .where((warehouse) => warehouse.id != event.warehouseId)
            .toList();

        final filteredWarehouses = _filterWarehouses(
          updatedWarehouses,
          state.searchQuery,
          state.selectedType,
        );

        emit(state.copyWith(
          warehouses: updatedWarehouses,
          filteredWarehouses: filteredWarehouses,
        ));
      } catch (e) {
        emit(state.copyWith(error: e.toString()));
      }
    });

    on<ClearError>((event, emit) {
      emit(state.copyWith(error: null));
    });
  }

  List<Warehouse> _filterWarehouses(
    List<Warehouse> warehouses,
    String searchQuery,
    WarehouseType? selectedType,
  ) {
    var filtered = warehouses;

    // Filter by search query
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((warehouse) {
        return warehouse.name.toLowerCase().contains(query) ||
            (warehouse.address?.toLowerCase().contains(query) ?? false) ||
            (warehouse.description?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Filter by type
    if (selectedType != null) {
      filtered = filtered
          .where((warehouse) => warehouse.type == selectedType)
          .toList();
    }

    return filtered;
  }
}
