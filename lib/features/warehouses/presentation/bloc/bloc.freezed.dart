// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BlocWarehousesEvents {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocWarehousesEventsCopyWith<$Res> {
  factory $BlocWarehousesEventsCopyWith(BlocWarehousesEvents value,
          $Res Function(BlocWarehousesEvents) then) =
      _$BlocWarehousesEventsCopyWithImpl<$Res, BlocWarehousesEvents>;
}

/// @nodoc
class _$BlocWarehousesEventsCopyWithImpl<$Res,
        $Val extends BlocWarehousesEvents>
    implements $BlocWarehousesEventsCopyWith<$Res> {
  _$BlocWarehousesEventsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadWarehousesImplCopyWith<$Res> {
  factory _$$LoadWarehousesImplCopyWith(_$LoadWarehousesImpl value,
          $Res Function(_$LoadWarehousesImpl) then) =
      __$$LoadWarehousesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadWarehousesImplCopyWithImpl<$Res>
    extends _$BlocWarehousesEventsCopyWithImpl<$Res, _$LoadWarehousesImpl>
    implements _$$LoadWarehousesImplCopyWith<$Res> {
  __$$LoadWarehousesImplCopyWithImpl(
      _$LoadWarehousesImpl _value, $Res Function(_$LoadWarehousesImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadWarehousesImpl implements LoadWarehouses {
  const _$LoadWarehousesImpl();

  @override
  String toString() {
    return 'BlocWarehousesEvents.loadWarehouses()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadWarehousesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) {
    return loadWarehouses();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) {
    return loadWarehouses?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) {
    if (loadWarehouses != null) {
      return loadWarehouses();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) {
    return loadWarehouses(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) {
    return loadWarehouses?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) {
    if (loadWarehouses != null) {
      return loadWarehouses(this);
    }
    return orElse();
  }
}

abstract class LoadWarehouses implements BlocWarehousesEvents {
  const factory LoadWarehouses() = _$LoadWarehousesImpl;
}

/// @nodoc
abstract class _$$SearchWarehousesImplCopyWith<$Res> {
  factory _$$SearchWarehousesImplCopyWith(_$SearchWarehousesImpl value,
          $Res Function(_$SearchWarehousesImpl) then) =
      __$$SearchWarehousesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$SearchWarehousesImplCopyWithImpl<$Res>
    extends _$BlocWarehousesEventsCopyWithImpl<$Res, _$SearchWarehousesImpl>
    implements _$$SearchWarehousesImplCopyWith<$Res> {
  __$$SearchWarehousesImplCopyWithImpl(_$SearchWarehousesImpl _value,
      $Res Function(_$SearchWarehousesImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$SearchWarehousesImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchWarehousesImpl implements SearchWarehouses {
  const _$SearchWarehousesImpl(this.query);

  @override
  final String query;

  @override
  String toString() {
    return 'BlocWarehousesEvents.searchWarehouses(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchWarehousesImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchWarehousesImplCopyWith<_$SearchWarehousesImpl> get copyWith =>
      __$$SearchWarehousesImplCopyWithImpl<_$SearchWarehousesImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) {
    return searchWarehouses(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) {
    return searchWarehouses?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) {
    if (searchWarehouses != null) {
      return searchWarehouses(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) {
    return searchWarehouses(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) {
    return searchWarehouses?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) {
    if (searchWarehouses != null) {
      return searchWarehouses(this);
    }
    return orElse();
  }
}

abstract class SearchWarehouses implements BlocWarehousesEvents {
  const factory SearchWarehouses(final String query) = _$SearchWarehousesImpl;

  String get query;

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchWarehousesImplCopyWith<_$SearchWarehousesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FilterWarehousesByTypeImplCopyWith<$Res> {
  factory _$$FilterWarehousesByTypeImplCopyWith(
          _$FilterWarehousesByTypeImpl value,
          $Res Function(_$FilterWarehousesByTypeImpl) then) =
      __$$FilterWarehousesByTypeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({WarehouseType? type});
}

/// @nodoc
class __$$FilterWarehousesByTypeImplCopyWithImpl<$Res>
    extends _$BlocWarehousesEventsCopyWithImpl<$Res,
        _$FilterWarehousesByTypeImpl>
    implements _$$FilterWarehousesByTypeImplCopyWith<$Res> {
  __$$FilterWarehousesByTypeImplCopyWithImpl(
      _$FilterWarehousesByTypeImpl _value,
      $Res Function(_$FilterWarehousesByTypeImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
  }) {
    return _then(_$FilterWarehousesByTypeImpl(
      freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as WarehouseType?,
    ));
  }
}

/// @nodoc

class _$FilterWarehousesByTypeImpl implements FilterWarehousesByType {
  const _$FilterWarehousesByTypeImpl(this.type);

  @override
  final WarehouseType? type;

  @override
  String toString() {
    return 'BlocWarehousesEvents.filterWarehousesByType(type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterWarehousesByTypeImpl &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterWarehousesByTypeImplCopyWith<_$FilterWarehousesByTypeImpl>
      get copyWith => __$$FilterWarehousesByTypeImplCopyWithImpl<
          _$FilterWarehousesByTypeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) {
    return filterWarehousesByType(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) {
    return filterWarehousesByType?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) {
    if (filterWarehousesByType != null) {
      return filterWarehousesByType(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) {
    return filterWarehousesByType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) {
    return filterWarehousesByType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) {
    if (filterWarehousesByType != null) {
      return filterWarehousesByType(this);
    }
    return orElse();
  }
}

abstract class FilterWarehousesByType implements BlocWarehousesEvents {
  const factory FilterWarehousesByType(final WarehouseType? type) =
      _$FilterWarehousesByTypeImpl;

  WarehouseType? get type;

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterWarehousesByTypeImplCopyWith<_$FilterWarehousesByTypeImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddWarehouseImplCopyWith<$Res> {
  factory _$$AddWarehouseImplCopyWith(
          _$AddWarehouseImpl value, $Res Function(_$AddWarehouseImpl) then) =
      __$$AddWarehouseImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Warehouse warehouse});

  $WarehouseCopyWith<$Res> get warehouse;
}

/// @nodoc
class __$$AddWarehouseImplCopyWithImpl<$Res>
    extends _$BlocWarehousesEventsCopyWithImpl<$Res, _$AddWarehouseImpl>
    implements _$$AddWarehouseImplCopyWith<$Res> {
  __$$AddWarehouseImplCopyWithImpl(
      _$AddWarehouseImpl _value, $Res Function(_$AddWarehouseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouse = null,
  }) {
    return _then(_$AddWarehouseImpl(
      null == warehouse
          ? _value.warehouse
          : warehouse // ignore: cast_nullable_to_non_nullable
              as Warehouse,
    ));
  }

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WarehouseCopyWith<$Res> get warehouse {
    return $WarehouseCopyWith<$Res>(_value.warehouse, (value) {
      return _then(_value.copyWith(warehouse: value));
    });
  }
}

/// @nodoc

class _$AddWarehouseImpl implements AddWarehouse {
  const _$AddWarehouseImpl(this.warehouse);

  @override
  final Warehouse warehouse;

  @override
  String toString() {
    return 'BlocWarehousesEvents.addWarehouse(warehouse: $warehouse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddWarehouseImpl &&
            (identical(other.warehouse, warehouse) ||
                other.warehouse == warehouse));
  }

  @override
  int get hashCode => Object.hash(runtimeType, warehouse);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddWarehouseImplCopyWith<_$AddWarehouseImpl> get copyWith =>
      __$$AddWarehouseImplCopyWithImpl<_$AddWarehouseImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) {
    return addWarehouse(warehouse);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) {
    return addWarehouse?.call(warehouse);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) {
    if (addWarehouse != null) {
      return addWarehouse(warehouse);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) {
    return addWarehouse(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) {
    return addWarehouse?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) {
    if (addWarehouse != null) {
      return addWarehouse(this);
    }
    return orElse();
  }
}

abstract class AddWarehouse implements BlocWarehousesEvents {
  const factory AddWarehouse(final Warehouse warehouse) = _$AddWarehouseImpl;

  Warehouse get warehouse;

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddWarehouseImplCopyWith<_$AddWarehouseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateWarehouseImplCopyWith<$Res> {
  factory _$$UpdateWarehouseImplCopyWith(_$UpdateWarehouseImpl value,
          $Res Function(_$UpdateWarehouseImpl) then) =
      __$$UpdateWarehouseImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Warehouse warehouse});

  $WarehouseCopyWith<$Res> get warehouse;
}

/// @nodoc
class __$$UpdateWarehouseImplCopyWithImpl<$Res>
    extends _$BlocWarehousesEventsCopyWithImpl<$Res, _$UpdateWarehouseImpl>
    implements _$$UpdateWarehouseImplCopyWith<$Res> {
  __$$UpdateWarehouseImplCopyWithImpl(
      _$UpdateWarehouseImpl _value, $Res Function(_$UpdateWarehouseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouse = null,
  }) {
    return _then(_$UpdateWarehouseImpl(
      null == warehouse
          ? _value.warehouse
          : warehouse // ignore: cast_nullable_to_non_nullable
              as Warehouse,
    ));
  }

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WarehouseCopyWith<$Res> get warehouse {
    return $WarehouseCopyWith<$Res>(_value.warehouse, (value) {
      return _then(_value.copyWith(warehouse: value));
    });
  }
}

/// @nodoc

class _$UpdateWarehouseImpl implements UpdateWarehouse {
  const _$UpdateWarehouseImpl(this.warehouse);

  @override
  final Warehouse warehouse;

  @override
  String toString() {
    return 'BlocWarehousesEvents.updateWarehouse(warehouse: $warehouse)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateWarehouseImpl &&
            (identical(other.warehouse, warehouse) ||
                other.warehouse == warehouse));
  }

  @override
  int get hashCode => Object.hash(runtimeType, warehouse);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateWarehouseImplCopyWith<_$UpdateWarehouseImpl> get copyWith =>
      __$$UpdateWarehouseImplCopyWithImpl<_$UpdateWarehouseImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) {
    return updateWarehouse(warehouse);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) {
    return updateWarehouse?.call(warehouse);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) {
    if (updateWarehouse != null) {
      return updateWarehouse(warehouse);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) {
    return updateWarehouse(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) {
    return updateWarehouse?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) {
    if (updateWarehouse != null) {
      return updateWarehouse(this);
    }
    return orElse();
  }
}

abstract class UpdateWarehouse implements BlocWarehousesEvents {
  const factory UpdateWarehouse(final Warehouse warehouse) =
      _$UpdateWarehouseImpl;

  Warehouse get warehouse;

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateWarehouseImplCopyWith<_$UpdateWarehouseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteWarehouseImplCopyWith<$Res> {
  factory _$$DeleteWarehouseImplCopyWith(_$DeleteWarehouseImpl value,
          $Res Function(_$DeleteWarehouseImpl) then) =
      __$$DeleteWarehouseImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String warehouseId});
}

/// @nodoc
class __$$DeleteWarehouseImplCopyWithImpl<$Res>
    extends _$BlocWarehousesEventsCopyWithImpl<$Res, _$DeleteWarehouseImpl>
    implements _$$DeleteWarehouseImplCopyWith<$Res> {
  __$$DeleteWarehouseImplCopyWithImpl(
      _$DeleteWarehouseImpl _value, $Res Function(_$DeleteWarehouseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouseId = null,
  }) {
    return _then(_$DeleteWarehouseImpl(
      null == warehouseId
          ? _value.warehouseId
          : warehouseId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteWarehouseImpl implements DeleteWarehouse {
  const _$DeleteWarehouseImpl(this.warehouseId);

  @override
  final String warehouseId;

  @override
  String toString() {
    return 'BlocWarehousesEvents.deleteWarehouse(warehouseId: $warehouseId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteWarehouseImpl &&
            (identical(other.warehouseId, warehouseId) ||
                other.warehouseId == warehouseId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, warehouseId);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteWarehouseImplCopyWith<_$DeleteWarehouseImpl> get copyWith =>
      __$$DeleteWarehouseImplCopyWithImpl<_$DeleteWarehouseImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) {
    return deleteWarehouse(warehouseId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) {
    return deleteWarehouse?.call(warehouseId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) {
    if (deleteWarehouse != null) {
      return deleteWarehouse(warehouseId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) {
    return deleteWarehouse(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) {
    return deleteWarehouse?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) {
    if (deleteWarehouse != null) {
      return deleteWarehouse(this);
    }
    return orElse();
  }
}

abstract class DeleteWarehouse implements BlocWarehousesEvents {
  const factory DeleteWarehouse(final String warehouseId) =
      _$DeleteWarehouseImpl;

  String get warehouseId;

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteWarehouseImplCopyWith<_$DeleteWarehouseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClearErrorImplCopyWith<$Res> {
  factory _$$ClearErrorImplCopyWith(
          _$ClearErrorImpl value, $Res Function(_$ClearErrorImpl) then) =
      __$$ClearErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ClearErrorImplCopyWithImpl<$Res>
    extends _$BlocWarehousesEventsCopyWithImpl<$Res, _$ClearErrorImpl>
    implements _$$ClearErrorImplCopyWith<$Res> {
  __$$ClearErrorImplCopyWithImpl(
      _$ClearErrorImpl _value, $Res Function(_$ClearErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesEvents
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ClearErrorImpl implements ClearError {
  const _$ClearErrorImpl();

  @override
  String toString() {
    return 'BlocWarehousesEvents.clearError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ClearErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadWarehouses,
    required TResult Function(String query) searchWarehouses,
    required TResult Function(WarehouseType? type) filterWarehousesByType,
    required TResult Function(Warehouse warehouse) addWarehouse,
    required TResult Function(Warehouse warehouse) updateWarehouse,
    required TResult Function(String warehouseId) deleteWarehouse,
    required TResult Function() clearError,
  }) {
    return clearError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadWarehouses,
    TResult? Function(String query)? searchWarehouses,
    TResult? Function(WarehouseType? type)? filterWarehousesByType,
    TResult? Function(Warehouse warehouse)? addWarehouse,
    TResult? Function(Warehouse warehouse)? updateWarehouse,
    TResult? Function(String warehouseId)? deleteWarehouse,
    TResult? Function()? clearError,
  }) {
    return clearError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadWarehouses,
    TResult Function(String query)? searchWarehouses,
    TResult Function(WarehouseType? type)? filterWarehousesByType,
    TResult Function(Warehouse warehouse)? addWarehouse,
    TResult Function(Warehouse warehouse)? updateWarehouse,
    TResult Function(String warehouseId)? deleteWarehouse,
    TResult Function()? clearError,
    required TResult orElse(),
  }) {
    if (clearError != null) {
      return clearError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadWarehouses value) loadWarehouses,
    required TResult Function(SearchWarehouses value) searchWarehouses,
    required TResult Function(FilterWarehousesByType value)
        filterWarehousesByType,
    required TResult Function(AddWarehouse value) addWarehouse,
    required TResult Function(UpdateWarehouse value) updateWarehouse,
    required TResult Function(DeleteWarehouse value) deleteWarehouse,
    required TResult Function(ClearError value) clearError,
  }) {
    return clearError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadWarehouses value)? loadWarehouses,
    TResult? Function(SearchWarehouses value)? searchWarehouses,
    TResult? Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult? Function(AddWarehouse value)? addWarehouse,
    TResult? Function(UpdateWarehouse value)? updateWarehouse,
    TResult? Function(DeleteWarehouse value)? deleteWarehouse,
    TResult? Function(ClearError value)? clearError,
  }) {
    return clearError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadWarehouses value)? loadWarehouses,
    TResult Function(SearchWarehouses value)? searchWarehouses,
    TResult Function(FilterWarehousesByType value)? filterWarehousesByType,
    TResult Function(AddWarehouse value)? addWarehouse,
    TResult Function(UpdateWarehouse value)? updateWarehouse,
    TResult Function(DeleteWarehouse value)? deleteWarehouse,
    TResult Function(ClearError value)? clearError,
    required TResult orElse(),
  }) {
    if (clearError != null) {
      return clearError(this);
    }
    return orElse();
  }
}

abstract class ClearError implements BlocWarehousesEvents {
  const factory ClearError() = _$ClearErrorImpl;
}

/// @nodoc
mixin _$BlocWarehousesState {
  List<Warehouse> get warehouses => throw _privateConstructorUsedError;
  List<Warehouse> get filteredWarehouses => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String get searchQuery => throw _privateConstructorUsedError;
  WarehouseType? get selectedType => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of BlocWarehousesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BlocWarehousesStateCopyWith<BlocWarehousesState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocWarehousesStateCopyWith<$Res> {
  factory $BlocWarehousesStateCopyWith(
          BlocWarehousesState value, $Res Function(BlocWarehousesState) then) =
      _$BlocWarehousesStateCopyWithImpl<$Res, BlocWarehousesState>;
  @useResult
  $Res call(
      {List<Warehouse> warehouses,
      List<Warehouse> filteredWarehouses,
      bool isLoading,
      String searchQuery,
      WarehouseType? selectedType,
      String? error});
}

/// @nodoc
class _$BlocWarehousesStateCopyWithImpl<$Res, $Val extends BlocWarehousesState>
    implements $BlocWarehousesStateCopyWith<$Res> {
  _$BlocWarehousesStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocWarehousesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouses = null,
    Object? filteredWarehouses = null,
    Object? isLoading = null,
    Object? searchQuery = null,
    Object? selectedType = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      warehouses: null == warehouses
          ? _value.warehouses
          : warehouses // ignore: cast_nullable_to_non_nullable
              as List<Warehouse>,
      filteredWarehouses: null == filteredWarehouses
          ? _value.filteredWarehouses
          : filteredWarehouses // ignore: cast_nullable_to_non_nullable
              as List<Warehouse>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      selectedType: freezed == selectedType
          ? _value.selectedType
          : selectedType // ignore: cast_nullable_to_non_nullable
              as WarehouseType?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlocWarehousesStateImplCopyWith<$Res>
    implements $BlocWarehousesStateCopyWith<$Res> {
  factory _$$BlocWarehousesStateImplCopyWith(_$BlocWarehousesStateImpl value,
          $Res Function(_$BlocWarehousesStateImpl) then) =
      __$$BlocWarehousesStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Warehouse> warehouses,
      List<Warehouse> filteredWarehouses,
      bool isLoading,
      String searchQuery,
      WarehouseType? selectedType,
      String? error});
}

/// @nodoc
class __$$BlocWarehousesStateImplCopyWithImpl<$Res>
    extends _$BlocWarehousesStateCopyWithImpl<$Res, _$BlocWarehousesStateImpl>
    implements _$$BlocWarehousesStateImplCopyWith<$Res> {
  __$$BlocWarehousesStateImplCopyWithImpl(_$BlocWarehousesStateImpl _value,
      $Res Function(_$BlocWarehousesStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocWarehousesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? warehouses = null,
    Object? filteredWarehouses = null,
    Object? isLoading = null,
    Object? searchQuery = null,
    Object? selectedType = freezed,
    Object? error = freezed,
  }) {
    return _then(_$BlocWarehousesStateImpl(
      warehouses: null == warehouses
          ? _value._warehouses
          : warehouses // ignore: cast_nullable_to_non_nullable
              as List<Warehouse>,
      filteredWarehouses: null == filteredWarehouses
          ? _value._filteredWarehouses
          : filteredWarehouses // ignore: cast_nullable_to_non_nullable
              as List<Warehouse>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      selectedType: freezed == selectedType
          ? _value.selectedType
          : selectedType // ignore: cast_nullable_to_non_nullable
              as WarehouseType?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$BlocWarehousesStateImpl implements _BlocWarehousesState {
  const _$BlocWarehousesStateImpl(
      {required final List<Warehouse> warehouses,
      required final List<Warehouse> filteredWarehouses,
      required this.isLoading,
      required this.searchQuery,
      required this.selectedType,
      required this.error})
      : _warehouses = warehouses,
        _filteredWarehouses = filteredWarehouses;

  final List<Warehouse> _warehouses;
  @override
  List<Warehouse> get warehouses {
    if (_warehouses is EqualUnmodifiableListView) return _warehouses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_warehouses);
  }

  final List<Warehouse> _filteredWarehouses;
  @override
  List<Warehouse> get filteredWarehouses {
    if (_filteredWarehouses is EqualUnmodifiableListView)
      return _filteredWarehouses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredWarehouses);
  }

  @override
  final bool isLoading;
  @override
  final String searchQuery;
  @override
  final WarehouseType? selectedType;
  @override
  final String? error;

  @override
  String toString() {
    return 'BlocWarehousesState(warehouses: $warehouses, filteredWarehouses: $filteredWarehouses, isLoading: $isLoading, searchQuery: $searchQuery, selectedType: $selectedType, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlocWarehousesStateImpl &&
            const DeepCollectionEquality()
                .equals(other._warehouses, _warehouses) &&
            const DeepCollectionEquality()
                .equals(other._filteredWarehouses, _filteredWarehouses) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.selectedType, selectedType) ||
                other.selectedType == selectedType) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_warehouses),
      const DeepCollectionEquality().hash(_filteredWarehouses),
      isLoading,
      searchQuery,
      selectedType,
      error);

  /// Create a copy of BlocWarehousesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BlocWarehousesStateImplCopyWith<_$BlocWarehousesStateImpl> get copyWith =>
      __$$BlocWarehousesStateImplCopyWithImpl<_$BlocWarehousesStateImpl>(
          this, _$identity);
}

abstract class _BlocWarehousesState implements BlocWarehousesState {
  const factory _BlocWarehousesState(
      {required final List<Warehouse> warehouses,
      required final List<Warehouse> filteredWarehouses,
      required final bool isLoading,
      required final String searchQuery,
      required final WarehouseType? selectedType,
      required final String? error}) = _$BlocWarehousesStateImpl;

  @override
  List<Warehouse> get warehouses;
  @override
  List<Warehouse> get filteredWarehouses;
  @override
  bool get isLoading;
  @override
  String get searchQuery;
  @override
  WarehouseType? get selectedType;
  @override
  String? get error;

  /// Create a copy of BlocWarehousesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BlocWarehousesStateImplCopyWith<_$BlocWarehousesStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
