import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/repositories/index.dart';
import 'package:sphere/features/_initial/storage/presentation/add_material.dart';
import 'package:sphere/features/_initial/storage/presentation/edit_material.dart';
import 'package:sphere/features/_initial/storage/presentation/material_card.dart';
import 'package:sphere/features/_initial/storage/presentation/transfer.dart';
import 'package:sphere/features/_initial/storage/presentation/write_off_material.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class WarehouseContentsScreen extends StatefulWidget {
  const WarehouseContentsScreen({
    super.key,
    @PathParam('warehouseId') required this.warehouseId,
  });

  final String warehouseId;

  @override
  State<WarehouseContentsScreen> createState() =>
      _WarehouseContentsScreenState();
}

class _WarehouseContentsScreenState extends State<WarehouseContentsScreen>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(latency: 500);

  List<MaterialModel> _materials = [];
  Warehouse? _warehouse;
  bool _isLoading = false;
  bool _isLoadingWarehouse = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadWarehouseInfo();
    _loadMaterials();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadWarehouseInfo() async {
    setState(() {
      _isLoadingWarehouse = true;
    });

    try {
      // Load warehouse information
      // Note: This would need to be implemented in the API
      // For now, we'll create a placeholder
      setState(() {
        _warehouse = Warehouse(
          id: widget.warehouseId,
          name: 'Склад #${widget.warehouseId}',
          type: WarehouseType.general,
          address: 'Загрузка...',
        );
      });
      _setAppBarConfig();
    } catch (e) {
      debugPrint('Error loading warehouse info: $e');
    } finally {
      setState(() {
        _isLoadingWarehouse = false;
      });
    }
  }

  Future<void> _loadMaterials() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final searchQuery =
          _searchController.text.isNotEmpty ? _searchController.text : null;

      // Search materials by warehouse ID
      final result = await StorageRepository.search(SearchModel(
        filters: SearchFiltersModel(
          query: searchQuery,
          // Add warehouse filter when API supports it
          // warehouseId: widget.warehouseId,
        ),
      ));

      final items = result?.data?.items ?? [];

      setState(() {
        _materials = items;
      });
    } catch (e) {
      debugPrint('Error loading materials: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onSearchChanged(String value) {
    _debouncer.run(_loadMaterials);
  }

  void _setAppBarConfig() {
    final warehouseName = _warehouse?.name ?? 'Склад';

    final newConfig = CustomAppBarConfig(
      title: warehouseName,
      description: 'Содержимое склада • ${_materials.length} позиций',
      rightPart: CustomAppBarFeatures.getPopupMenu(
        context: context,
        children: [
          CustomDropdownMenuItem(
            onTap: () {
              CustomDropdownMenu.instance.hide();
              showBaseDialog(
                context,
                maxWidth: 500,
                builder: (context) => AddMaterialBody(
                  refresher: _loadMaterials,
                  // warehouseId: widget.warehouseId, // When API supports it
                ),
              );
            },
            icon: Assets.icons.add,
            text: 'Добавить материал',
            description: 'Добавить материал на склад',
          ),
          CustomDropdownMenuItem(
            onTap: () {
              CustomDropdownMenu.instance.hide();
              _loadMaterials();
            },
            icon: Assets.icons.repeat,
            text: 'Обновить',
            description: 'Обновить список материалов',
          ),
        ],
      ),
      isLoading: _isLoading || _isLoadingWarehouse,
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Wrapper(
      body: Column(
        children: [
          // Warehouse info and search
          Container(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Warehouse info card
                if (_warehouse != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: isDarkTheme
                          ? AppColors.darkSurface
                          : AppColors.lightSurface,
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: isDarkTheme
                            ? AppColors.darkStroke
                            : AppColors.lightStroke,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8.0),
                          decoration: BoxDecoration(
                            color: _getTypeColor(_warehouse!.type)
                                .withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: SVG(
                            _getTypeIcon(_warehouse!.type),
                            width: 24.0,
                            color: _getTypeColor(_warehouse!.type),
                          ),
                        ),
                        const SizedBox(width: 12.0),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _warehouse!.name,
                                style: Fonts.titleSmall.merge(
                                  TextStyle(
                                    color: isDarkTheme
                                        ? AppColors.darkPrimary
                                        : AppColors.lightPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4.0),
                              Text(
                                _getTypeLabel(_warehouse!.type),
                                style: Fonts.bodySmall.merge(
                                  TextStyle(
                                    color: isDarkTheme
                                        ? AppColors.darkDescription
                                        : AppColors.lightDescription,
                                  ),
                                ),
                              ),
                              if (_warehouse!.address != null) ...[
                                const SizedBox(height: 4.0),
                                Row(
                                  children: [
                                    SVG(
                                      Assets.icons.warehouse,
                                      width: 12.0,
                                      color: isDarkTheme
                                          ? AppColors.darkDescription
                                          : AppColors.lightDescription,
                                    ),
                                    const SizedBox(width: 4.0),
                                    Expanded(
                                      child: Text(
                                        _warehouse!.address!,
                                        style: Fonts.labelSmall.merge(
                                          TextStyle(
                                            color: isDarkTheme
                                                ? AppColors.darkDescription
                                                : AppColors.lightDescription,
                                          ),
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16.0),
                ],

                // Search field
                TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  style: Fonts.labelSmall,
                  decoration: InputDecoration(
                    hintText: 'Поиск материалов на складе...',
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: SVG(
                        Assets.icons.search,
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Materials grid
          Expanded(
            child: _isLoading
                ? _buildLoadingGrid()
                : _materials.isEmpty
                    ? _buildEmptyState()
                    : _buildMaterialsGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: MasonryGridView.count(
        crossAxisCount: _getCrossAxisCount(),
        mainAxisSpacing: 12.0,
        crossAxisSpacing: 12.0,
        itemCount: 6,
        itemBuilder: (context, index) {
          return const MaterialCard(
            isLoading: true,
            material: MaterialModel(),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SVG(
            Assets.icons.warehouse,
            width: 64.0,
            color: isDarkTheme
                ? AppColors.darkDescription
                : AppColors.lightDescription,
          ),
          const SizedBox(height: 16.0),
          Text(
            'Склад пуст',
            style: Fonts.titleMedium.merge(
              TextStyle(
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
              ),
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            'На этом складе пока нет материалов',
            style: Fonts.bodyMedium.merge(
              TextStyle(
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
              ),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24.0),
          CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () {
              showBaseDialog(
                context,
                maxWidth: 500,
                builder: (context) => AddMaterialBody(
                  refresher: _loadMaterials,
                ),
              );
            },
            text: 'Добавить материал',
          ),
        ],
      ),
    );
  }

  Widget _buildMaterialsGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: MasonryGridView.count(
        crossAxisCount: _getCrossAxisCount(),
        mainAxisSpacing: 12.0,
        crossAxisSpacing: 12.0,
        itemCount: _materials.length,
        itemBuilder: (context, index) {
          final material = _materials[index];
          return MaterialCard(
            withProjectName: true,
            material: material,
            onSecondaryTapDown: (details) =>
                _showMaterialContextMenu(material, details),
          );
        },
      ),
    );
  }

  void _showMaterialContextMenu(
      MaterialModel material, TapDownDetails details) {
    CustomDropdownMenu.instance.hide();
    CustomDropdownMenu.instance.show(
      context: context,
      position: details.globalPosition,
      items: [
        CustomDropdownMenuItem(
          icon: Assets.icons.edit,
          text: 'Редактировать',
          description: 'Корректировка значений',
          onTap: () {
            CustomDropdownMenu.instance.hide();
            showBaseDialog(
              context,
              maxWidth: 500,
              builder: (context) => EditMaterialBody(
                material: material,
                refresher: _loadMaterials,
              ),
            );
          },
        ),
        CustomDropdownMenuItem(
          icon: Assets.icons.import,
          text: 'Перемещение',
          description: 'Перемещение между складами',
          onTap: () {
            CustomDropdownMenu.instance.hide();
            showBaseDialog(
              context,
              maxWidth: 500,
              builder: (context) => TransferMaterialBody(
                material: material,
                refresher: _loadMaterials,
              ),
            );
          },
        ),
        CustomDropdownMenuItem(
          icon: Assets.icons.delete,
          text: 'Списание',
          description: 'Списание материала',
          onTap: () {
            CustomDropdownMenu.instance.hide();
            showBaseDialog(
              context,
              maxWidth: 500,
              builder: (context) => WriteOffMaterialBody(
                material: material,
                refresher: _loadMaterials,
              ),
            );
          },
        ),
      ],
    );
  }

  int _getCrossAxisCount() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) return 4;
    if (screenWidth > 800) return 3;
    if (screenWidth > 600) return 2;
    return 1;
  }

  Color _getTypeColor(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return AppColors.lightPrimary;
      case WarehouseType.contractor:
        return Colors.green;
      case WarehouseType.virtualProject:
        return Colors.purple;
    }
  }

  String _getTypeIcon(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return Assets.icons.warehouse;
      case WarehouseType.contractor:
        return Assets.icons.warehouse;
      case WarehouseType.virtualProject:
        return Assets.icons.help;
    }
  }

  String _getTypeLabel(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return 'Общий склад';
      case WarehouseType.contractor:
        return 'Склад контрагента';
      case WarehouseType.virtualProject:
        return 'Виртуальный склад проекта';
    }
  }
}
