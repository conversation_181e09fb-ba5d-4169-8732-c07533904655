import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/repositories/index.dart';
import 'package:sphere/features/production/data/models/index.dart';
import 'package:sphere/features/production/data/repositories/index.dart';
import 'package:sphere/features/production/presentation/card.dart';
import 'package:sphere/features/project/data/models/index.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class ProductionScreen extends StatefulWidget {
  const ProductionScreen({super.key, this.projectId, this.projectName});

  final String? projectId;
  final String? projectName;

  @override
  State<ProductionScreen> createState() => _ProductionScreenState();
}

class _ProductionScreenState extends State<ProductionScreen> {
  bool _isLoading = false;
  GetProductionItemsModel _data = GetProductionItemsModel();
  List<MaterialModel> _materials = [];
  bool? isForce = false;

  // State for popup
  final Map<String, MaterialAction> _materialActions = {};

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    if (widget.projectId == null) return;

    setState(() {
      _isLoading = true;
    });

    final result = await ProductionRepository.search(widget.projectId!);
    final preparedResult = result?.data;

    setState(() {
      if (preparedResult != null) _data = preparedResult;
      _isLoading = false;
    });
  }

  Future<void> _loadProjectStorageData({String searchQuery = ''}) async {
    setState(() {
      _isLoading = true;
    });

    final search = SearchModel(
      filters: SearchFiltersModel(
        query: searchQuery.isEmpty ? null : searchQuery,
        projectId: widget.projectId,
      ),
    );

    final result = await StorageRepository.search(search);
    final items = result?.data?.items;

    setState(() {
      _materials = items ?? [];
      _materialActions.clear();
      for (var material in _materials) {
        _materialActions[material.id ?? ''] = MaterialAction(
          transferQuantity: material.quantities?.ready?.toDouble() ?? 0.0,
        );
      }
      _isLoading = false;
    });
  }

  Future<void> _archive() async {
    if (widget.projectId == null) return;

    setState(() {
      _isLoading = true;
    });

    final transferMaterials = _materialActions.entries
        .where((entry) => entry.value.transferQuantity > 0)
        .map((entry) => ArchiveUnitModel(
              storageItemId: entry.key,
              quantity: entry.value.transferQuantity,
              comment: entry.value.transferComment,
            ))
        .toList();

    final writeOffMaterials = _materialActions.entries
        .where((entry) => entry.value.writeOffQuantity > 0)
        .map((entry) => ArchiveUnitModel(
              storageItemId: entry.key,
              quantity: entry.value.writeOffQuantity,
              comment: entry.value.writeOffComment,
            ))
        .toList();

    final result = await ProjectRepository.archive(
      projectId: widget.projectId!,
      data: ArchiveInputModel(
        transferMaterials: transferMaterials,
        writeOffMaterials: writeOffMaterials,
        force: isForce,
      ),
    );

    setState(() {
      _isLoading = false;
    });

    if (result?.statusCode != null && result!.statusCode! < 400) {
      if (mounted) {
        context.router.push(SplashRoute());
      }
    }
  }

  Future<void> _endProjectPopup() async {
    await _loadProjectStorageData();

    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final errorColor = isDarkTheme ? AppColors.darkError : AppColors.lightError;

    return showBaseDialog(
      context,
      maxWidth: 1900,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Завершение проекта',
              style: Fonts.titleMedium.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Укажите, что делать с оставшимися материалами:',
              style: Fonts.bodyMedium,
            ),
            const SizedBox(height: 12),
            if (_materials.isEmpty || _isLoading)
              Center(
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : Text(
                        'Нет материалов для обработки',
                        style: Fonts.bodyMedium,
                      ),
              )
            else
              Expanded(
                child: Column(
                  children: [
                    Row(children: [
                      SizedBox(width: 32.0),
                      Expanded(
                        child: Text(
                          'Название',
                          style: Fonts.bodySmall.merge(TextStyle(
                            color: isDarkTheme
                                ? AppColors.darkDescription
                                : AppColors.lightDescription,
                          )),
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Expanded(
                          child: Text(
                        'Количество на склад',
                        style: Fonts.bodySmall.merge(TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        )),
                      )),
                      SizedBox(width: 8.0),
                      Expanded(
                          child: Text(
                        'Комментарий',
                        style: Fonts.bodySmall.merge(TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        )),
                      )),
                      SizedBox(width: 8.0),
                      Expanded(
                          child: Text(
                        'Количество на списание',
                        style: Fonts.bodySmall.merge(TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        )),
                      )),
                      SizedBox(width: 8.0),
                      Expanded(
                          child: Text(
                        'Комментарий',
                        style: Fonts.bodySmall.merge(TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        )),
                      )),
                    ]),
                    SizedBox(height: 8.0),
                    Divider(height: 1),
                    Expanded(
                      child: ListView.separated(
                        padding: EdgeInsets.symmetric(vertical: 12.0),
                        shrinkWrap: true,
                        physics: const ClampingScrollPhysics(),
                        itemCount: _materials.length,
                        itemBuilder: (context, index) {
                          final material = _materials[index];
                          final action = _materialActions[material.id ?? '']!;

                          return MaterialActionCard(
                            material: material,
                            action: action,
                            onChanged: (updatedAction) {
                              setDialogState(() {
                                _materialActions[material.id ?? ''] =
                                    updatedAction;
                              });
                            },
                          );
                        },
                        separatorBuilder: (context, index) =>
                            const SizedBox(height: 16),
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    minimumSize: const Size(120, 48),
                  ),
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Отмена',
                    style: Fonts.labelMedium,
                  ),
                ),
                FilledButton(
                  style: FilledButton.styleFrom(
                    backgroundColor: errorColor,
                    minimumSize: const Size(180, 48),
                  ),
                  onPressed: _materialActions.entries.any((entry) =>
                          entry.value.transferQuantity > 0 ||
                          entry.value.writeOffQuantity > 0)
                      ? () {
                          Navigator.pop(context);
                          _archive();
                        }
                      : null,
                  child: Text(
                    'Завершить проект',
                    style: Fonts.labelMedium,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      appBar: CustomAppBar(
        title: widget.projectName,
        description: 'Элементов: ${_data.totalItems}',
        isLoading: _isLoading,
        rightPart: Row(
          children: [
            TextButton(
              onPressed: _endProjectPopup,
              child: Row(
                children: [
                  SVG(Assets.icons.check),
                  const SizedBox(width: 6.0),
                  Text(
                    'Завершить проект',
                    style: Fonts.labelSmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(12.0),
        itemCount: _data.items?.length ?? 0,
        itemBuilder: (context, index) {
          final item = _data.items![index];

          return ProductionCard(
            projectId: widget.projectId ?? '',
            data: item,
            refresher: init,
          );
        },
        separatorBuilder: (context, index) => const SizedBox(height: 8.0),
      ),
    );
  }
}

// Helper class to manage material actions
class MaterialAction {
  double transferQuantity;
  String transferComment;
  double writeOffQuantity;
  String writeOffComment;

  MaterialAction({
    this.transferQuantity = 0.0,
    this.transferComment = '',
    this.writeOffQuantity = 0.0,
    this.writeOffComment = '',
  });
}

// Widget for individual material action card
class MaterialActionCard extends StatefulWidget {
  final MaterialModel material;
  final MaterialAction action;
  final ValueChanged<MaterialAction> onChanged;

  const MaterialActionCard({
    super.key,
    required this.material,
    required this.action,
    required this.onChanged,
  });

  @override
  _MaterialActionCardState createState() => _MaterialActionCardState();
}

class _MaterialActionCardState extends State<MaterialActionCard> {
  late TextEditingController _transferController;
  late TextEditingController _writeOffController;

  @override
  void initState() {
    super.initState();
    _transferController = TextEditingController(
      text: widget.action.transferQuantity.toStringAsFixed(2),
    );
    _writeOffController = TextEditingController(
      text: widget.action.writeOffQuantity.toStringAsFixed(2),
    );
  }

  @override
  void didUpdateWidget(covariant MaterialActionCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update controllers if the action has changed, and avoid updating the focused field
    if (oldWidget.action != widget.action) {
      final focusNode = FocusScope.of(context).focusedChild;
      if (focusNode != _transferController &&
          widget.action.transferQuantity.toStringAsFixed(2) !=
              _transferController.text) {
        _transferController.text =
            widget.action.transferQuantity.toStringAsFixed(2);
      }
      if (focusNode != _writeOffController &&
          widget.action.writeOffQuantity.toStringAsFixed(2) !=
              _writeOffController.text) {
        _writeOffController.text =
            widget.action.writeOffQuantity.toStringAsFixed(2);
      }
    }
  }

  @override
  void dispose() {
    _transferController.dispose();
    _writeOffController.dispose();
    super.dispose();
  }

  void _updateQuantities({double? newTransfer, double? newWriteOff}) {
    final maxQuantity = widget.material.quantities?.ready?.toDouble() ?? 0.0;
    double transfer = newTransfer ?? widget.action.transferQuantity;
    double writeOff = newWriteOff ?? widget.action.writeOffQuantity;

    // Ensure individual quantities don't exceed maxQuantity
    transfer = transfer.clamp(0.0, maxQuantity);
    writeOff = writeOff.clamp(0.0, maxQuantity);

    // Adjust the other field to keep total within maxQuantity
    if (newTransfer != null) {
      writeOff = (maxQuantity - transfer).clamp(0.0, maxQuantity);
    } else if (newWriteOff != null) {
      transfer = (maxQuantity - writeOff).clamp(0.0, maxQuantity);
    }

    // Update the non-edited controller only
    final focusNode = FocusScope.of(context).focusedChild;
    if (newTransfer != null && focusNode != _writeOffController) {
      _writeOffController.text = writeOff.toStringAsFixed(2);
    } else if (newWriteOff != null && focusNode != _transferController) {
      _transferController.text = transfer.toStringAsFixed(2);
    }

    // Notify parent
    widget.onChanged(
      MaterialAction(
        transferQuantity: transfer,
        transferComment: widget.action.transferComment,
        writeOffQuantity: writeOff,
        writeOffComment: widget.action.writeOffComment,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final maxQuantity = widget.material.quantities?.ready?.toDouble() ?? 0.0;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SVG(Assets.icons.widgets),
        const SizedBox(width: 8),
        Expanded(
          flex: 1,
          child: Text(
            widget.material.nomenclature?.name ?? 'Без названия',
            style: Fonts.labelSmall,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextField(
            controller: _transferController,
            decoration: const InputDecoration(
              hintText: 'На склад',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            onChanged: (value) {
              final newQuantity = double.tryParse(value) ?? 0.0;
              if (newQuantity <= maxQuantity) {
                _updateQuantities(newTransfer: newQuantity);
              }
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'Комментарий',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            ),
            onChanged: (value) {
              widget.onChanged(
                MaterialAction(
                  transferQuantity: widget.action.transferQuantity,
                  transferComment: value,
                  writeOffQuantity: widget.action.writeOffQuantity,
                  writeOffComment: widget.action.writeOffComment,
                ),
              );
            },
          ),
        ),
        SizedBox(height: 24.0, child: VerticalDivider(width: 12.0)),
        Expanded(
          child: TextField(
            controller: _writeOffController,
            decoration: const InputDecoration(
              hintText: 'Списать',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            onChanged: (value) {
              final newQuantity = double.tryParse(value) ?? 0.0;
              if (newQuantity <= maxQuantity) {
                _updateQuantities(newWriteOff: newQuantity);
              }
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'Комментарий',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            ),
            onChanged: (value) {
              widget.onChanged(
                MaterialAction(
                  transferQuantity: widget.action.transferQuantity,
                  transferComment: widget.action.transferComment,
                  writeOffQuantity: widget.action.writeOffQuantity,
                  writeOffComment: value,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
