import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/production/data/models/index.dart';
import 'package:sphere/features/production/data/models/production.dart';
import 'package:sphere/features/production/data/repositories/index.dart';
import 'package:sphere/features/tasks/data/models/comment/comment.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/utility/comment.dart';

class ProductionCard extends StatefulWidget {
  const ProductionCard({
    super.key,
    required this.projectId,
    required this.data,
    required this.refresher,
  });

  final String projectId;
  final ProductionModel data;
  final void Function() refresher;

  @override
  State<ProductionCard> createState() => _ProductionCardState();
}

class _ProductionCardState extends State<ProductionCard> {
  bool _isLoading = false;
  ProductionTaskStatus _newStatus = ProductionTaskStatus.readyForInspection;
  final TextEditingController _commentController = TextEditingController();
  DateTime _releaseDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _newStatus = widget.data.productionTask?.status ??
        ProductionTaskStatus.readyForInspection;

    final now = DateTime.now();
    if (widget.data.product?.parameters?.releaseDate != null) {
      _releaseDate = widget.data.product!.parameters!.releaseDate!.isBefore(now)
          ? now
          : widget.data.product!.parameters!.releaseDate!;
    } else {
      _releaseDate = now;
    }
  }

  void _setReleaseDate() async {
    if (widget.data.productId == null &&
        widget.data.product!.parameters != null) {
      return;
    }
    setState(() {
      _isLoading = true;
    });

    ProductRepository.updateParameters(
      widget.data.productId!,
      widget.data.product!.parameters!.copyWith(
        releaseDate: _releaseDate.add(Duration(hours: 8)),
      ),
    );

    setState(() {
      _isLoading = false;
    });
    widget.refresher();
  }

  void _changeStatus() async {
    setState(() {
      _isLoading = true;
    });

    await ProductionRepository.taskUpdateStatus(ChangeStatusProductionModel(
      taskId: widget.data.productionTask?.id,
      status: _newStatus,
      comment: _commentController.text,
    ));

    widget.refresher.call();

    setState(() {
      _isLoading = false;
    });
  }

  void _showChangeStatusDialog() {
    CustomDropdownMenu.instance.hide();
    showDialog(
      context: context,
      builder: (context) {
        final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

        return AlertDialog(
          backgroundColor: isDarkTheme
              ? AppColors.darkBackground
              : AppColors.lightBackground,
          title: const Text('Изменение статуса', style: Fonts.titleSmall),
          content: StatefulBuilder(builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_isLoading) ...[
                  const Center(child: CircularProgressIndicator()),
                  const SizedBox(height: 12.0),
                ],
                ...ProductionTaskStatus.values.map((status) {
                  return RadioListTile<ProductionTaskStatus>(
                    value: status,
                    selected: status == _newStatus,
                    title: Text(status.getName(), style: Fonts.labelSmall),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _newStatus = value);
                      }
                    },
                    groupValue: _newStatus,
                  );
                }),
                const SizedBox(height: 12.0),
                TextField(
                  controller: _commentController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    labelText: 'Комментарий',
                    border: OutlineInputBorder(),
                  ),
                  minLines: 1,
                  maxLines: 4,
                ),
              ],
            );
          }),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена', style: Fonts.labelSmall),
            ),
            TextButton(
              onPressed: () {
                _changeStatus();
                Navigator.of(context).pop();
              },
              child: const Text('Подтвердить', style: Fonts.labelSmall),
            ),
          ],
        );
      },
    );
  }

  void _showDetails(BuildContext context) {
    CustomDropdownMenu.instance.hide();

    final task = widget.data.productionTask;
    final comments = task?.statusHistory ?? [];

    showBaseDialog(context, builder: (context) {
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${widget.data.product?.name}.',
            style: Fonts.titleLarge,
          ),
          SizedBox(height: 4.0),
          Text(
            task?.status?.getName() ?? 'status not found',
            style: Fonts.labelSmall.merge(TextStyle(
              color: task?.status?.getColor(),
            )),
          ),
          SizedBox(height: 12.0),
          if (comments.isEmpty)
            Expanded(
              child: Center(
                child: Text(
                  'Комментарии отсутствуют',
                  style: Fonts.labelSmall.merge(
                    TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription),
                  ),
                ),
              ),
            ),
          if (comments.isNotEmpty)
            Expanded(
              child: ListView.separated(
                reverse: true,
                itemCount: comments.length,
                itemBuilder: (context, index) {
                  final comment = comments[index];

                  return Comment(
                    data: CommentModel(
                      userId: comment.userId,
                      user: comment.user,
                      comment: comment.comment,
                      updatedAt: comment.date,
                      createdAt: comment.date,
                    ),
                  );
                },
                separatorBuilder: (_, __) => SizedBox(height: 8.0),
              ),
            )
        ],
      );
    });
  }

  void _showChangePlanDate(BuildContext context) {
    CustomDropdownMenu.instance.hide();

    showBaseDialog(context, builder: (context) {
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

      // Ensure _releaseDate is not before firstDate
      final DateTime firstDate = DateTime.now();
      final DateTime validInitialDate =
          _releaseDate.isBefore(firstDate) ? firstDate : _releaseDate;

      return StatefulBuilder(builder: (context, setState) {
        return Column(children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Планируемая дата выпуска ГП:', style: Fonts.labelSmall),
              Text(
                getDateString(_releaseDate),
                style: Fonts.labelSmall.merge(
                  TextStyle(
                    color: isDarkTheme
                        ? AppColors.darkSecondary
                        : AppColors.lightSecondary,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.0),
          CalendarDatePicker(
            initialDate: validInitialDate,
            firstDate: firstDate,
            lastDate: DateTime.now().add(Duration(days: 730)),
            currentDate: validInitialDate,
            onDateChanged: (DateTime newDate) {
              setState(() {
                _releaseDate = newDate;
              });
            },
          ),
          Row(
            children: [
              Expanded(
                child: CustomElevatedButton(
                  onPressed: () {
                    _setReleaseDate();
                    context.router.maybePop();
                  },
                  text: 'Сохранить',
                ),
              ),
            ],
          ),
        ]);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      onSecondaryTapDown: (details) {
        CustomDropdownMenu.instance.hide();
        CustomDropdownMenu.instance.show(
          context: context,
          items: [
            // CustomDropdownMenuItem(
            //   icon: Assets.icons.openFolder,
            //   text: 'Показать материалы',
            //   onTap: () {},
            // ),
            if (widget.data.productionTask?.id == null)
              CustomDropdownMenuItem(
                icon: Assets.icons.add,
                text: 'Начать выполенение',
                onTap: () {
                  if (widget.data.requiredQuantity == null) return;
                  ProductionRepository.taskCreate(
                    projectId: widget.projectId,
                    productId: widget.data.productId ?? '',
                    quantityRequired: widget.data.requiredQuantity!,
                  );
                },
              ),
            if (widget.data.status != ProductionStatus.ready)
              CustomDropdownMenuItem(
                icon: Assets.icons.edit,
                text: 'Изменить планирумеую дату выпуска ГП',
                onTap: () => _showChangePlanDate(context),
              )
          ],
          position: details.globalPosition,
        );
      },
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(children: [
          Text(
            widget.data.product?.priority.toString() ?? 'Pr',
            style: Fonts.labelSmall.merge(TextStyle(
              color: AppColors.lightSecondary,
            )),
          ),
          SizedBox(width: 12.0),
          Text(widget.data.product?.name ?? 'name', style: Fonts.titleSmall),
          SizedBox(width: 12.0),
          Text(widget.data.status?.getName() ?? 'status',
              style: Fonts.labelSmall.merge(TextStyle(
                color: widget.data.status?.getColor(),
              ))),
          SizedBox(width: 4.0),
          Text('(${widget.data.filledPercentage}%)',
              style: Fonts.labelSmall.merge(TextStyle(
                color: widget.data.status?.getColor(),
              ))),
          Spacer(),
          Text(
              '${widget.data.readyQuantity} / ${widget.data.requiredQuantity} шт.',
              style: Fonts.labelSmall.merge(TextStyle(
                color: AppColors.lightDescription,
              ))),
        ]),
        if (widget.data.product?.parameters?.releaseDate != null)
          SizedBox(height: 12.0),
        if (widget.data.product?.parameters?.releaseDate != null)
          Text(
            'Выпуск до: ${getDateString(widget.data.product!.parameters!.releaseDate!)}',
            style: Fonts.labelSmall,
          ),
        if ((widget.data.product?.operations ?? []).isNotEmpty)
          SizedBox(height: 12.0),
        ...(widget.data.product?.operations ?? []).asMap().entries.map((entry) {
          final index = entry.key;
          final operation = entry.value;

          return Text('${index + 1}. $operation', style: Fonts.bodySmall);
        }),
        if (widget.data.productionTask != null) SizedBox(height: 12.0),
        if (widget.data.productionTask != null)
          CustomCard(
            onTapDown: (details) {
              CustomDropdownMenu.instance.hide();
              CustomDropdownMenu.instance.show(
                context: context,
                items: [
                  CustomDropdownMenuItem(
                    icon: Assets.icons.help,
                    text: 'Показать комментарии',
                    onTap: () {
                      _showDetails(context);
                    },
                  ),
                  CustomDropdownMenuItem(
                    icon: Assets.icons.edit,
                    text: 'Изменить статус',
                    onTap: () {
                      _showChangeStatusDialog();
                    },
                  ),
                ],
                position: details.globalPosition,
              );
            },
            child: Row(children: [
              Text(
                widget.data.productionTask?.status?.getName() ?? 'status',
                style: Fonts.labelSmall.merge(TextStyle(
                    color: widget.data.productionTask?.status?.getColor())),
              ),
            ]),
          ),
      ]),
    );
  }
}
