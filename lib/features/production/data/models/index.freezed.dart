// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GetProductionItemsModel _$GetProductionItemsModelFromJson(
    Map<String, dynamic> json) {
  return _GetProductionItemsModel.fromJson(json);
}

/// @nodoc
mixin _$GetProductionItemsModel {
  List<ProductionModel>? get items => throw _privateConstructorUsedError;
  int? get totalItems => throw _privateConstructorUsedError;

  /// Serializes this GetProductionItemsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GetProductionItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GetProductionItemsModelCopyWith<GetProductionItemsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GetProductionItemsModelCopyWith<$Res> {
  factory $GetProductionItemsModelCopyWith(GetProductionItemsModel value,
          $Res Function(GetProductionItemsModel) then) =
      _$GetProductionItemsModelCopyWithImpl<$Res, GetProductionItemsModel>;
  @useResult
  $Res call({List<ProductionModel>? items, int? totalItems});
}

/// @nodoc
class _$GetProductionItemsModelCopyWithImpl<$Res,
        $Val extends GetProductionItemsModel>
    implements $GetProductionItemsModelCopyWith<$Res> {
  _$GetProductionItemsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GetProductionItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GetProductionItemsModelImplCopyWith<$Res>
    implements $GetProductionItemsModelCopyWith<$Res> {
  factory _$$GetProductionItemsModelImplCopyWith(
          _$GetProductionItemsModelImpl value,
          $Res Function(_$GetProductionItemsModelImpl) then) =
      __$$GetProductionItemsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ProductionModel>? items, int? totalItems});
}

/// @nodoc
class __$$GetProductionItemsModelImplCopyWithImpl<$Res>
    extends _$GetProductionItemsModelCopyWithImpl<$Res,
        _$GetProductionItemsModelImpl>
    implements _$$GetProductionItemsModelImplCopyWith<$Res> {
  __$$GetProductionItemsModelImplCopyWithImpl(
      _$GetProductionItemsModelImpl _value,
      $Res Function(_$GetProductionItemsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of GetProductionItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_$GetProductionItemsModelImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$GetProductionItemsModelImpl implements _GetProductionItemsModel {
  const _$GetProductionItemsModelImpl(
      {final List<ProductionModel>? items, this.totalItems})
      : _items = items;

  factory _$GetProductionItemsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$GetProductionItemsModelImplFromJson(json);

  final List<ProductionModel>? _items;
  @override
  List<ProductionModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalItems;

  @override
  String toString() {
    return 'GetProductionItemsModel(items: $items, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetProductionItemsModelImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), totalItems);

  /// Create a copy of GetProductionItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetProductionItemsModelImplCopyWith<_$GetProductionItemsModelImpl>
      get copyWith => __$$GetProductionItemsModelImplCopyWithImpl<
          _$GetProductionItemsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GetProductionItemsModelImplToJson(
      this,
    );
  }
}

abstract class _GetProductionItemsModel implements GetProductionItemsModel {
  const factory _GetProductionItemsModel(
      {final List<ProductionModel>? items,
      final int? totalItems}) = _$GetProductionItemsModelImpl;

  factory _GetProductionItemsModel.fromJson(Map<String, dynamic> json) =
      _$GetProductionItemsModelImpl.fromJson;

  @override
  List<ProductionModel>? get items;
  @override
  int? get totalItems;

  /// Create a copy of GetProductionItemsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetProductionItemsModelImplCopyWith<_$GetProductionItemsModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ChangeStatusProductionModel _$ChangeStatusProductionModelFromJson(
    Map<String, dynamic> json) {
  return _ChangeStatusProductionModel.fromJson(json);
}

/// @nodoc
mixin _$ChangeStatusProductionModel {
  String? get taskId => throw _privateConstructorUsedError;
  ProductionTaskStatus? get status => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this ChangeStatusProductionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChangeStatusProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChangeStatusProductionModelCopyWith<ChangeStatusProductionModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChangeStatusProductionModelCopyWith<$Res> {
  factory $ChangeStatusProductionModelCopyWith(
          ChangeStatusProductionModel value,
          $Res Function(ChangeStatusProductionModel) then) =
      _$ChangeStatusProductionModelCopyWithImpl<$Res,
          ChangeStatusProductionModel>;
  @useResult
  $Res call({String? taskId, ProductionTaskStatus? status, String? comment});
}

/// @nodoc
class _$ChangeStatusProductionModelCopyWithImpl<$Res,
        $Val extends ChangeStatusProductionModel>
    implements $ChangeStatusProductionModelCopyWith<$Res> {
  _$ChangeStatusProductionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChangeStatusProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? status = freezed,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChangeStatusProductionModelImplCopyWith<$Res>
    implements $ChangeStatusProductionModelCopyWith<$Res> {
  factory _$$ChangeStatusProductionModelImplCopyWith(
          _$ChangeStatusProductionModelImpl value,
          $Res Function(_$ChangeStatusProductionModelImpl) then) =
      __$$ChangeStatusProductionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? taskId, ProductionTaskStatus? status, String? comment});
}

/// @nodoc
class __$$ChangeStatusProductionModelImplCopyWithImpl<$Res>
    extends _$ChangeStatusProductionModelCopyWithImpl<$Res,
        _$ChangeStatusProductionModelImpl>
    implements _$$ChangeStatusProductionModelImplCopyWith<$Res> {
  __$$ChangeStatusProductionModelImplCopyWithImpl(
      _$ChangeStatusProductionModelImpl _value,
      $Res Function(_$ChangeStatusProductionModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChangeStatusProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? status = freezed,
    Object? comment = freezed,
  }) {
    return _then(_$ChangeStatusProductionModelImpl(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ChangeStatusProductionModelImpl
    implements _ChangeStatusProductionModel {
  const _$ChangeStatusProductionModelImpl(
      {this.taskId, this.status, this.comment});

  factory _$ChangeStatusProductionModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ChangeStatusProductionModelImplFromJson(json);

  @override
  final String? taskId;
  @override
  final ProductionTaskStatus? status;
  @override
  final String? comment;

  @override
  String toString() {
    return 'ChangeStatusProductionModel(taskId: $taskId, status: $status, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeStatusProductionModelImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, taskId, status, comment);

  /// Create a copy of ChangeStatusProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeStatusProductionModelImplCopyWith<_$ChangeStatusProductionModelImpl>
      get copyWith => __$$ChangeStatusProductionModelImplCopyWithImpl<
          _$ChangeStatusProductionModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChangeStatusProductionModelImplToJson(
      this,
    );
  }
}

abstract class _ChangeStatusProductionModel
    implements ChangeStatusProductionModel {
  const factory _ChangeStatusProductionModel(
      {final String? taskId,
      final ProductionTaskStatus? status,
      final String? comment}) = _$ChangeStatusProductionModelImpl;

  factory _ChangeStatusProductionModel.fromJson(Map<String, dynamic> json) =
      _$ChangeStatusProductionModelImpl.fromJson;

  @override
  String? get taskId;
  @override
  ProductionTaskStatus? get status;
  @override
  String? get comment;

  /// Create a copy of ChangeStatusProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChangeStatusProductionModelImplCopyWith<_$ChangeStatusProductionModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
