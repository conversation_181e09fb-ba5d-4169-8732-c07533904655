import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/shared/styles/colors.dart';

part 'production.freezed.dart';
part 'production.g.dart';

@freezed
class ProductionModel with _$ProductionModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionModel({
    String? productId,
    ProductModel? product,
    double? requiredQuantity,
    double? readyQuantity,
    ProductionStatus? status,
    double? filledPercentage,
    List<MaterialModel>? materials,
    ProductionTaskModel? productionTask,
  }) = _ProductionModel;

  factory ProductionModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionModelFromJson(json);
}

@freezed
class ProductionTaskModel with _$ProductionTaskModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskModel({
    @JsonKey(name: '_id') String? id,
    String? projcectId,
    String? productId,
    ProductionTaskStatus? status,
    double? quantityRequired,
    List<ProductionMaterialModel>? materials,
    List<ProductionStatusHistoryModel>? statusHistory,
    DateTime? updatedAt,
    DateTime? createdAt,
  }) = _ProductionTaskModel;

  factory ProductionTaskModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionTaskModelFromJson(json);
}

@freezed
class ProductionStatusHistoryModel with _$ProductionStatusHistoryModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionStatusHistoryModel({
    ProductionTaskStatus? status,
    DateTime? date,
    String? userId,
    UserModel? user,
    String? comment,
  }) = _ProductionStatusHistoryModel;

  factory ProductionStatusHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionStatusHistoryModelFromJson(json);
}

@freezed
class ProductionMaterialModel with _$ProductionMaterialModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionMaterialModel({
    String? storageItemId,
    double? requiredQuantity,
    double? issuedQuantity,
    String? materialName,
    UnitType? materialUnit,
  }) = _ProductionMaterialModel;

  factory ProductionMaterialModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionMaterialModelFromJson(json);
}

enum ProductionTaskStatus {
  @JsonValue('materials_requested')
  materialsRequested,
  @JsonValue('materials_issued')
  materialsIssued,
  @JsonValue('assembly_in_progress')
  assemblyInProgress,
  @JsonValue('ready_for_inspection')
  readyForInspection,
  @JsonValue('qc_inspection')
  qcInspection,
  @JsonValue('qc_approved')
  qcApproved;

  String getName() {
    switch (this) {
      case ProductionTaskStatus.materialsRequested:
        return 'Материалы запрошены';
      case ProductionTaskStatus.materialsIssued:
        return 'Материалы на согласовании';
      case ProductionTaskStatus.assemblyInProgress:
        return 'Сборка в процессе';
      case ProductionTaskStatus.readyForInspection:
        return 'Готово для ОТК';
      case ProductionTaskStatus.qcInspection:
        return 'Контроль ОТК';
      case ProductionTaskStatus.qcApproved:
        return 'Принято ОТК';
    }
  }

  Color getColor() {
    switch (this) {
      case ProductionTaskStatus.materialsRequested:
        return AppColors.lightSecondary;
      case ProductionTaskStatus.materialsIssued:
        return AppColors.lightWarning;
      case ProductionTaskStatus.assemblyInProgress:
        return AppColors.lightSecondary;
      case ProductionTaskStatus.readyForInspection:
        return AppColors.lightSecondary;
      case ProductionTaskStatus.qcInspection:
        return AppColors.lightWarning;
      case ProductionTaskStatus.qcApproved:
        return AppColors.lightSuccess;
    }
  }
}

enum ProductionStatus {
  ready,
  partial,
  @JsonValue('not_ready')
  notReady,
  @JsonValue('pending_sub_assemblies')
  pendingSubAssemblies;

  String getName() {
    switch (this) {
      case ProductionStatus.ready:
        return 'Полностью готов к производству';
      case ProductionStatus.partial:
        return 'Частично готов к производству (есть некоторые материалы)';
      case ProductionStatus.notReady:
        return 'Не готов (нет материалов)';
      case ProductionStatus.pendingSubAssemblies:
        return 'Все материалы есть, но не все дочерние сборки завершены';
    }
  }

  Color getColor() {
    switch (this) {
      case ProductionStatus.ready:
        return AppColors.lightSuccess;
      case ProductionStatus.partial:
        return AppColors.lightWarning;
      case ProductionStatus.notReady:
        return AppColors.lightSecondary;
      case ProductionStatus.pendingSubAssemblies:
        return AppColors.lightWarning;
    }
  }
}
