// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'production.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductionModel _$ProductionModelFromJson(Map<String, dynamic> json) {
  return _ProductionModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionModel {
  String? get productId => throw _privateConstructorUsedError;
  ProductModel? get product => throw _privateConstructorUsedError;
  double? get requiredQuantity => throw _privateConstructorUsedError;
  double? get readyQuantity => throw _privateConstructorUsedError;
  ProductionStatus? get status => throw _privateConstructorUsedError;
  double? get filledPercentage => throw _privateConstructorUsedError;
  List<MaterialModel>? get materials => throw _privateConstructorUsedError;
  ProductionTaskModel? get productionTask => throw _privateConstructorUsedError;

  /// Serializes this ProductionModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionModelCopyWith<ProductionModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionModelCopyWith<$Res> {
  factory $ProductionModelCopyWith(
          ProductionModel value, $Res Function(ProductionModel) then) =
      _$ProductionModelCopyWithImpl<$Res, ProductionModel>;
  @useResult
  $Res call(
      {String? productId,
      ProductModel? product,
      double? requiredQuantity,
      double? readyQuantity,
      ProductionStatus? status,
      double? filledPercentage,
      List<MaterialModel>? materials,
      ProductionTaskModel? productionTask});

  $ProductModelCopyWith<$Res>? get product;
  $ProductionTaskModelCopyWith<$Res>? get productionTask;
}

/// @nodoc
class _$ProductionModelCopyWithImpl<$Res, $Val extends ProductionModel>
    implements $ProductionModelCopyWith<$Res> {
  _$ProductionModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? product = freezed,
    Object? requiredQuantity = freezed,
    Object? readyQuantity = freezed,
    Object? status = freezed,
    Object? filledPercentage = freezed,
    Object? materials = freezed,
    Object? productionTask = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      readyQuantity: freezed == readyQuantity
          ? _value.readyQuantity
          : readyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionStatus?,
      filledPercentage: freezed == filledPercentage
          ? _value.filledPercentage
          : filledPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value.materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<MaterialModel>?,
      productionTask: freezed == productionTask
          ? _value.productionTask
          : productionTask // ignore: cast_nullable_to_non_nullable
              as ProductionTaskModel?,
    ) as $Val);
  }

  /// Create a copy of ProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get product {
    if (_value.product == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.product!, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }

  /// Create a copy of ProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductionTaskModelCopyWith<$Res>? get productionTask {
    if (_value.productionTask == null) {
      return null;
    }

    return $ProductionTaskModelCopyWith<$Res>(_value.productionTask!, (value) {
      return _then(_value.copyWith(productionTask: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductionModelImplCopyWith<$Res>
    implements $ProductionModelCopyWith<$Res> {
  factory _$$ProductionModelImplCopyWith(_$ProductionModelImpl value,
          $Res Function(_$ProductionModelImpl) then) =
      __$$ProductionModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      ProductModel? product,
      double? requiredQuantity,
      double? readyQuantity,
      ProductionStatus? status,
      double? filledPercentage,
      List<MaterialModel>? materials,
      ProductionTaskModel? productionTask});

  @override
  $ProductModelCopyWith<$Res>? get product;
  @override
  $ProductionTaskModelCopyWith<$Res>? get productionTask;
}

/// @nodoc
class __$$ProductionModelImplCopyWithImpl<$Res>
    extends _$ProductionModelCopyWithImpl<$Res, _$ProductionModelImpl>
    implements _$$ProductionModelImplCopyWith<$Res> {
  __$$ProductionModelImplCopyWithImpl(
      _$ProductionModelImpl _value, $Res Function(_$ProductionModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? product = freezed,
    Object? requiredQuantity = freezed,
    Object? readyQuantity = freezed,
    Object? status = freezed,
    Object? filledPercentage = freezed,
    Object? materials = freezed,
    Object? productionTask = freezed,
  }) {
    return _then(_$ProductionModelImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      readyQuantity: freezed == readyQuantity
          ? _value.readyQuantity
          : readyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionStatus?,
      filledPercentage: freezed == filledPercentage
          ? _value.filledPercentage
          : filledPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value._materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<MaterialModel>?,
      productionTask: freezed == productionTask
          ? _value.productionTask
          : productionTask // ignore: cast_nullable_to_non_nullable
              as ProductionTaskModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionModelImpl implements _ProductionModel {
  const _$ProductionModelImpl(
      {this.productId,
      this.product,
      this.requiredQuantity,
      this.readyQuantity,
      this.status,
      this.filledPercentage,
      final List<MaterialModel>? materials,
      this.productionTask})
      : _materials = materials;

  factory _$ProductionModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionModelImplFromJson(json);

  @override
  final String? productId;
  @override
  final ProductModel? product;
  @override
  final double? requiredQuantity;
  @override
  final double? readyQuantity;
  @override
  final ProductionStatus? status;
  @override
  final double? filledPercentage;
  final List<MaterialModel>? _materials;
  @override
  List<MaterialModel>? get materials {
    final value = _materials;
    if (value == null) return null;
    if (_materials is EqualUnmodifiableListView) return _materials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ProductionTaskModel? productionTask;

  @override
  String toString() {
    return 'ProductionModel(productId: $productId, product: $product, requiredQuantity: $requiredQuantity, readyQuantity: $readyQuantity, status: $status, filledPercentage: $filledPercentage, materials: $materials, productionTask: $productionTask)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.requiredQuantity, requiredQuantity) ||
                other.requiredQuantity == requiredQuantity) &&
            (identical(other.readyQuantity, readyQuantity) ||
                other.readyQuantity == readyQuantity) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.filledPercentage, filledPercentage) ||
                other.filledPercentage == filledPercentage) &&
            const DeepCollectionEquality()
                .equals(other._materials, _materials) &&
            (identical(other.productionTask, productionTask) ||
                other.productionTask == productionTask));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      productId,
      product,
      requiredQuantity,
      readyQuantity,
      status,
      filledPercentage,
      const DeepCollectionEquality().hash(_materials),
      productionTask);

  /// Create a copy of ProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionModelImplCopyWith<_$ProductionModelImpl> get copyWith =>
      __$$ProductionModelImplCopyWithImpl<_$ProductionModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionModel implements ProductionModel {
  const factory _ProductionModel(
      {final String? productId,
      final ProductModel? product,
      final double? requiredQuantity,
      final double? readyQuantity,
      final ProductionStatus? status,
      final double? filledPercentage,
      final List<MaterialModel>? materials,
      final ProductionTaskModel? productionTask}) = _$ProductionModelImpl;

  factory _ProductionModel.fromJson(Map<String, dynamic> json) =
      _$ProductionModelImpl.fromJson;

  @override
  String? get productId;
  @override
  ProductModel? get product;
  @override
  double? get requiredQuantity;
  @override
  double? get readyQuantity;
  @override
  ProductionStatus? get status;
  @override
  double? get filledPercentage;
  @override
  List<MaterialModel>? get materials;
  @override
  ProductionTaskModel? get productionTask;

  /// Create a copy of ProductionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionModelImplCopyWith<_$ProductionModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductionTaskModel _$ProductionTaskModelFromJson(Map<String, dynamic> json) {
  return _ProductionTaskModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get projcectId => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  ProductionTaskStatus? get status => throw _privateConstructorUsedError;
  double? get quantityRequired => throw _privateConstructorUsedError;
  List<ProductionMaterialModel>? get materials =>
      throw _privateConstructorUsedError;
  List<ProductionStatusHistoryModel>? get statusHistory =>
      throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskModelCopyWith<ProductionTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskModelCopyWith<$Res> {
  factory $ProductionTaskModelCopyWith(
          ProductionTaskModel value, $Res Function(ProductionTaskModel) then) =
      _$ProductionTaskModelCopyWithImpl<$Res, ProductionTaskModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projcectId,
      String? productId,
      ProductionTaskStatus? status,
      double? quantityRequired,
      List<ProductionMaterialModel>? materials,
      List<ProductionStatusHistoryModel>? statusHistory,
      DateTime? updatedAt,
      DateTime? createdAt});
}

/// @nodoc
class _$ProductionTaskModelCopyWithImpl<$Res, $Val extends ProductionTaskModel>
    implements $ProductionTaskModelCopyWith<$Res> {
  _$ProductionTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projcectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? quantityRequired = freezed,
    Object? materials = freezed,
    Object? statusHistory = freezed,
    Object? updatedAt = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projcectId: freezed == projcectId
          ? _value.projcectId
          : projcectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      quantityRequired: freezed == quantityRequired
          ? _value.quantityRequired
          : quantityRequired // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value.materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<ProductionMaterialModel>?,
      statusHistory: freezed == statusHistory
          ? _value.statusHistory
          : statusHistory // ignore: cast_nullable_to_non_nullable
              as List<ProductionStatusHistoryModel>?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskModelImplCopyWith<$Res>
    implements $ProductionTaskModelCopyWith<$Res> {
  factory _$$ProductionTaskModelImplCopyWith(_$ProductionTaskModelImpl value,
          $Res Function(_$ProductionTaskModelImpl) then) =
      __$$ProductionTaskModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projcectId,
      String? productId,
      ProductionTaskStatus? status,
      double? quantityRequired,
      List<ProductionMaterialModel>? materials,
      List<ProductionStatusHistoryModel>? statusHistory,
      DateTime? updatedAt,
      DateTime? createdAt});
}

/// @nodoc
class __$$ProductionTaskModelImplCopyWithImpl<$Res>
    extends _$ProductionTaskModelCopyWithImpl<$Res, _$ProductionTaskModelImpl>
    implements _$$ProductionTaskModelImplCopyWith<$Res> {
  __$$ProductionTaskModelImplCopyWithImpl(_$ProductionTaskModelImpl _value,
      $Res Function(_$ProductionTaskModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projcectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? quantityRequired = freezed,
    Object? materials = freezed,
    Object? statusHistory = freezed,
    Object? updatedAt = freezed,
    Object? createdAt = freezed,
  }) {
    return _then(_$ProductionTaskModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projcectId: freezed == projcectId
          ? _value.projcectId
          : projcectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      quantityRequired: freezed == quantityRequired
          ? _value.quantityRequired
          : quantityRequired // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value._materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<ProductionMaterialModel>?,
      statusHistory: freezed == statusHistory
          ? _value._statusHistory
          : statusHistory // ignore: cast_nullable_to_non_nullable
              as List<ProductionStatusHistoryModel>?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskModelImpl implements _ProductionTaskModel {
  const _$ProductionTaskModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.projcectId,
      this.productId,
      this.status,
      this.quantityRequired,
      final List<ProductionMaterialModel>? materials,
      final List<ProductionStatusHistoryModel>? statusHistory,
      this.updatedAt,
      this.createdAt})
      : _materials = materials,
        _statusHistory = statusHistory;

  factory _$ProductionTaskModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionTaskModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? projcectId;
  @override
  final String? productId;
  @override
  final ProductionTaskStatus? status;
  @override
  final double? quantityRequired;
  final List<ProductionMaterialModel>? _materials;
  @override
  List<ProductionMaterialModel>? get materials {
    final value = _materials;
    if (value == null) return null;
    if (_materials is EqualUnmodifiableListView) return _materials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductionStatusHistoryModel>? _statusHistory;
  @override
  List<ProductionStatusHistoryModel>? get statusHistory {
    final value = _statusHistory;
    if (value == null) return null;
    if (_statusHistory is EqualUnmodifiableListView) return _statusHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? updatedAt;
  @override
  final DateTime? createdAt;

  @override
  String toString() {
    return 'ProductionTaskModel(id: $id, projcectId: $projcectId, productId: $productId, status: $status, quantityRequired: $quantityRequired, materials: $materials, statusHistory: $statusHistory, updatedAt: $updatedAt, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.projcectId, projcectId) ||
                other.projcectId == projcectId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.quantityRequired, quantityRequired) ||
                other.quantityRequired == quantityRequired) &&
            const DeepCollectionEquality()
                .equals(other._materials, _materials) &&
            const DeepCollectionEquality()
                .equals(other._statusHistory, _statusHistory) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      projcectId,
      productId,
      status,
      quantityRequired,
      const DeepCollectionEquality().hash(_materials),
      const DeepCollectionEquality().hash(_statusHistory),
      updatedAt,
      createdAt);

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskModelImplCopyWith<_$ProductionTaskModelImpl> get copyWith =>
      __$$ProductionTaskModelImplCopyWithImpl<_$ProductionTaskModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskModel implements ProductionTaskModel {
  const factory _ProductionTaskModel(
      {@JsonKey(name: '_id') final String? id,
      final String? projcectId,
      final String? productId,
      final ProductionTaskStatus? status,
      final double? quantityRequired,
      final List<ProductionMaterialModel>? materials,
      final List<ProductionStatusHistoryModel>? statusHistory,
      final DateTime? updatedAt,
      final DateTime? createdAt}) = _$ProductionTaskModelImpl;

  factory _ProductionTaskModel.fromJson(Map<String, dynamic> json) =
      _$ProductionTaskModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get projcectId;
  @override
  String? get productId;
  @override
  ProductionTaskStatus? get status;
  @override
  double? get quantityRequired;
  @override
  List<ProductionMaterialModel>? get materials;
  @override
  List<ProductionStatusHistoryModel>? get statusHistory;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get createdAt;

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskModelImplCopyWith<_$ProductionTaskModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductionStatusHistoryModel _$ProductionStatusHistoryModelFromJson(
    Map<String, dynamic> json) {
  return _ProductionStatusHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionStatusHistoryModel {
  ProductionTaskStatus? get status => throw _privateConstructorUsedError;
  DateTime? get date => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;
  UserModel? get user => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this ProductionStatusHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionStatusHistoryModelCopyWith<ProductionStatusHistoryModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionStatusHistoryModelCopyWith<$Res> {
  factory $ProductionStatusHistoryModelCopyWith(
          ProductionStatusHistoryModel value,
          $Res Function(ProductionStatusHistoryModel) then) =
      _$ProductionStatusHistoryModelCopyWithImpl<$Res,
          ProductionStatusHistoryModel>;
  @useResult
  $Res call(
      {ProductionTaskStatus? status,
      DateTime? date,
      String? userId,
      UserModel? user,
      String? comment});
}

/// @nodoc
class _$ProductionStatusHistoryModelCopyWithImpl<$Res,
        $Val extends ProductionStatusHistoryModel>
    implements $ProductionStatusHistoryModelCopyWith<$Res> {
  _$ProductionStatusHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? date = freezed,
    Object? userId = freezed,
    Object? user = freezed,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionStatusHistoryModelImplCopyWith<$Res>
    implements $ProductionStatusHistoryModelCopyWith<$Res> {
  factory _$$ProductionStatusHistoryModelImplCopyWith(
          _$ProductionStatusHistoryModelImpl value,
          $Res Function(_$ProductionStatusHistoryModelImpl) then) =
      __$$ProductionStatusHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ProductionTaskStatus? status,
      DateTime? date,
      String? userId,
      UserModel? user,
      String? comment});
}

/// @nodoc
class __$$ProductionStatusHistoryModelImplCopyWithImpl<$Res>
    extends _$ProductionStatusHistoryModelCopyWithImpl<$Res,
        _$ProductionStatusHistoryModelImpl>
    implements _$$ProductionStatusHistoryModelImplCopyWith<$Res> {
  __$$ProductionStatusHistoryModelImplCopyWithImpl(
      _$ProductionStatusHistoryModelImpl _value,
      $Res Function(_$ProductionStatusHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? date = freezed,
    Object? userId = freezed,
    Object? user = freezed,
    Object? comment = freezed,
  }) {
    return _then(_$ProductionStatusHistoryModelImpl(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionStatusHistoryModelImpl
    implements _ProductionStatusHistoryModel {
  const _$ProductionStatusHistoryModelImpl(
      {this.status, this.date, this.userId, this.user, this.comment});

  factory _$ProductionStatusHistoryModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionStatusHistoryModelImplFromJson(json);

  @override
  final ProductionTaskStatus? status;
  @override
  final DateTime? date;
  @override
  final String? userId;
  @override
  final UserModel? user;
  @override
  final String? comment;

  @override
  String toString() {
    return 'ProductionStatusHistoryModel(status: $status, date: $date, userId: $userId, user: $user, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionStatusHistoryModelImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, status, date, userId, user, comment);

  /// Create a copy of ProductionStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionStatusHistoryModelImplCopyWith<
          _$ProductionStatusHistoryModelImpl>
      get copyWith => __$$ProductionStatusHistoryModelImplCopyWithImpl<
          _$ProductionStatusHistoryModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionStatusHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionStatusHistoryModel
    implements ProductionStatusHistoryModel {
  const factory _ProductionStatusHistoryModel(
      {final ProductionTaskStatus? status,
      final DateTime? date,
      final String? userId,
      final UserModel? user,
      final String? comment}) = _$ProductionStatusHistoryModelImpl;

  factory _ProductionStatusHistoryModel.fromJson(Map<String, dynamic> json) =
      _$ProductionStatusHistoryModelImpl.fromJson;

  @override
  ProductionTaskStatus? get status;
  @override
  DateTime? get date;
  @override
  String? get userId;
  @override
  UserModel? get user;
  @override
  String? get comment;

  /// Create a copy of ProductionStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionStatusHistoryModelImplCopyWith<
          _$ProductionStatusHistoryModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionMaterialModel _$ProductionMaterialModelFromJson(
    Map<String, dynamic> json) {
  return _ProductionMaterialModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionMaterialModel {
  String? get storageItemId => throw _privateConstructorUsedError;
  double? get requiredQuantity => throw _privateConstructorUsedError;
  double? get issuedQuantity => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;
  UnitType? get materialUnit => throw _privateConstructorUsedError;

  /// Serializes this ProductionMaterialModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionMaterialModelCopyWith<ProductionMaterialModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionMaterialModelCopyWith<$Res> {
  factory $ProductionMaterialModelCopyWith(ProductionMaterialModel value,
          $Res Function(ProductionMaterialModel) then) =
      _$ProductionMaterialModelCopyWithImpl<$Res, ProductionMaterialModel>;
  @useResult
  $Res call(
      {String? storageItemId,
      double? requiredQuantity,
      double? issuedQuantity,
      String? materialName,
      UnitType? materialUnit});
}

/// @nodoc
class _$ProductionMaterialModelCopyWithImpl<$Res,
        $Val extends ProductionMaterialModel>
    implements $ProductionMaterialModelCopyWith<$Res> {
  _$ProductionMaterialModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storageItemId = freezed,
    Object? requiredQuantity = freezed,
    Object? issuedQuantity = freezed,
    Object? materialName = freezed,
    Object? materialUnit = freezed,
  }) {
    return _then(_value.copyWith(
      storageItemId: freezed == storageItemId
          ? _value.storageItemId
          : storageItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      issuedQuantity: freezed == issuedQuantity
          ? _value.issuedQuantity
          : issuedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialUnit: freezed == materialUnit
          ? _value.materialUnit
          : materialUnit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionMaterialModelImplCopyWith<$Res>
    implements $ProductionMaterialModelCopyWith<$Res> {
  factory _$$ProductionMaterialModelImplCopyWith(
          _$ProductionMaterialModelImpl value,
          $Res Function(_$ProductionMaterialModelImpl) then) =
      __$$ProductionMaterialModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? storageItemId,
      double? requiredQuantity,
      double? issuedQuantity,
      String? materialName,
      UnitType? materialUnit});
}

/// @nodoc
class __$$ProductionMaterialModelImplCopyWithImpl<$Res>
    extends _$ProductionMaterialModelCopyWithImpl<$Res,
        _$ProductionMaterialModelImpl>
    implements _$$ProductionMaterialModelImplCopyWith<$Res> {
  __$$ProductionMaterialModelImplCopyWithImpl(
      _$ProductionMaterialModelImpl _value,
      $Res Function(_$ProductionMaterialModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storageItemId = freezed,
    Object? requiredQuantity = freezed,
    Object? issuedQuantity = freezed,
    Object? materialName = freezed,
    Object? materialUnit = freezed,
  }) {
    return _then(_$ProductionMaterialModelImpl(
      storageItemId: freezed == storageItemId
          ? _value.storageItemId
          : storageItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      issuedQuantity: freezed == issuedQuantity
          ? _value.issuedQuantity
          : issuedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialUnit: freezed == materialUnit
          ? _value.materialUnit
          : materialUnit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionMaterialModelImpl implements _ProductionMaterialModel {
  const _$ProductionMaterialModelImpl(
      {this.storageItemId,
      this.requiredQuantity,
      this.issuedQuantity,
      this.materialName,
      this.materialUnit});

  factory _$ProductionMaterialModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionMaterialModelImplFromJson(json);

  @override
  final String? storageItemId;
  @override
  final double? requiredQuantity;
  @override
  final double? issuedQuantity;
  @override
  final String? materialName;
  @override
  final UnitType? materialUnit;

  @override
  String toString() {
    return 'ProductionMaterialModel(storageItemId: $storageItemId, requiredQuantity: $requiredQuantity, issuedQuantity: $issuedQuantity, materialName: $materialName, materialUnit: $materialUnit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionMaterialModelImpl &&
            (identical(other.storageItemId, storageItemId) ||
                other.storageItemId == storageItemId) &&
            (identical(other.requiredQuantity, requiredQuantity) ||
                other.requiredQuantity == requiredQuantity) &&
            (identical(other.issuedQuantity, issuedQuantity) ||
                other.issuedQuantity == issuedQuantity) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.materialUnit, materialUnit) ||
                other.materialUnit == materialUnit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, storageItemId, requiredQuantity,
      issuedQuantity, materialName, materialUnit);

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionMaterialModelImplCopyWith<_$ProductionMaterialModelImpl>
      get copyWith => __$$ProductionMaterialModelImplCopyWithImpl<
          _$ProductionMaterialModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionMaterialModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionMaterialModel implements ProductionMaterialModel {
  const factory _ProductionMaterialModel(
      {final String? storageItemId,
      final double? requiredQuantity,
      final double? issuedQuantity,
      final String? materialName,
      final UnitType? materialUnit}) = _$ProductionMaterialModelImpl;

  factory _ProductionMaterialModel.fromJson(Map<String, dynamic> json) =
      _$ProductionMaterialModelImpl.fromJson;

  @override
  String? get storageItemId;
  @override
  double? get requiredQuantity;
  @override
  double? get issuedQuantity;
  @override
  String? get materialName;
  @override
  UnitType? get materialUnit;

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionMaterialModelImplCopyWith<_$ProductionMaterialModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
