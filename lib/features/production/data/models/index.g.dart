// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GetProductionItemsModelImpl _$$GetProductionItemsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$GetProductionItemsModelImpl(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ProductionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GetProductionItemsModelImplToJson(
        _$GetProductionItemsModelImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_$ChangeStatusProductionModelImpl _$$ChangeStatusProductionModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ChangeStatusProductionModelImpl(
      taskId: json['taskId'] as String?,
      status:
          $enumDecodeNullable(_$ProductionTaskStatusEnumMap, json['status']),
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$$ChangeStatusProductionModelImplToJson(
        _$ChangeStatusProductionModelImpl instance) =>
    <String, dynamic>{
      if (instance.taskId case final value?) 'taskId': value,
      if (_$ProductionTaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.comment case final value?) 'comment': value,
    };

const _$ProductionTaskStatusEnumMap = {
  ProductionTaskStatus.materialsRequested: 'materials_requested',
  ProductionTaskStatus.materialsIssued: 'materials_issued',
  ProductionTaskStatus.assemblyInProgress: 'assembly_in_progress',
  ProductionTaskStatus.readyForInspection: 'ready_for_inspection',
  ProductionTaskStatus.qcInspection: 'qc_inspection',
  ProductionTaskStatus.qcApproved: 'qc_approved',
};
