import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/production/data/models/production.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
class GetProductionItemsModel with _$GetProductionItemsModel {
  @JsonSerializable(includeIfNull: false)
  const factory GetProductionItemsModel({
    List<ProductionModel>? items,
    int? totalItems,
  }) = _GetProductionItemsModel;

  factory GetProductionItemsModel.fromJson(Map<String, dynamic> json) =>
      _$GetProductionItemsModelFromJson(json);
}

@freezed
class ChangeStatusProductionModel with _$ChangeStatusProductionModel {
  @JsonSerializable(includeIfNull: false)
  const factory ChangeStatusProductionModel({
    String? taskId,
    ProductionTaskStatus? status,
    String? comment,
  }) = _ChangeStatusProductionModel;

  factory ChangeStatusProductionModel.fromJson(Map<String, dynamic> json) =>
      _$ChangeStatusProductionModelFromJson(json);
}
