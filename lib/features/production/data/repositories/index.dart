import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sphere/features/production/data/models/index.dart';
import 'package:sphere/shared/data/datasources/api.dart';

class ProductionRepository {
  static Future<Response<GetProductionItemsModel>?> search(
    String projectId,
  ) async {
    final body = jsonEncode({
      'projectId': projectId,
    });

    final request = await API.request<GetProductionItemsModel>(
      url: '/production/items',
      body: body,
      method: 'POST',
      fromJson: GetProductionItemsModel.fromJson,
    );

    return request;
  }

  static Future<Response<dynamic>?> taskCreate({
    required String projectId,
    required String productId,
    required double quantityRequired,
  }) async {
    final body = jsonEncode({
      'projectId': projectId,
      'productId': productId,
      'quantityRequired': quantityRequired,
    });

    final request = await API.request<dynamic>(
      url: '/production/task/create',
      body: body,
      method: 'POST',
      // fromJson: GetProductionItemsModel.fromJson,
    );

    return request;
  }

  // TODO: update status for production task1
  static Future<Response<dynamic>?> taskUpdateStatus(
    ChangeStatusProductionModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<dynamic>(
      url: '/production/task/update-status',
      body: body,
      method: 'POST',
      // fromJson: GetProductionItemsModel.fromJson,
    );

    return request;
  }
}
