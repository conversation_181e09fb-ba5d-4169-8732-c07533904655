// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
class CreateTaskModel with _$CreateTaskModel {
  @JsonSerializable(includeIfNull: false)
  const factory CreateTaskModel({
    Department? department,
    DateTime? deadlineDate,
    String? projectId,
    List<String>? workerIds,
    @JsonKey(name: 'targetIds') List<String>? productIds,
    bool? includeChildProducts,
    @Json<PERSON>ey(name: 'provisionType') ProvisionsFilter? filterType,
    String? comment,
  }) = _CreateTaskModel;

  factory CreateTaskModel.fromJson(Map<String, dynamic> json) =>
      _$CreateTaskModelFromJson(json);
}

@freezed
class EditTaskModel with _$EditTaskModel {
  @JsonSerializable(includeIfNull: false)
  const factory EditTaskModel({
    List<String>? workerIds,
    DateTime? deadlineDate,
  }) = _EditTaskModel;

  factory EditTaskModel.fromJson(Map<String, dynamic> json) =>
      _$EditTaskModelFromJson(json);
}

@freezed
class SearchTasksOutputModel with _$SearchTasksOutputModel {
  @JsonSerializable(includeIfNull: false)
  const factory SearchTasksOutputModel({
    List<TaskModel>? items,
    int? totalItems,
    String? message,
  }) = _SearchTasksOutputModel;

  factory SearchTasksOutputModel.fromJson(Map<String, dynamic> json) =>
      _$SearchTasksOutputModelFromJson(json);
}

@freezed
class UpdateTaskProgressStatusModel with _$UpdateTaskProgressStatusModel {
  @JsonSerializable(includeIfNull: false)
  const factory UpdateTaskProgressStatusModel({
    @JsonKey(name: 'taskProgressIds') List<String>? taskProgressIds,
    TaskStatus? status,
    String? comment,
    bool? includeChildren,
  }) = _UpdateTaskProgressStatusModel;

  factory UpdateTaskProgressStatusModel.fromJson(Map<String, dynamic> json) =>
      _$UpdateTaskProgressStatusModelFromJson(json);
}
