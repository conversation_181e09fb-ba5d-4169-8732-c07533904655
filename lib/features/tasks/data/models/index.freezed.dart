// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CreateTaskModel _$CreateTaskModelFromJson(Map<String, dynamic> json) {
  return _CreateTaskModel.fromJson(json);
}

/// @nodoc
mixin _$CreateTaskModel {
  Department? get department => throw _privateConstructorUsedError;
  DateTime? get deadlineDate => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  List<String>? get workerIds => throw _privateConstructorUsedError;
  @JsonKey(name: 'targetIds')
  List<String>? get productIds => throw _privateConstructorUsedError;
  bool? get includeChildProducts => throw _privateConstructorUsedError;
  @JsonKey(name: 'provisionType')
  ProvisionsFilter? get filterType => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this CreateTaskModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateTaskModelCopyWith<CreateTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateTaskModelCopyWith<$Res> {
  factory $CreateTaskModelCopyWith(
          CreateTaskModel value, $Res Function(CreateTaskModel) then) =
      _$CreateTaskModelCopyWithImpl<$Res, CreateTaskModel>;
  @useResult
  $Res call(
      {Department? department,
      DateTime? deadlineDate,
      String? projectId,
      List<String>? workerIds,
      @JsonKey(name: 'targetIds') List<String>? productIds,
      bool? includeChildProducts,
      @JsonKey(name: 'provisionType') ProvisionsFilter? filterType,
      String? comment});
}

/// @nodoc
class _$CreateTaskModelCopyWithImpl<$Res, $Val extends CreateTaskModel>
    implements $CreateTaskModelCopyWith<$Res> {
  _$CreateTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? department = freezed,
    Object? deadlineDate = freezed,
    Object? projectId = freezed,
    Object? workerIds = freezed,
    Object? productIds = freezed,
    Object? includeChildProducts = freezed,
    Object? filterType = freezed,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      workerIds: freezed == workerIds
          ? _value.workerIds
          : workerIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      productIds: freezed == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      includeChildProducts: freezed == includeChildProducts
          ? _value.includeChildProducts
          : includeChildProducts // ignore: cast_nullable_to_non_nullable
              as bool?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateTaskModelImplCopyWith<$Res>
    implements $CreateTaskModelCopyWith<$Res> {
  factory _$$CreateTaskModelImplCopyWith(_$CreateTaskModelImpl value,
          $Res Function(_$CreateTaskModelImpl) then) =
      __$$CreateTaskModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Department? department,
      DateTime? deadlineDate,
      String? projectId,
      List<String>? workerIds,
      @JsonKey(name: 'targetIds') List<String>? productIds,
      bool? includeChildProducts,
      @JsonKey(name: 'provisionType') ProvisionsFilter? filterType,
      String? comment});
}

/// @nodoc
class __$$CreateTaskModelImplCopyWithImpl<$Res>
    extends _$CreateTaskModelCopyWithImpl<$Res, _$CreateTaskModelImpl>
    implements _$$CreateTaskModelImplCopyWith<$Res> {
  __$$CreateTaskModelImplCopyWithImpl(
      _$CreateTaskModelImpl _value, $Res Function(_$CreateTaskModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? department = freezed,
    Object? deadlineDate = freezed,
    Object? projectId = freezed,
    Object? workerIds = freezed,
    Object? productIds = freezed,
    Object? includeChildProducts = freezed,
    Object? filterType = freezed,
    Object? comment = freezed,
  }) {
    return _then(_$CreateTaskModelImpl(
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      workerIds: freezed == workerIds
          ? _value._workerIds
          : workerIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      productIds: freezed == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      includeChildProducts: freezed == includeChildProducts
          ? _value.includeChildProducts
          : includeChildProducts // ignore: cast_nullable_to_non_nullable
              as bool?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$CreateTaskModelImpl implements _CreateTaskModel {
  const _$CreateTaskModelImpl(
      {this.department,
      this.deadlineDate,
      this.projectId,
      final List<String>? workerIds,
      @JsonKey(name: 'targetIds') final List<String>? productIds,
      this.includeChildProducts,
      @JsonKey(name: 'provisionType') this.filterType,
      this.comment})
      : _workerIds = workerIds,
        _productIds = productIds;

  factory _$CreateTaskModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateTaskModelImplFromJson(json);

  @override
  final Department? department;
  @override
  final DateTime? deadlineDate;
  @override
  final String? projectId;
  final List<String>? _workerIds;
  @override
  List<String>? get workerIds {
    final value = _workerIds;
    if (value == null) return null;
    if (_workerIds is EqualUnmodifiableListView) return _workerIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _productIds;
  @override
  @JsonKey(name: 'targetIds')
  List<String>? get productIds {
    final value = _productIds;
    if (value == null) return null;
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? includeChildProducts;
  @override
  @JsonKey(name: 'provisionType')
  final ProvisionsFilter? filterType;
  @override
  final String? comment;

  @override
  String toString() {
    return 'CreateTaskModel(department: $department, deadlineDate: $deadlineDate, projectId: $projectId, workerIds: $workerIds, productIds: $productIds, includeChildProducts: $includeChildProducts, filterType: $filterType, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateTaskModelImpl &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.deadlineDate, deadlineDate) ||
                other.deadlineDate == deadlineDate) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            const DeepCollectionEquality()
                .equals(other._workerIds, _workerIds) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            (identical(other.includeChildProducts, includeChildProducts) ||
                other.includeChildProducts == includeChildProducts) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      department,
      deadlineDate,
      projectId,
      const DeepCollectionEquality().hash(_workerIds),
      const DeepCollectionEquality().hash(_productIds),
      includeChildProducts,
      filterType,
      comment);

  /// Create a copy of CreateTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateTaskModelImplCopyWith<_$CreateTaskModelImpl> get copyWith =>
      __$$CreateTaskModelImplCopyWithImpl<_$CreateTaskModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateTaskModelImplToJson(
      this,
    );
  }
}

abstract class _CreateTaskModel implements CreateTaskModel {
  const factory _CreateTaskModel(
      {final Department? department,
      final DateTime? deadlineDate,
      final String? projectId,
      final List<String>? workerIds,
      @JsonKey(name: 'targetIds') final List<String>? productIds,
      final bool? includeChildProducts,
      @JsonKey(name: 'provisionType') final ProvisionsFilter? filterType,
      final String? comment}) = _$CreateTaskModelImpl;

  factory _CreateTaskModel.fromJson(Map<String, dynamic> json) =
      _$CreateTaskModelImpl.fromJson;

  @override
  Department? get department;
  @override
  DateTime? get deadlineDate;
  @override
  String? get projectId;
  @override
  List<String>? get workerIds;
  @override
  @JsonKey(name: 'targetIds')
  List<String>? get productIds;
  @override
  bool? get includeChildProducts;
  @override
  @JsonKey(name: 'provisionType')
  ProvisionsFilter? get filterType;
  @override
  String? get comment;

  /// Create a copy of CreateTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateTaskModelImplCopyWith<_$CreateTaskModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EditTaskModel _$EditTaskModelFromJson(Map<String, dynamic> json) {
  return _EditTaskModel.fromJson(json);
}

/// @nodoc
mixin _$EditTaskModel {
  List<String>? get workerIds => throw _privateConstructorUsedError;
  DateTime? get deadlineDate => throw _privateConstructorUsedError;

  /// Serializes this EditTaskModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EditTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditTaskModelCopyWith<EditTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditTaskModelCopyWith<$Res> {
  factory $EditTaskModelCopyWith(
          EditTaskModel value, $Res Function(EditTaskModel) then) =
      _$EditTaskModelCopyWithImpl<$Res, EditTaskModel>;
  @useResult
  $Res call({List<String>? workerIds, DateTime? deadlineDate});
}

/// @nodoc
class _$EditTaskModelCopyWithImpl<$Res, $Val extends EditTaskModel>
    implements $EditTaskModelCopyWith<$Res> {
  _$EditTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workerIds = freezed,
    Object? deadlineDate = freezed,
  }) {
    return _then(_value.copyWith(
      workerIds: freezed == workerIds
          ? _value.workerIds
          : workerIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditTaskModelImplCopyWith<$Res>
    implements $EditTaskModelCopyWith<$Res> {
  factory _$$EditTaskModelImplCopyWith(
          _$EditTaskModelImpl value, $Res Function(_$EditTaskModelImpl) then) =
      __$$EditTaskModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<String>? workerIds, DateTime? deadlineDate});
}

/// @nodoc
class __$$EditTaskModelImplCopyWithImpl<$Res>
    extends _$EditTaskModelCopyWithImpl<$Res, _$EditTaskModelImpl>
    implements _$$EditTaskModelImplCopyWith<$Res> {
  __$$EditTaskModelImplCopyWithImpl(
      _$EditTaskModelImpl _value, $Res Function(_$EditTaskModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? workerIds = freezed,
    Object? deadlineDate = freezed,
  }) {
    return _then(_$EditTaskModelImpl(
      workerIds: freezed == workerIds
          ? _value._workerIds
          : workerIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$EditTaskModelImpl implements _EditTaskModel {
  const _$EditTaskModelImpl({final List<String>? workerIds, this.deadlineDate})
      : _workerIds = workerIds;

  factory _$EditTaskModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$EditTaskModelImplFromJson(json);

  final List<String>? _workerIds;
  @override
  List<String>? get workerIds {
    final value = _workerIds;
    if (value == null) return null;
    if (_workerIds is EqualUnmodifiableListView) return _workerIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? deadlineDate;

  @override
  String toString() {
    return 'EditTaskModel(workerIds: $workerIds, deadlineDate: $deadlineDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditTaskModelImpl &&
            const DeepCollectionEquality()
                .equals(other._workerIds, _workerIds) &&
            (identical(other.deadlineDate, deadlineDate) ||
                other.deadlineDate == deadlineDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_workerIds), deadlineDate);

  /// Create a copy of EditTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditTaskModelImplCopyWith<_$EditTaskModelImpl> get copyWith =>
      __$$EditTaskModelImplCopyWithImpl<_$EditTaskModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EditTaskModelImplToJson(
      this,
    );
  }
}

abstract class _EditTaskModel implements EditTaskModel {
  const factory _EditTaskModel(
      {final List<String>? workerIds,
      final DateTime? deadlineDate}) = _$EditTaskModelImpl;

  factory _EditTaskModel.fromJson(Map<String, dynamic> json) =
      _$EditTaskModelImpl.fromJson;

  @override
  List<String>? get workerIds;
  @override
  DateTime? get deadlineDate;

  /// Create a copy of EditTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditTaskModelImplCopyWith<_$EditTaskModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SearchTasksOutputModel _$SearchTasksOutputModelFromJson(
    Map<String, dynamic> json) {
  return _SearchTasksOutputModel.fromJson(json);
}

/// @nodoc
mixin _$SearchTasksOutputModel {
  List<TaskModel>? get items => throw _privateConstructorUsedError;
  int? get totalItems => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Serializes this SearchTasksOutputModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SearchTasksOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SearchTasksOutputModelCopyWith<SearchTasksOutputModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchTasksOutputModelCopyWith<$Res> {
  factory $SearchTasksOutputModelCopyWith(SearchTasksOutputModel value,
          $Res Function(SearchTasksOutputModel) then) =
      _$SearchTasksOutputModelCopyWithImpl<$Res, SearchTasksOutputModel>;
  @useResult
  $Res call({List<TaskModel>? items, int? totalItems, String? message});
}

/// @nodoc
class _$SearchTasksOutputModelCopyWithImpl<$Res,
        $Val extends SearchTasksOutputModel>
    implements $SearchTasksOutputModelCopyWith<$Res> {
  _$SearchTasksOutputModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchTasksOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<TaskModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchTasksOutputModelImplCopyWith<$Res>
    implements $SearchTasksOutputModelCopyWith<$Res> {
  factory _$$SearchTasksOutputModelImplCopyWith(
          _$SearchTasksOutputModelImpl value,
          $Res Function(_$SearchTasksOutputModelImpl) then) =
      __$$SearchTasksOutputModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<TaskModel>? items, int? totalItems, String? message});
}

/// @nodoc
class __$$SearchTasksOutputModelImplCopyWithImpl<$Res>
    extends _$SearchTasksOutputModelCopyWithImpl<$Res,
        _$SearchTasksOutputModelImpl>
    implements _$$SearchTasksOutputModelImplCopyWith<$Res> {
  __$$SearchTasksOutputModelImplCopyWithImpl(
      _$SearchTasksOutputModelImpl _value,
      $Res Function(_$SearchTasksOutputModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchTasksOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
    Object? message = freezed,
  }) {
    return _then(_$SearchTasksOutputModelImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<TaskModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SearchTasksOutputModelImpl implements _SearchTasksOutputModel {
  const _$SearchTasksOutputModelImpl(
      {final List<TaskModel>? items, this.totalItems, this.message})
      : _items = items;

  factory _$SearchTasksOutputModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchTasksOutputModelImplFromJson(json);

  final List<TaskModel>? _items;
  @override
  List<TaskModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalItems;
  @override
  final String? message;

  @override
  String toString() {
    return 'SearchTasksOutputModel(items: $items, totalItems: $totalItems, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchTasksOutputModelImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_items), totalItems, message);

  /// Create a copy of SearchTasksOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchTasksOutputModelImplCopyWith<_$SearchTasksOutputModelImpl>
      get copyWith => __$$SearchTasksOutputModelImplCopyWithImpl<
          _$SearchTasksOutputModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchTasksOutputModelImplToJson(
      this,
    );
  }
}

abstract class _SearchTasksOutputModel implements SearchTasksOutputModel {
  const factory _SearchTasksOutputModel(
      {final List<TaskModel>? items,
      final int? totalItems,
      final String? message}) = _$SearchTasksOutputModelImpl;

  factory _SearchTasksOutputModel.fromJson(Map<String, dynamic> json) =
      _$SearchTasksOutputModelImpl.fromJson;

  @override
  List<TaskModel>? get items;
  @override
  int? get totalItems;
  @override
  String? get message;

  /// Create a copy of SearchTasksOutputModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchTasksOutputModelImplCopyWith<_$SearchTasksOutputModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UpdateTaskProgressStatusModel _$UpdateTaskProgressStatusModelFromJson(
    Map<String, dynamic> json) {
  return _UpdateTaskProgressStatusModel.fromJson(json);
}

/// @nodoc
mixin _$UpdateTaskProgressStatusModel {
  @JsonKey(name: 'taskProgressIds')
  List<String>? get taskProgressIds => throw _privateConstructorUsedError;
  TaskStatus? get status => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;
  bool? get includeChildren => throw _privateConstructorUsedError;

  /// Serializes this UpdateTaskProgressStatusModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateTaskProgressStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateTaskProgressStatusModelCopyWith<UpdateTaskProgressStatusModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateTaskProgressStatusModelCopyWith<$Res> {
  factory $UpdateTaskProgressStatusModelCopyWith(
          UpdateTaskProgressStatusModel value,
          $Res Function(UpdateTaskProgressStatusModel) then) =
      _$UpdateTaskProgressStatusModelCopyWithImpl<$Res,
          UpdateTaskProgressStatusModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'taskProgressIds') List<String>? taskProgressIds,
      TaskStatus? status,
      String? comment,
      bool? includeChildren});
}

/// @nodoc
class _$UpdateTaskProgressStatusModelCopyWithImpl<$Res,
        $Val extends UpdateTaskProgressStatusModel>
    implements $UpdateTaskProgressStatusModelCopyWith<$Res> {
  _$UpdateTaskProgressStatusModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateTaskProgressStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskProgressIds = freezed,
    Object? status = freezed,
    Object? comment = freezed,
    Object? includeChildren = freezed,
  }) {
    return _then(_value.copyWith(
      taskProgressIds: freezed == taskProgressIds
          ? _value.taskProgressIds
          : taskProgressIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      includeChildren: freezed == includeChildren
          ? _value.includeChildren
          : includeChildren // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateTaskProgressStatusModelImplCopyWith<$Res>
    implements $UpdateTaskProgressStatusModelCopyWith<$Res> {
  factory _$$UpdateTaskProgressStatusModelImplCopyWith(
          _$UpdateTaskProgressStatusModelImpl value,
          $Res Function(_$UpdateTaskProgressStatusModelImpl) then) =
      __$$UpdateTaskProgressStatusModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'taskProgressIds') List<String>? taskProgressIds,
      TaskStatus? status,
      String? comment,
      bool? includeChildren});
}

/// @nodoc
class __$$UpdateTaskProgressStatusModelImplCopyWithImpl<$Res>
    extends _$UpdateTaskProgressStatusModelCopyWithImpl<$Res,
        _$UpdateTaskProgressStatusModelImpl>
    implements _$$UpdateTaskProgressStatusModelImplCopyWith<$Res> {
  __$$UpdateTaskProgressStatusModelImplCopyWithImpl(
      _$UpdateTaskProgressStatusModelImpl _value,
      $Res Function(_$UpdateTaskProgressStatusModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateTaskProgressStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskProgressIds = freezed,
    Object? status = freezed,
    Object? comment = freezed,
    Object? includeChildren = freezed,
  }) {
    return _then(_$UpdateTaskProgressStatusModelImpl(
      taskProgressIds: freezed == taskProgressIds
          ? _value._taskProgressIds
          : taskProgressIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      includeChildren: freezed == includeChildren
          ? _value.includeChildren
          : includeChildren // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$UpdateTaskProgressStatusModelImpl
    implements _UpdateTaskProgressStatusModel {
  const _$UpdateTaskProgressStatusModelImpl(
      {@JsonKey(name: 'taskProgressIds') final List<String>? taskProgressIds,
      this.status,
      this.comment,
      this.includeChildren})
      : _taskProgressIds = taskProgressIds;

  factory _$UpdateTaskProgressStatusModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$UpdateTaskProgressStatusModelImplFromJson(json);

  final List<String>? _taskProgressIds;
  @override
  @JsonKey(name: 'taskProgressIds')
  List<String>? get taskProgressIds {
    final value = _taskProgressIds;
    if (value == null) return null;
    if (_taskProgressIds is EqualUnmodifiableListView) return _taskProgressIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final TaskStatus? status;
  @override
  final String? comment;
  @override
  final bool? includeChildren;

  @override
  String toString() {
    return 'UpdateTaskProgressStatusModel(taskProgressIds: $taskProgressIds, status: $status, comment: $comment, includeChildren: $includeChildren)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTaskProgressStatusModelImpl &&
            const DeepCollectionEquality()
                .equals(other._taskProgressIds, _taskProgressIds) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.includeChildren, includeChildren) ||
                other.includeChildren == includeChildren));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_taskProgressIds),
      status,
      comment,
      includeChildren);

  /// Create a copy of UpdateTaskProgressStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTaskProgressStatusModelImplCopyWith<
          _$UpdateTaskProgressStatusModelImpl>
      get copyWith => __$$UpdateTaskProgressStatusModelImplCopyWithImpl<
          _$UpdateTaskProgressStatusModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateTaskProgressStatusModelImplToJson(
      this,
    );
  }
}

abstract class _UpdateTaskProgressStatusModel
    implements UpdateTaskProgressStatusModel {
  const factory _UpdateTaskProgressStatusModel(
      {@JsonKey(name: 'taskProgressIds') final List<String>? taskProgressIds,
      final TaskStatus? status,
      final String? comment,
      final bool? includeChildren}) = _$UpdateTaskProgressStatusModelImpl;

  factory _UpdateTaskProgressStatusModel.fromJson(Map<String, dynamic> json) =
      _$UpdateTaskProgressStatusModelImpl.fromJson;

  @override
  @JsonKey(name: 'taskProgressIds')
  List<String>? get taskProgressIds;
  @override
  TaskStatus? get status;
  @override
  String? get comment;
  @override
  bool? get includeChildren;

  /// Create a copy of UpdateTaskProgressStatusModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateTaskProgressStatusModelImplCopyWith<
          _$UpdateTaskProgressStatusModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
