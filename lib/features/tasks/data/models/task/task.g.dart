// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TaskModelImpl _$$TaskModelImplFromJson(Map<String, dynamic> json) =>
    _$TaskModelImpl(
      id: json['_id'] as String?,
      department: $enumDecodeNullable(_$DepartmentEnumMap, json['department']),
      workerIds: (json['workerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      projectId: json['projectId'] as String?,
      deadlineDate: json['deadlineDate'] == null
          ? null
          : DateTime.parse(json['deadlineDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      status: $enumDecodeNullable(_$TaskStatusEnumMap, json['status']),
      comment: json['comment'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      total: (json['total'] as num?)?.toInt(),
      completed: (json['completed'] as num?)?.toInt(),
      inProgress: (json['inProgress'] as num?)?.toInt(),
      inReview: (json['inReview'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$TaskModelImplToJson(_$TaskModelImpl instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'department': instance.department,
      'workerIds': instance.workerIds,
      'projectId': instance.projectId,
      'deadlineDate': instance.deadlineDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'status': _$TaskStatusEnumMap[instance.status],
      'comment': instance.comment,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'total': instance.total,
      'completed': instance.completed,
      'inProgress': instance.inProgress,
      'inReview': instance.inReview,
    };

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};

const _$TaskStatusEnumMap = {
  TaskStatus.inProgress: 'in_progress',
  TaskStatus.completed: 'completed',
  TaskStatus.inReview: 'in_review',
  TaskStatus.rejected: 'rejected',
  TaskStatus.canceled: 'cancelled',
};
