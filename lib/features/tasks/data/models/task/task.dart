// ignore_for_file: invalid_annotation_target

import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/shared/styles/colors.dart';

part 'task.freezed.dart';
part 'task.g.dart';

@freezed
class TaskModel with _$TaskModel {
  const TaskModel._();
  const factory TaskModel({
    @JsonKey(name: '_id') String? id,
    Department? department,
    List<String>? workerIds,
    String? projectId,
    DateTime? deadlineDate,
    DateTime? endDate,
    TaskStatus? status,
    String? comment,
    DateTime? createdAt,
    DateTime? updatedAt,

    // additional
    int? total,
    int? completed,
    int? inProgress,
    int? inReview,
  }) = _TaskModel;

  factory TaskModel.fromJson(Map<String, dynamic> json) =>
      _$TaskModelFromJson(json);
}

enum TaskStatus {
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('in_review')
  inReview,
  @JsonValue('rejected')
  rejected,
  @JsonValue('cancelled')
  canceled;

  String getName() {
    switch (this) {
      case TaskStatus.inProgress:
        return 'В процессе';
      case TaskStatus.completed:
        return 'Выполнено';
      case TaskStatus.inReview:
        return 'На проверке';
      case TaskStatus.rejected:
        return 'Отклонено';
      case TaskStatus.canceled:
        return 'Отменено';
    }
  }

  Color getColor({bool isDarkTheme = false}) {
    switch (this) {
      case TaskStatus.inProgress:
        return isDarkTheme ? AppColors.darkPurple : AppColors.lightPurple;
      case TaskStatus.completed:
        return isDarkTheme ? AppColors.darkSuccess : AppColors.lightSuccess;
      case TaskStatus.inReview:
        return isDarkTheme ? AppColors.darkWarning : AppColors.lightWarning;
      case TaskStatus.rejected:
        return isDarkTheme ? AppColors.darkError : AppColors.lightError;
      case TaskStatus.canceled:
        return isDarkTheme
            ? AppColors.darkDescription
            : AppColors.lightDescription;
    }
  }
}
