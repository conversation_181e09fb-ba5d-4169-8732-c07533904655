// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/user/data/models/user.dart';

part 'comment.freezed.dart';
part 'comment.g.dart';

@freezed
class CommentModel with _$CommentModel {
  @JsonSerializable(includeIfNull: false)
  const factory CommentModel({
    @JsonKey(name: '_id') String? id,
    String? comment,
    String? userId,
    UserModel? user,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _CommentModel;

  factory CommentModel.fromJson(Map<String, dynamic> json) =>
      _$CommentModelFromJson(json);
}
