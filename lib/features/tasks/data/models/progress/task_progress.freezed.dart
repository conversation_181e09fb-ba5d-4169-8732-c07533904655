// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'task_progress.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TaskProgressModel _$TaskProgressModelFromJson(Map<String, dynamic> json) {
  return _TaskProgressModel.fromJson(json);
}

/// @nodoc
mixin _$TaskProgressModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get taskId => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  TaskStatus? get status => throw _privateConstructorUsedError;
  List<CommentModel>? get comments => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  List<UserModel>? get workers => throw _privateConstructorUsedError;
  DateTime? get deadlineDate => throw _privateConstructorUsedError;
  List<String>? get parentProgressTaskIds => throw _privateConstructorUsedError;

  /// Serializes this TaskProgressModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaskProgressModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskProgressModelCopyWith<TaskProgressModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskProgressModelCopyWith<$Res> {
  factory $TaskProgressModelCopyWith(
          TaskProgressModel value, $Res Function(TaskProgressModel) then) =
      _$TaskProgressModelCopyWithImpl<$Res, TaskProgressModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? taskId,
      String? projectId,
      String? productId,
      TaskStatus? status,
      List<CommentModel>? comments,
      DateTime? createdAt,
      DateTime? updatedAt,
      List<UserModel>? workers,
      DateTime? deadlineDate,
      List<String>? parentProgressTaskIds});
}

/// @nodoc
class _$TaskProgressModelCopyWithImpl<$Res, $Val extends TaskProgressModel>
    implements $TaskProgressModelCopyWith<$Res> {
  _$TaskProgressModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskProgressModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? taskId = freezed,
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? comments = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? workers = freezed,
    Object? deadlineDate = freezed,
    Object? parentProgressTaskIds = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus?,
      comments: freezed == comments
          ? _value.comments
          : comments // ignore: cast_nullable_to_non_nullable
              as List<CommentModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      workers: freezed == workers
          ? _value.workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<UserModel>?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      parentProgressTaskIds: freezed == parentProgressTaskIds
          ? _value.parentProgressTaskIds
          : parentProgressTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskProgressModelImplCopyWith<$Res>
    implements $TaskProgressModelCopyWith<$Res> {
  factory _$$TaskProgressModelImplCopyWith(_$TaskProgressModelImpl value,
          $Res Function(_$TaskProgressModelImpl) then) =
      __$$TaskProgressModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? taskId,
      String? projectId,
      String? productId,
      TaskStatus? status,
      List<CommentModel>? comments,
      DateTime? createdAt,
      DateTime? updatedAt,
      List<UserModel>? workers,
      DateTime? deadlineDate,
      List<String>? parentProgressTaskIds});
}

/// @nodoc
class __$$TaskProgressModelImplCopyWithImpl<$Res>
    extends _$TaskProgressModelCopyWithImpl<$Res, _$TaskProgressModelImpl>
    implements _$$TaskProgressModelImplCopyWith<$Res> {
  __$$TaskProgressModelImplCopyWithImpl(_$TaskProgressModelImpl _value,
      $Res Function(_$TaskProgressModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskProgressModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? taskId = freezed,
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? comments = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? workers = freezed,
    Object? deadlineDate = freezed,
    Object? parentProgressTaskIds = freezed,
  }) {
    return _then(_$TaskProgressModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskStatus?,
      comments: freezed == comments
          ? _value._comments
          : comments // ignore: cast_nullable_to_non_nullable
              as List<CommentModel>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      workers: freezed == workers
          ? _value._workers
          : workers // ignore: cast_nullable_to_non_nullable
              as List<UserModel>?,
      deadlineDate: freezed == deadlineDate
          ? _value.deadlineDate
          : deadlineDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      parentProgressTaskIds: freezed == parentProgressTaskIds
          ? _value._parentProgressTaskIds
          : parentProgressTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$TaskProgressModelImpl implements _TaskProgressModel {
  const _$TaskProgressModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.taskId,
      this.projectId,
      this.productId,
      this.status,
      final List<CommentModel>? comments,
      this.createdAt,
      this.updatedAt,
      final List<UserModel>? workers,
      this.deadlineDate,
      final List<String>? parentProgressTaskIds})
      : _comments = comments,
        _workers = workers,
        _parentProgressTaskIds = parentProgressTaskIds;

  factory _$TaskProgressModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaskProgressModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? taskId;
  @override
  final String? projectId;
  @override
  final String? productId;
  @override
  final TaskStatus? status;
  final List<CommentModel>? _comments;
  @override
  List<CommentModel>? get comments {
    final value = _comments;
    if (value == null) return null;
    if (_comments is EqualUnmodifiableListView) return _comments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  final List<UserModel>? _workers;
  @override
  List<UserModel>? get workers {
    final value = _workers;
    if (value == null) return null;
    if (_workers is EqualUnmodifiableListView) return _workers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? deadlineDate;
  final List<String>? _parentProgressTaskIds;
  @override
  List<String>? get parentProgressTaskIds {
    final value = _parentProgressTaskIds;
    if (value == null) return null;
    if (_parentProgressTaskIds is EqualUnmodifiableListView)
      return _parentProgressTaskIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TaskProgressModel(id: $id, taskId: $taskId, projectId: $projectId, productId: $productId, status: $status, comments: $comments, createdAt: $createdAt, updatedAt: $updatedAt, workers: $workers, deadlineDate: $deadlineDate, parentProgressTaskIds: $parentProgressTaskIds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskProgressModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._comments, _comments) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other._workers, _workers) &&
            (identical(other.deadlineDate, deadlineDate) ||
                other.deadlineDate == deadlineDate) &&
            const DeepCollectionEquality()
                .equals(other._parentProgressTaskIds, _parentProgressTaskIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      taskId,
      projectId,
      productId,
      status,
      const DeepCollectionEquality().hash(_comments),
      createdAt,
      updatedAt,
      const DeepCollectionEquality().hash(_workers),
      deadlineDate,
      const DeepCollectionEquality().hash(_parentProgressTaskIds));

  /// Create a copy of TaskProgressModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskProgressModelImplCopyWith<_$TaskProgressModelImpl> get copyWith =>
      __$$TaskProgressModelImplCopyWithImpl<_$TaskProgressModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaskProgressModelImplToJson(
      this,
    );
  }
}

abstract class _TaskProgressModel implements TaskProgressModel {
  const factory _TaskProgressModel(
      {@JsonKey(name: '_id') final String? id,
      final String? taskId,
      final String? projectId,
      final String? productId,
      final TaskStatus? status,
      final List<CommentModel>? comments,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      final List<UserModel>? workers,
      final DateTime? deadlineDate,
      final List<String>? parentProgressTaskIds}) = _$TaskProgressModelImpl;

  factory _TaskProgressModel.fromJson(Map<String, dynamic> json) =
      _$TaskProgressModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get taskId;
  @override
  String? get projectId;
  @override
  String? get productId;
  @override
  TaskStatus? get status;
  @override
  List<CommentModel>? get comments;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  List<UserModel>? get workers;
  @override
  DateTime? get deadlineDate;
  @override
  List<String>? get parentProgressTaskIds;

  /// Create a copy of TaskProgressModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskProgressModelImplCopyWith<_$TaskProgressModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
