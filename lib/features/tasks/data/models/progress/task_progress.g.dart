// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_progress.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TaskProgressModelImpl _$$TaskProgressModelImplFromJson(
        Map<String, dynamic> json) =>
    _$TaskProgressModelImpl(
      id: json['_id'] as String?,
      taskId: json['taskId'] as String?,
      projectId: json['projectId'] as String?,
      productId: json['productId'] as String?,
      status: $enumDecodeNullable(_$TaskStatusEnumMap, json['status']),
      comments: (json['comments'] as List<dynamic>?)
          ?.map((e) => CommentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      workers: (json['workers'] as List<dynamic>?)
          ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      deadlineDate: json['deadlineDate'] == null
          ? null
          : DateTime.parse(json['deadlineDate'] as String),
      parentProgressTaskIds: (json['parentProgressTaskIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$TaskProgressModelImplToJson(
        _$TaskProgressModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.taskId case final value?) 'taskId': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.productId case final value?) 'productId': value,
      if (_$TaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.comments case final value?) 'comments': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.workers case final value?) 'workers': value,
      if (instance.deadlineDate?.toIso8601String() case final value?)
        'deadlineDate': value,
      if (instance.parentProgressTaskIds case final value?)
        'parentProgressTaskIds': value,
    };

const _$TaskStatusEnumMap = {
  TaskStatus.inProgress: 'in_progress',
  TaskStatus.completed: 'completed',
  TaskStatus.inReview: 'in_review',
  TaskStatus.rejected: 'rejected',
  TaskStatus.canceled: 'cancelled',
};
