// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/tasks/data/models/comment/comment.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';
import 'package:sphere/features/user/data/models/user.dart';

part 'task_progress.freezed.dart';
part 'task_progress.g.dart';

@freezed
class TaskProgressModel with _$TaskProgressModel {
  @JsonSerializable(includeIfNull: false)
  const factory TaskProgressModel({
    @JsonKey(name: '_id') String? id,
    String? taskId,
    String? projectId,
    String? productId,
    TaskStatus? status,
    List<CommentModel>? comments,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<UserModel>? workers,
    DateTime? deadlineDate,
    List<String>? parentProgressTaskIds,
  }) = _TaskProgressModel;

  factory TaskProgressModel.fromJson(Map<String, dynamic> json) =>
      _$TaskProgressModelFromJson(json);
}
