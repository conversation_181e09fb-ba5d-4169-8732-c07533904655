// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreateTaskModelImpl _$$CreateTaskModelImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateTaskModelImpl(
      department: $enumDecodeNullable(_$DepartmentEnumMap, json['department']),
      deadlineDate: json['deadlineDate'] == null
          ? null
          : DateTime.parse(json['deadlineDate'] as String),
      projectId: json['projectId'] as String?,
      workerIds: (json['workerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      productIds: (json['targetIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      includeChildProducts: json['includeChildProducts'] as bool?,
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['provisionType']),
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$$CreateTaskModelImplToJson(
        _$CreateTaskModelImpl instance) =>
    <String, dynamic>{
      if (instance.department case final value?) 'department': value,
      if (instance.deadlineDate?.toIso8601String() case final value?)
        'deadlineDate': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.workerIds case final value?) 'workerIds': value,
      if (instance.productIds case final value?) 'targetIds': value,
      if (instance.includeChildProducts case final value?)
        'includeChildProducts': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'provisionType': value,
      if (instance.comment case final value?) 'comment': value,
    };

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};

const _$ProvisionsFilterEnumMap = {
  ProvisionsFilter.cooperation: 'cooperation',
  ProvisionsFilter.materials: 'materials',
  ProvisionsFilter.cooperationMaterials: 'cooperation_materials',
  ProvisionsFilter.assemblyMaterials: 'assembly_materials',
  ProvisionsFilter.purchased: 'purchased',
  ProvisionsFilter.assembly: 'assembly',
};

_$EditTaskModelImpl _$$EditTaskModelImplFromJson(Map<String, dynamic> json) =>
    _$EditTaskModelImpl(
      workerIds: (json['workerIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      deadlineDate: json['deadlineDate'] == null
          ? null
          : DateTime.parse(json['deadlineDate'] as String),
    );

Map<String, dynamic> _$$EditTaskModelImplToJson(_$EditTaskModelImpl instance) =>
    <String, dynamic>{
      if (instance.workerIds case final value?) 'workerIds': value,
      if (instance.deadlineDate?.toIso8601String() case final value?)
        'deadlineDate': value,
    };

_$SearchTasksOutputModelImpl _$$SearchTasksOutputModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SearchTasksOutputModelImpl(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => TaskModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$$SearchTasksOutputModelImplToJson(
        _$SearchTasksOutputModelImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
      if (instance.message case final value?) 'message': value,
    };

_$UpdateTaskProgressStatusModelImpl
    _$$UpdateTaskProgressStatusModelImplFromJson(Map<String, dynamic> json) =>
        _$UpdateTaskProgressStatusModelImpl(
          taskProgressIds: (json['taskProgressIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
          status: $enumDecodeNullable(_$TaskStatusEnumMap, json['status']),
          comment: json['comment'] as String?,
          includeChildren: json['includeChildren'] as bool?,
        );

Map<String, dynamic> _$$UpdateTaskProgressStatusModelImplToJson(
        _$UpdateTaskProgressStatusModelImpl instance) =>
    <String, dynamic>{
      if (instance.taskProgressIds case final value?) 'taskProgressIds': value,
      if (_$TaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.includeChildren case final value?) 'includeChildren': value,
    };

const _$TaskStatusEnumMap = {
  TaskStatus.inProgress: 'in_progress',
  TaskStatus.completed: 'completed',
  TaskStatus.inReview: 'in_review',
  TaskStatus.rejected: 'rejected',
  TaskStatus.canceled: 'cancelled',
};
