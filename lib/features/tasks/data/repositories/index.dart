import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sphere/features/tasks/data/models/index.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class TasksRepository {
  static Future<Response<TaskModel>?> create(
    CreateTaskModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<TaskModel>(
      url: '/tasks/createWorkTask',
      body: body,
      method: 'POST',
      fromJson: TaskModel.fromJson,
    );

    return request;
  }

  static Future<Response<TaskModel>?> edit(
    EditTaskModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<TaskModel>(
      url: '/tasks/editWorkTask',
      body: body,
      method: 'POST',
      fromJson: TaskModel.fromJson,
    );

    return request;
  }

  static Future<Response<SearchTasksOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<SearchTasksOutputModel>(
      url: '/tasks/search',
      body: body,
      method: 'POST',
      fromJson: SearchTasksOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<TaskProgressModel>?> addTaskProgressComment(
    String taskProgressId,
    String comment,
  ) async {
    final body = jsonEncode({
      'taskProgressId': taskProgressId,
      'comment': comment,
    });

    final request = await API.request<TaskProgressModel>(
      url: '/tasks/addTaskProgressComment',
      body: body,
      method: 'POST',
      fromJson: TaskProgressModel.fromJson,
    );

    return request;
  }

  static Future<Response<TaskProgressModel>?> updateTaskProgressStatus(
    UpdateTaskProgressStatusModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<TaskProgressModel>(
      url: '/tasks/updateTaskProgressStatus',
      body: body,
      method: 'POST',
      fromJson: TaskProgressModel.fromJson,
    );

    return request;
  }
}
