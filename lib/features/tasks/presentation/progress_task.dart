import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/structure/presentation/bloc/bloc.dart';
import 'package:sphere/features/tasks/data/models/index.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';
import 'package:sphere/features/tasks/data/repositories/index.dart';
import 'package:sphere/features/tasks/presentation/users.dart';
import 'package:sphere/features/user/presentation/users_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/utility/comment.dart';

class ProgressTask extends StatefulWidget {
  const ProgressTask({
    super.key,
    required this.progressTask,
    this.isLoading,
    this.refresher,
    this.isSingle = false,
    this.progressTaskForceSelected = false,
    this.selected = false,
    this.onSelect,
  });

  final TaskProgressModel progressTask;
  final bool? isLoading;
  final void Function()? refresher;
  final bool isSingle;
  final bool progressTaskForceSelected;
  final bool selected;
  final void Function()? onSelect;

  @override
  State<ProgressTask> createState() => _ProgressTaskState();
}

class _ProgressTaskState extends State<ProgressTask> {
  bool _isLoading = false;
  TaskStatus _newStatus = TaskStatus.inProgress;
  final TextEditingController _commentController = TextEditingController();
  bool _withChilds = false;

  void _initialize() {
    _newStatus = widget.progressTask.status ?? TaskStatus.inProgress;
  }

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _changeStatus(List<TaskProgressModel> selectedProgressTasks) async {
    if (widget.progressTask.id == null) return;

    setState(() => _isLoading = true);

    try {
      await TasksRepository.updateTaskProgressStatus(
        UpdateTaskProgressStatusModel(
          taskProgressIds: selectedProgressTasks
              .map((task) => task.id)
              .whereType<String>()
              .toList(),
          status: _newStatus,
          comment: _commentController.text.isNotEmpty
              ? _commentController.text
              : null,
          includeChildren: _withChilds,
        ),
      );
      widget.refresher?.call();
    } finally {
      setState(() => _isLoading = false);
      context
          .read<BlocStructure>()
          .add(ClearProgressTasksSelectionsInStructure());
    }
  }

  void _showChangeStatusDialog(List<TaskProgressModel> selectedProgressTasks) {
    CustomDropdownMenu.instance.hide();
    showDialog(
      context: context,
      builder: (context) {
        final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

        return AlertDialog(
          backgroundColor: isDarkTheme
              ? AppColors.darkBackground
              : AppColors.lightBackground,
          title: const Text('Изменение статуса', style: Fonts.titleSmall),
          content: StatefulBuilder(builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_isLoading) ...[
                  const Center(child: CircularProgressIndicator()),
                  const SizedBox(height: 12.0),
                ],
                ...TaskStatus.values.map((status) {
                  return RadioListTile<TaskStatus>(
                    value: status,
                    selected: status == _newStatus,
                    title: Text(status.getName(), style: Fonts.labelSmall),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _newStatus = value);
                      }
                    },
                    groupValue: _newStatus,
                  );
                }),
                const SizedBox(height: 12.0),
                TextField(
                  controller: _commentController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    labelText: 'Комментарий',
                    border: OutlineInputBorder(),
                  ),
                  minLines: 1,
                  maxLines: 4,
                ),
                CheckboxListTile.adaptive(
                  value: _withChilds,
                  onChanged: (value) {
                    setState(() => _withChilds = value ?? false);
                  },
                  title: Text('Вклюая дочерние подзадачи',
                      style: Fonts.labelSmall),
                )
              ],
            );
          }),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена', style: Fonts.labelSmall),
            ),
            TextButton(
              onPressed: () {
                _changeStatus(selectedProgressTasks);
                Navigator.of(context).pop();
              },
              child: const Text('Подтвердить', style: Fonts.labelSmall),
            ),
          ],
        );
      },
    );
  }

  void _showDetails(BuildContext context) {
    CustomDropdownMenu.instance.hide();

    final progressTask = widget.progressTask;
    final comments = progressTask.comments?.reversed.toList() ?? [];

    showBaseDialog(context, builder: (context) {
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Задача до ${getDateString(progressTask.deadlineDate ?? DateTime(2000))}',
            style: Fonts.titleSmall,
          ),
          Text(
            progressTask.status?.getName() ?? 'status not found',
            style: Fonts.labelSmall.merge(TextStyle(
              color: progressTask.status?.getColor(),
            )),
          ),
          SizedBox(height: 12.0),
          UsersCard(
            minified: true,
            users: progressTask.workers ?? [],
          ),
          SizedBox(height: 12.0),
          if (comments.isEmpty)
            Expanded(
              child: Center(
                child: Text(
                  'Комментарии отсутствуют',
                  style: Fonts.labelSmall.merge(
                    TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription),
                  ),
                ),
              ),
            ),
          if (comments.isNotEmpty)
            Expanded(
              child: ListView.separated(
                reverse: true,
                itemCount: comments.length,
                itemBuilder: (context, index) {
                  final comment = comments[index];

                  return Comment(data: comment);
                },
                separatorBuilder: (_, __) => SizedBox(height: 8.0),
              ),
            )
        ],
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = context.read<BlocStructure>().state;
    final message = widget.progressTask.status?.getName() ?? 'message';

    // print(state.selectedProgressTasks);

    // final selectedProgressTaskIds = state.selectedProgressTasks
    //     .map((progressTask) => progressTask.id)
    //     .where((id) => id != null)
    //     .toSet();

    // bool getIsSelected() {
    //   if (selectedProgressTaskIds.isEmpty || widget.progressTask.id == null) {
    //     return false;
    //   }
    //   return selectedProgressTaskIds.contains(widget.progressTask.id);
    // }

    // final isSelected = getIsSelected();

    void toggleSelect() {
      context.read<BlocStructure>().add(
            ToggleSelectedProgressTaskInStructure(
              widget.progressTask,
            ),
          );
    }

    final color =
        (widget.progressTask.status ?? TaskStatus.inProgress).getColor();

    return Tooltip(
        message: message,
        textStyle: Fonts.labelSmall.merge(TextStyle(
          color: color,
        )),
        child: CustomCard(
          border: Border.all(
            color: color,
            width: 2.0,
          ),
          isLoading: widget.isLoading,
          padding: EdgeInsets.all(8.0),
          onTap: () => _showDetails(context),
          onSecondaryTapDown: (details) {
            CustomDropdownMenu.instance.show(
              context: context,
              items: [
                CustomDropdownMenuItem(
                  text: 'Изменить статус',
                  onTap: () {
                    if (widget.isSingle) {
                      _showChangeStatusDialog([widget.progressTask]);
                    } else {
                      _showChangeStatusDialog(state.selectedProgressTasks);
                    }
                  },
                ),
                // CustomDropdownMenuItem(
                //   text: 'Удалить',
                //   onTap: () {
                //     _removeTask();
                //   },
                // ),
              ],
              position: details.globalPosition,
            );
          },
          child: Wrap(
            // mainAxisSize: MainAxisSize.min,
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            // spacing: 12.0,
            children: [
              if (!widget.isSingle)
                Checkbox.adaptive(
                    value: widget.progressTaskForceSelected || widget.selected,
                    onChanged: (value) {
                      if (widget.progressTaskForceSelected) return;
                      toggleSelect();
                    }),
              if (widget.progressTask.deadlineDate != null)
                SizedBox(width: 8.0),
              if (widget.progressTask.deadlineDate != null)
                Text(
                  'До: ${getDateString(
                    widget.progressTask.deadlineDate!,
                  )}',
                  style: Fonts.labelSmall,
                ),
              if (widget.progressTask.deadlineDate != null)
                SizedBox(width: 16.0),
              TaskProgressUsers(
                workers: widget.progressTask.workers,
                // onRemove: (user) {
                //   _removeWorkers([user]);
                // },
              ),
            ],
          ),
        ));
  }
}
