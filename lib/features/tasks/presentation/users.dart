import 'package:flutter/material.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/presentation/user_card.dart';

class TaskProgressUsers extends StatelessWidget {
  const TaskProgressUsers({super.key, this.workers, this.onRemove});

  final List<UserModel>? workers;
  final Function(UserModel)? onRemove;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 10.0,
      runSpacing: 10.0,
      children: workers?.map((user) {
            return IgnorePointer(
              child: UserCard(
                wrapped: false,
                minified: true,
                user: user,
              ),
            );
          }).toList() ??
          [],
    );
  }
}
