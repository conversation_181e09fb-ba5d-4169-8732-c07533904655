import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/tasks/data/models/index.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';
import 'package:sphere/features/tasks/data/repositories/index.dart';
import 'package:sphere/features/tasks/presentation/users.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class SupplyProgressTask extends StatefulWidget {
  const SupplyProgressTask({
    super.key,
    required this.progressTask,
    this.refresher,
    this.isLoading = false,
    this.isSingle = false,
    this.selected = false,
    this.progressTaskForceSelected = false,
  });

  final TaskProgressModel progressTask;
  final VoidCallback? refresher;
  final bool isLoading;
  final bool isSingle;
  final bool selected;
  final bool progressTaskForceSelected;

  @override
  State<SupplyProgressTask> createState() => _SupplyProgressTaskState();
}

class _SupplyProgressTaskState extends State<SupplyProgressTask> {
  bool _isLoading = false;
  TaskStatus _newStatus = TaskStatus.inProgress;
  final _commentController = TextEditingController();
  bool _withChilds = false;

  void _showChangeStatusDialog(List<TaskProgressModel> selectedProgressTasks) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: isDarkTheme
              ? AppColors.darkBackground
              : AppColors.lightBackground,
          title: const Text('Изменение статуса', style: Fonts.titleSmall),
          content: StatefulBuilder(builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_isLoading) ...[
                  const Center(child: CircularProgressIndicator()),
                  const SizedBox(height: 12.0),
                ],
                ...TaskStatus.values.map((status) {
                  return RadioListTile<TaskStatus>(
                    value: status,
                    selected: status == _newStatus,
                    title: Text(status.getName(), style: Fonts.labelSmall),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _newStatus = value);
                      }
                    },
                    groupValue: _newStatus,
                  );
                }),
                const SizedBox(height: 12.0),
                TextField(
                  controller: _commentController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    labelText: 'Комментарий',
                  ),
                  minLines: 1,
                  maxLines: 4,
                ),
                const SizedBox(height: 12.0),
                CheckboxListTile(
                  value: _withChilds,
                  onChanged: (value) {
                    setState(() => _withChilds = value ?? false);
                  },
                  title: const Text(
                    'Включить дочерние задачи',
                    style: Fonts.labelSmall,
                  ),
                ),
              ],
            );
          }),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена'),
            ),
            CustomElevatedButton(
              type: CustomElevatedButtonTypes.accent,
              onPressed: () {
                Navigator.of(context).pop();
                _changeStatus(selectedProgressTasks);
              },
              text: 'Изменить',
            ),
          ],
        );
      },
    );
  }

  void _changeStatus(List<TaskProgressModel> selectedProgressTasks) async {
    if (widget.progressTask.id == null) return;

    setState(() => _isLoading = true);

    try {
      await TasksRepository.updateTaskProgressStatus(
        UpdateTaskProgressStatusModel(
          taskProgressIds: selectedProgressTasks
              .map((task) => task.id)
              .whereType<String>()
              .toList(),
          status: _newStatus,
          comment: _commentController.text.isNotEmpty
              ? _commentController.text
              : null,
          includeChildren: _withChilds,
        ),
      );
      widget.refresher?.call();
    } finally {
      setState(() => _isLoading = false);
      context
          .read<BlocPurchaseList>()
          .add(ClearProgressTasksSelectionsInPurchaseList());
    }
  }

  void _showDetails(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: isDarkTheme
              ? AppColors.darkBackground
              : AppColors.lightBackground,
          title: const Text('Детали задачи', style: Fonts.titleSmall),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.progressTask.deadlineDate != null) ...[
                Text(
                  'Срок выполнения: ${getDateString(widget.progressTask.deadlineDate!)}',
                  style: Fonts.labelSmall,
                ),
                const SizedBox(height: 8.0),
              ],
              Text(
                'Статус: ${widget.progressTask.status?.getName() ?? 'Не указан'}',
                style: Fonts.labelSmall,
              ),
              const SizedBox(height: 8.0),
              if (widget.progressTask.workers != null &&
                  widget.progressTask.workers!.isNotEmpty) ...[
                const Text('Исполнители:', style: Fonts.labelSmall),
                const SizedBox(height: 4.0),
                TaskProgressUsers(workers: widget.progressTask.workers),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Закрыть'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = context.read<BlocPurchaseList>().state;
    // final message = widget.progressTask.status?.getName() ?? 'message';

    // void toggleSelect() {
    //   context.read<BlocPurchaseList>().add(
    //         ToggleSelectedProgressTaskInPurchaseList(
    //           widget.progressTask,
    //         ),
    //       );
    // }

    Color color = widget.progressTask.status?.getColor() ?? AppColors.medium;

    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: CustomCard(
          border: Border.all(
            color: color,
            width: 2.0,
          ),
          isLoading: widget.isLoading,
          padding: EdgeInsets.all(8.0),
          onTap: () => _showDetails(context),
          onSecondaryTapDown: (details) {
            CustomDropdownMenu.instance.show(
              context: context,
              items: [
                CustomDropdownMenuItem(
                  text: 'Изменить статус',
                  onTap: () {
                    if (widget.isSingle) {
                      _showChangeStatusDialog([widget.progressTask]);
                    } else {
                      _showChangeStatusDialog(state.selectedProgressTasks);
                    }
                  },
                ),
              ],
              position: details.globalPosition,
            );
          },
          child: Wrap(
            runAlignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              if (!widget.isSingle)
                Checkbox.adaptive(
                    value: widget.progressTaskForceSelected || widget.selected,
                    onChanged: (value) {
                      // if (widget.progressTaskForceSelected) return;
                      // toggleSelect();
                    }),
              if (widget.progressTask.deadlineDate != null)
                SizedBox(width: 8.0),
              if (widget.progressTask.deadlineDate != null)
                Text(
                  'До: ${getDateString(
                    widget.progressTask.deadlineDate!,
                  )}',
                  style: Fonts.labelSmall,
                ),
              if (widget.progressTask.deadlineDate != null)
                SizedBox(width: 16.0),
              TaskProgressUsers(
                workers: widget.progressTask.workers,
              ),
            ],
          ),
        ));
  }
}

// Extension to get color for TaskStatus
extension TaskStatusColor on TaskStatus {
  Color getColor() {
    switch (this) {
      case TaskStatus.inProgress:
        return AppColors.darkWarning;
      case TaskStatus.completed:
        return AppColors.darkSuccess;
      case TaskStatus.inReview:
        return AppColors.darkSecondary;
      case TaskStatus.rejected:
        return AppColors.darkError;
      case TaskStatus.canceled:
        return AppColors.medium;
    }
  }
}
