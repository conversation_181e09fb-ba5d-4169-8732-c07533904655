// import 'package:flutter/material.dart';

// class TaskProgressChangeStatusBody extends StatefulWidget {
//   const TaskProgressChangeStatusBody({
//     super.key,
//     required this.product,
//     this.refresher,
//   });

//   final ProductModel product;
//   final VoidCallback? refresher;

//   @override
//   State<ProductEditOperationsBody> createState() =>
//       _ProductEditOperationsBodyState();
// }

// class _ProductEditOperationsBodyState extends State<ProductEditOperationsBody> {
//   bool _isLoading = false;
//   final List<TextEditingController> _controllers = [];

//   @override
//   void initState() {
//     super.initState();
//     _initialize();
//   }

//   void _initialize() {
//     _controllers.addAll(widget.product.operations
//             ?.map((op) => TextEditingController(text: op)) ??
//         []);
//   }

//   Future<void> _editOperations() async {
//     if (widget.product.id == null) return;

//     setState(() => _isLoading = true);

//     try {
//       final result = await ProductRepository.editOperations(
//         productId: widget.product.id!,
//         operations: _controllers.map((c) => c.text).toList(),
//       );
//       Logger().d(result?.data);
//       widget.refresher?.call();
//       CustomDrawer.instance.hide();
//     } finally {
//       setState(() => _isLoading = false);
//     }
//   }

//   void _addOperation() {
//     setState(() {
//       _controllers.add(TextEditingController(text: ''));
//     });
//   }

//   void _removeOperation(int index) {
//     setState(() {
//       _controllers[index].dispose();
//       _controllers.removeAt(index);
//     });
//   }

//   void _onReorder(int oldIndex, int newIndex) {
//     if (newIndex > oldIndex) {
//       newIndex -= 1; // Корректируем индекс, если элемент перемещается вниз
//     }
//     setState(() {
//       final controller = _controllers.removeAt(oldIndex);
//       _controllers.insert(newIndex, controller);
//     });
//   }

//   @override
//   void dispose() {
//     for (var controller in _controllers) {
//       controller.dispose();
//     }
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

//     return Stack(
//       children: [
//         if (_isLoading) LoadingIndicator(isDarkTheme: isDarkTheme),
//         Column(
//           children: [
//             Expanded(
//               child: ContentSection(
//                 controllers: _controllers,
//                 addOperation: _addOperation,
//                 removeOperation: _removeOperation,
//                 onReorder: _onReorder,
//               ),
//             ),
//             SaveButton(onPressed: _editOperations),
//           ],
//         ),
//       ],
//     );
//   }
// }

// class ContentSection extends StatelessWidget {
//   const ContentSection({
//     super.key,
//     required this.controllers,
//     required this.addOperation,
//     required this.removeOperation,
//     required this.onReorder,
//   });

//   final List<TextEditingController> controllers;
//   final VoidCallback addOperation;
//   final void Function(int index) removeOperation;
//   final void Function(int oldIndex, int newIndex) onReorder;

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.all(20.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'Изменение технологической карты',
//             style: Fonts.titleMedium.merge(const TextStyle(height: 1.6)),
//           ),
//           const SizedBox(height: 24.0),
//           Expanded(
//             child: OperationsSection(
//               controllers: controllers,
//               addOperation: addOperation,
//               removeOperation: removeOperation,
//               onReorder: onReorder,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
