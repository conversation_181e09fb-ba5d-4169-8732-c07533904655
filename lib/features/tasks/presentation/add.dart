import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/structure/presentation/bloc/bloc.dart';
import 'package:sphere/features/tasks/data/models/index.dart';
import 'package:sphere/features/tasks/data/repositories/index.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class AddTaskDrawer extends StatefulWidget {
  const AddTaskDrawer({
    super.key,
    this.projectId,
    this.productId,
    this.selecetedProducts,
    this.onSuccess,
    this.departments,
  });

  final String? projectId;
  final String? productId;
  final List<String>? selecetedProducts;
  final void Function()? onSuccess;
  final List<Department>? departments;

  @override
  State<AddTaskDrawer> createState() => _AddTaskDrawerState();
}

class _AddTaskDrawerState extends State<AddTaskDrawer> {
  final ScrollController _scrollController = ScrollController();
  bool _showTopGradient = false;
  bool _showBottomGradient = true;
  List<UserModel> _users = [
    const UserModel(),
    const UserModel(),
    const UserModel(),
  ];
  final List<UserModel> _selectedUsers = [];
  bool _isLoading = false;
  final _searchController = TextEditingController(text: '');
  DateTime _selectedDeadline = DateTime.now();
  final _commentController = TextEditingController();
  bool _calendarIsVisible = false;
  Department _selectedDepartment = Department.ogt;

  void _loadUsers() async {
    setState(() {
      _isLoading = true;
    });
    final result = await UserRepository.search(SearchModel(
      filters: SearchFiltersModel(
        department: _selectedDepartment,
        query:
            _searchController.text.isNotEmpty ? _searchController.text : null,
        roles: [UserRole.worker],
      ),
    ));

    setState(() {
      if (result?.data != null && result!.data?.items != null) {
        final users = result.data!.items!;
        _users = users;
      }
      _isLoading = false;
    });
  }

  void _onSelectUser(UserModel user) {
    setState(() {
      if (_selectedUsers.contains(user)) {
        _selectedUsers.remove(user);
      } else {
        _selectedUsers.add(user);
      }
    });
  }

  void _addSelectedUsers() async {
    setState(() {
      _isLoading = true;
    });
    // if (widget.productId == null) {
    //   print('product id is not found');
    //   return;
    // }
    if (widget.projectId == null) {
      print('project id is not found');
      return;
    }
    final selectedUsersIds = _selectedUsers
        .where((user) => user.id != null)
        .map((user) => user.id!)
        .toList();

    final selectedProducts = context.read<BlocStructure>().state.selected;

    final result = await TasksRepository.create(CreateTaskModel(
      projectId: widget.projectId,
      productIds: selectedProducts.isNotEmpty
          ? selectedProducts.map((product) => product.id ?? '').toList()
          : null,
      workerIds: selectedUsersIds,
      includeChildProducts: true,
      deadlineDate: _selectedDeadline,
      // department: _selectedDepartment,
      comment:
          _commentController.text.isNotEmpty ? _commentController.text : null,
    ));
    if (result?.data == null) return;
    widget.onSuccess?.call();

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
    _selectedDepartment = widget.departments?.first ?? Department.ogt;
    _loadUsers();
  }

  void _handleScroll() {
    setState(() {
      _showTopGradient = _scrollController.offset > 0;
      _showBottomGradient = _scrollController.position.pixels <
          _scrollController.position.maxScrollExtent;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(
        children: [
          // Padding(
          //   padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
          //   child: Text(
          //     'Добавление задачи',
          //     style: Fonts.labelSmall,
          //   ),
          // ),
          // SizedBox(height: 20.0),
          Padding(
            // padding: const EdgeInsets.symmetric(horizontal: 16.0),
            padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Планируемая дата завершения:', style: Fonts.labelSmall),
                GhostButton(
                  onTap: () {
                    setState(() {
                      _calendarIsVisible = !_calendarIsVisible;
                    });
                  },
                  child: Text(
                    getDateString(_selectedDeadline),
                    style: Fonts.labelSmall.merge(
                      TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkSecondary
                            : AppColors.lightSecondary,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (_calendarIsVisible) SizedBox(height: 8.0),
          if (_calendarIsVisible) Divider(height: 1.0),
          if (_calendarIsVisible)
            CalendarDatePicker(
              initialDate: _selectedDeadline,
              firstDate: DateTime.now(),
              lastDate: DateTime.now().add(Duration(days: 730)),
              currentDate: _selectedDeadline,
              onDateChanged: (DateTime newDate) {
                setState(() {
                  _selectedDeadline = newDate;
                  _calendarIsVisible = false;
                });
              },
            ),
          if (_calendarIsVisible) Divider(height: 1.0),
          if (widget.departments == null ||
              (widget.departments != null && widget.departments!.length > 1))
            SizedBox(height: 20.0),
          if (widget.departments == null ||
              (widget.departments != null && widget.departments!.length > 1))
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Для какого отдела:',
                    style: Fonts.labelSmall,
                  ),
                  SizedBox(width: 8.0),
                  Expanded(
                    child: Wrap(
                      alignment: WrapAlignment.end,
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children: List.generate(
                          widget.departments?.length ??
                              Department.values.length, (index) {
                        final department = widget.departments?[index] ??
                            Department.values[index];

                        return CustomChip(
                          onTap: () {
                            setState(() {
                              _selectedDepartment = department;
                            });
                            _loadUsers();
                          },
                          selected: department == _selectedDepartment,
                          text: department.getName(),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
          SizedBox(height: 20.0),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              onChanged: (_) {
                _loadUsers();
              },
              controller: _searchController,
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                hintText: 'Поиск',
                suffixIcon: Align(
                  widthFactor: 1.0,
                  alignment: Alignment.center,
                  child: SVG(
                    Assets.icons.search,
                    color: AppColors.medium,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 20.0),
          Expanded(
            child: ShaderMask(
              shaderCallback: (Rect rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    if (_showTopGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                    isDarkTheme
                        ? AppColors.darkBackground.withValues(alpha: 0)
                        : AppColors.lightBackground.withValues(alpha: 0),
                    isDarkTheme
                        ? AppColors.darkBackground.withValues(alpha: 0)
                        : AppColors.lightBackground.withValues(alpha: 0),
                    if (_showBottomGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                  ],
                  stops: const [0.0, 0.05, 0.95, 1.0],
                ).createShader(rect);
              },
              blendMode: BlendMode.dstOut,
              child: ListView(
                controller: _scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                children: [
                  ..._selectedUsers.asMap().entries.map((entry) {
                    final user = entry.value;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        UserCard(
                          selected: true,
                          isLoading: _isLoading,
                          onTap: () {
                            _onSelectUser(user);
                          },
                          user: user,
                        ),
                        const SizedBox(height: 12.0),
                      ],
                    );
                  }),
                  if (_selectedUsers.isNotEmpty) const Divider(height: 1.0),
                  if (_selectedUsers.isNotEmpty) const SizedBox(height: 12.0),
                  ..._users
                      .where((user) => !_selectedUsers.contains(user))
                      .toList()
                      .asMap()
                      .entries
                      .map((entry) {
                    final user = entry.value;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        UserCard(
                          isLoading: _isLoading,
                          onTap: () {
                            _onSelectUser(user);
                          },
                          user: user,
                        ),
                        const SizedBox(height: 12.0),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
          SizedBox(height: 12.0),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: TextField(
              controller: _commentController,
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                labelText: 'Комментарий',
              ),
              minLines: 1,
              maxLines: 4,
            ),
          ),
          SizedBox(height: 12.0),
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: CustomElevatedButton(
              type: CustomElevatedButtonTypes.accent,
              onPressed: _addSelectedUsers,
              text: 'Добавить исполнителей',
            ),
          )
        ],
      ),
    ]);
  }
}
