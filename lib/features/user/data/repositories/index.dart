import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sphere/features/user/data/models/index.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class UserRepository {
  static Future<Response<MessageUserOutputModel>?> craete(
    CreateUserInputModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<MessageUserOutputModel>(
      url: '/users/create',
      body: body,
      method: 'POST',
      fromJson: MessageUserOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<MessageUserOutputModel>?> edit(
    UserModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<MessageUserOutputModel>(
      url: '/users/edit',
      body: body,
      method: 'POST',
      fromJson: MessageUserOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<UserModel>?> getById(
    String id,
  ) async {
    final body = jsonEncode({'userId': id});

    final request = await API.request<UserModel>(
      url: '/users/getById',
      body: body,
      method: 'POST',
      fromJson: UserModel.fromJson,
    );

    return request;
  }

  static Future<Response<SearchUserOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<SearchUserOutputModel>(
      url: '/users/search',
      body: body,
      method: 'POST',
      fromJson: SearchUserOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<MessageUserOutputModel>?> block(
    String id,
  ) async {
    final body = jsonEncode({'userId': id});

    final request = await API.request<MessageUserOutputModel>(
      url: '/users/block',
      body: body,
      method: 'POST',
      fromJson: MessageUserOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<MessageUserOutputModel>?> unblock(
    String id,
  ) async {
    final body = jsonEncode({'userId': id});

    final request = await API.request<MessageUserOutputModel>(
      url: '/users/unblock',
      body: body,
      method: 'POST',
      fromJson: MessageUserOutputModel.fromJson,
    );

    return request;
  }
}
