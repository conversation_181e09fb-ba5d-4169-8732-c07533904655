// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'avatar.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AvatarModel _$AvatarModelFromJson(Map<String, dynamic> json) => AvatarModel(
      id: json['_id'] as String?,
      url: json['url'] as String?,
      previewUrl: json['previewUrl'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      userId: json['userId'] as String?,
    );

Map<String, dynamic> _$AvatarModelToJson(AvatarModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.url case final value?) 'url': value,
      if (instance.previewUrl case final value?) 'previewUrl': value,
      if (instance.width case final value?) 'width': value,
      if (instance.height case final value?) 'height': value,
      if (instance.userId case final value?) 'userId': value,
    };
