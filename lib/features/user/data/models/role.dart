import 'package:json_annotation/json_annotation.dart';

enum UserRole {
  @JsonValue('admin')
  admin,
  @JsonValue('headmanager')
  headmanager,
  @JsonValue('manager')
  manager,
  @JsonValue('worker')
  worker;

  String getName() {
    switch (this) {
      case UserRole.admin:
        return 'Админ';
      case UserRole.headmanager:
        return 'Главный Руководитель';
      case UserRole.manager:
        return 'Руководитель';
      case UserRole.worker:
        return 'Рабочий';
    }
  }
}
