import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/user/data/models/avatar.dart';
import 'package:sphere/features/user/data/models/role.dart';

part 'user.g.dart';

@JsonSerializable(includeIfNull: false)
class UserModel {
  @JsonKey(name: '_id')
  final String? id;
  final String? login;
  final String? password;
  final String? name;
  final String? lastName;
  final String? patronymic;
  final UserRole? role;
  final Department? department;
  final String? branchId;
  final AvatarModel? avatar;
  final bool? isBlocked;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserModel({
    this.id,
    this.login,
    this.password,
    this.name,
    this.lastName,
    this.patronymic,
    this.role,
    this.department,
    this.branchId,
    this.avatar,
    this.createdAt,
    this.isBlocked,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}
