// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: json['_id'] as String?,
      login: json['login'] as String?,
      password: json['password'] as String?,
      name: json['name'] as String?,
      lastName: json['lastName'] as String?,
      patronymic: json['patronymic'] as String?,
      role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']),
      department: $enumDecodeNullable(_$DepartmentEnumMap, json['department']),
      branchId: json['branchId'] as String?,
      avatar: json['avatar'] == null
          ? null
          : AvatarModel.fromJson(json['avatar'] as Map<String, dynamic>),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      isBlocked: json['isBlocked'] as bool?,
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.login case final value?) 'login': value,
      if (instance.password case final value?) 'password': value,
      if (instance.name case final value?) 'name': value,
      if (instance.lastName case final value?) 'lastName': value,
      if (instance.patronymic case final value?) 'patronymic': value,
      if (_$UserRoleEnumMap[instance.role] case final value?) 'role': value,
      if (instance.department case final value?) 'department': value,
      if (instance.branchId case final value?) 'branchId': value,
      if (instance.avatar case final value?) 'avatar': value,
      if (instance.isBlocked case final value?) 'isBlocked': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

const _$UserRoleEnumMap = {
  UserRole.admin: 'admin',
  UserRole.headmanager: 'headmanager',
  UserRole.manager: 'manager',
  UserRole.worker: 'worker',
};

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};
