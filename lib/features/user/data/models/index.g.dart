// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateUserInputModel _$CreateUserInputModelFromJson(
        Map<String, dynamic> json) =>
    CreateUserInputModel(
      login: json['login'] as String?,
      password: json['password'] as String?,
      name: json['name'] as String?,
      lastName: json['lastName'] as String?,
      patronymic: json['patronymic'] as String?,
      role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']),
      department: $enumDecodeNullable(_$DepartmentEnumMap, json['department']),
      branchId: json['branchId'] as String?,
    );

Map<String, dynamic> _$CreateUserInputModelToJson(
        CreateUserInputModel instance) =>
    <String, dynamic>{
      if (instance.login case final value?) 'login': value,
      if (instance.password case final value?) 'password': value,
      if (instance.name case final value?) 'name': value,
      if (instance.lastName case final value?) 'lastName': value,
      if (instance.patronymic case final value?) 'patronymic': value,
      if (_$UserRoleEnumMap[instance.role] case final value?) 'role': value,
      if (instance.department case final value?) 'department': value,
      if (instance.branchId case final value?) 'branchId': value,
    };

const _$UserRoleEnumMap = {
  UserRole.admin: 'admin',
  UserRole.headmanager: 'headmanager',
  UserRole.manager: 'manager',
  UserRole.worker: 'worker',
};

const _$DepartmentEnumMap = {
  Department.ogk: 'ogk',
  Department.ogt: 'ogt',
  Department.osivk: 'osivk',
  Department.sh: 'sh',
  Department.otk: 'otk',
  Department.prd: 'prd',
};

MessageUserOutputModel _$MessageUserOutputModelFromJson(
        Map<String, dynamic> json) =>
    MessageUserOutputModel(
      message: json['message'] as String?,
    );

Map<String, dynamic> _$MessageUserOutputModelToJson(
        MessageUserOutputModel instance) =>
    <String, dynamic>{
      if (instance.message case final value?) 'message': value,
    };

SearchUserOutputModel _$SearchUserOutputModelFromJson(
        Map<String, dynamic> json) =>
    SearchUserOutputModel(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => UserModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SearchUserOutputModelToJson(
        SearchUserOutputModel instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };
