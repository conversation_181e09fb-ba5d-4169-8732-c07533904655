import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';

part 'index.g.dart';

@JsonSerializable(includeIfNull: false)
class CreateUserInputModel {
  final String? login;
  final String? password;
  final String? name;
  final String? lastName;
  final String? patronymic;
  final UserRole? role;
  final Department? department;
  final String? branchId;

  const CreateUserInputModel({
    this.login,
    this.password,
    this.name,
    this.lastName,
    this.patronymic,
    this.role,
    this.department,
    this.branchId,
  });

  factory CreateUserInputModel.fromJson(Map<String, dynamic> json) =>
      _$CreateUserInputModelFromJson(json);
  Map<String, dynamic> toJson() => _$CreateUserInputModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class MessageUserOutputModel {
  final String? message;

  const MessageUserOutputModel({
    this.message,
  });

  factory MessageUserOutputModel.fromJson(Map<String, dynamic> json) =>
      _$MessageUserOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$MessageUserOutputModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class SearchUserOutputModel {
  final List<UserModel>? items;
  final int? totalItems;

  const SearchUserOutputModel({
    this.items,
    this.totalItems,
  });

  factory SearchUserOutputModel.fromJson(Map<String, dynamic> json) =>
      _$SearchUserOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$SearchUserOutputModelToJson(this);
}
