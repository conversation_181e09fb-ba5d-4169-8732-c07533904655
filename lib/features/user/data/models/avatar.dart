import 'package:json_annotation/json_annotation.dart';

part 'avatar.g.dart';

@JsonSerializable(includeIfNull: false)
class AvatarModel {
  @JsonKey(name: '_id')
  final String? id;
  final String? url;
  final String? previewUrl;
  final int? width;
  final int? height;
  final String? userId;

  const AvatarModel({
    this.id,
    this.url,
    this.previewUrl,
    this.width,
    this.height,
    this.userId,
  });

  factory AvatarModel.fromJson(Map<String, dynamic> json) =>
      _$AvatarModelFromJson(json);
  Map<String, dynamic> toJson() => _$AvatarModelToJson(this);
}
