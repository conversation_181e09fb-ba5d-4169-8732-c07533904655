import 'package:flutter/material.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/presentation/user_card_body.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class UserCard extends StatelessWidget {
  const UserCard({
    super.key,
    this.minified = false,
    this.wrapped = true,
    this.selected = false,
    this.isLoading,
    this.user,
    this.onTap,
    this.onSecondaryTapDown,
  });

  final bool minified;
  final bool wrapped;
  final bool selected;
  final bool? isLoading;
  final UserModel? user;
  final Function()? onTap;
  final Function(TapDownDetails)? onSecondaryTapDown;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    if (wrapped) {
      return Opacity(
        opacity: user?.isBlocked == true ? 0.5 : 1,
        child: CustomCard(
          border: selected
              ? Border.all(
                  color: isDarkTheme
                      ? AppColors.darkSecondary
                      : AppColors.lightSecondary,
                  width: 2.0)
              : null,
          isLoading: isLoading,
          onTap: onTap,
          onSecondaryTapDown: onSecondaryTapDown,
          child: UserCardBody(
            minified: minified,
            user: user,
          ),
        ),
      );
    } else {
      return Opacity(
        opacity: user?.isBlocked == true ? 0.5 : 1,
        child: GhostButton(
          onTap: onTap,
          onSecondaryTapDown: onSecondaryTapDown,
          child: UserCardBody(
            minified: minified,
            user: user,
          ),
        ),
      );
    }
  }
}
