import 'package:flutter/material.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class UserCardBody extends StatelessWidget {
  const UserCardBody({
    super.key,
    this.minified = false,
    this.user,
  });

  final bool minified;
  final UserModel? user;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10000.0),
          child: Image.network(
            user?.avatar?.url ?? 'https://i.postimg.cc/CK4JDcb4/dude.png',
            width: minified ? 40.0 : 70.0,
            height: minified ? 40.0 : 70.0,
            errorBuilder: (context, error, stackTrace) {
              return Image.asset(
                'assets/images/default_avatar.png',
                width: minified ? 40.0 : 70.0,
                height: minified ? 40.0 : 70.0,
              );
            },
          ),
        ),
        const SizedBox(width: 10.0),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${user?.lastName ?? 'LASTNAME'} ${user?.name ?? 'NAME'} ${user?.patronymic ?? 'PATRONYMIC'}',
                style: (minified ? Fonts.labelSmall : Fonts.labelMedium)
                    .merge(TextStyle(height: 1.1)),
              ),
              if (user?.role != null)
                Text(
                  '${user?.role?.getName() ?? ''} ${user?.department?.getName() ?? ''}',
                  style: minified
                      ? Fonts.bodySmall.merge(
                          const TextStyle(color: AppColors.lightSecondary),
                        )
                      : Fonts.bodyLarge.merge(
                          const TextStyle(color: AppColors.lightSecondary),
                        ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
