import 'package:flutter/material.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';

class UsersCard extends StatefulWidget {
  const UsersCard({
    super.key,
    this.users = const [],
    this.title,
    this.onSecondaryTapDown,
    this.onTap,
    this.isLoading,
    this.minified = false,
  });

  final List<UserModel> users;
  final String? title;
  final Function(TapDownDetails)? onSecondaryTapDown;
  final Function()? onTap;
  final bool? isLoading;
  final bool minified;

  @override
  State<UsersCard> createState() => _UsersCardState();
}

class _UsersCardState extends State<UsersCard> {
  final ScrollController _scrollController = ScrollController();
  bool _showLeftGradient = false;
  bool _showRightGradient = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_updateGradientVisibility);
  }

  void _updateGradientVisibility() {
    setState(() {
      _showLeftGradient = _scrollController.offset > 0;
      _showRightGradient = _scrollController.position.pixels <
          _scrollController.position.maxScrollExtent;
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateGradientVisibility);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      padding: widget.minified ? EdgeInsets.all(0) : null,
      border: widget.minified
          ? Border.all(width: 0.0, color: Colors.transparent)
          : null,
      isLoading: widget.isLoading,
      onTap: widget.onTap,
      onSecondaryTapDown: widget.onSecondaryTapDown,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!widget.minified)
            Text(
              '${widget.title ?? 'Исполнители'} (${widget.users.length})',
              style: Fonts.labelLarge,
            ),
          if (!widget.minified) const SizedBox(height: 12.0),
          if (widget.users.isEmpty)
            Text(
              'Исполнители не назначены',
              style: Fonts.labelSmall.merge(
                TextStyle(
                  color: isDarkTheme
                      ? AppColors.darkDescription
                      : AppColors.lightDescription,
                ),
              ),
            ),
          if (widget.users.isNotEmpty)
            ShaderMask(
              shaderCallback: (Rect rect) {
                return LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    if (_showLeftGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                    isDarkTheme
                        ? AppColors.darkBackground.withValues(alpha: 0)
                        : AppColors.lightBackground.withValues(alpha: 0),
                    isDarkTheme
                        ? AppColors.darkBackground.withValues(alpha: 0)
                        : AppColors.lightBackground.withValues(alpha: 0),
                    if (_showRightGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                  ],
                  stops: const [0.0, 0.1, 0.9, 1.0],
                ).createShader(rect);
              },
              blendMode: BlendMode.dstOut,
              child: SizedBox(
                height: widget.minified ? 42.0 : 100.0,
                child: ListView.separated(
                  controller: _scrollController,
                  scrollDirection: Axis.horizontal,
                  // shrinkWrap: true,
                  itemBuilder: (context, index) {
                    final user = widget.users[index];

                    if (user.role == UserRole.headmanager) {
                      return Tooltip(
                        message: 'Руководитель проекта',
                        textStyle: Fonts.labelSmall,
                        child: UserCard(user: user, minified: widget.minified),
                      );
                    }
                    return UserCard(
                      user: user,
                      wrapped: false,
                      minified: widget.minified,
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const VerticalDivider(width: 24.0);
                  },
                  itemCount: widget.users.length,
                  // shrinkWrap: true,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
