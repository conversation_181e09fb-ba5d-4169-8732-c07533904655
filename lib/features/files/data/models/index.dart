import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/files/data/models/file.dart';

part 'index.g.dart';

@JsonSerializable(includeIfNull: false)
class SearchFilesOutputModel {
  final List<FileModel>? items;
  final int? totalItems;
  final String? message;

  const SearchFilesOutputModel({
    this.items,
    this.totalItems,
    this.message,
  });

  factory SearchFilesOutputModel.fromJson(Map<String, dynamic> json) =>
      _$SearchFilesOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$SearchFilesOutputModelToJson(this);
}
