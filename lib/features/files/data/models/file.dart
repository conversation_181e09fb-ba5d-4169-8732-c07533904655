import 'package:json_annotation/json_annotation.dart';

part 'file.g.dart';

@JsonSerializable(includeIfNull: false)
class FileModel {
  @Json<PERSON>ey(name: '_id')
  final String? id;
  final String? name;
  final String? extension;
  @Json<PERSON>ey(name: 'bluePrint')
  final bool? blueprint;
  final bool? isFolder;
  final String? parentId;
  final String? productId;
  final String? projectId;
  final String? path;
  final List<BlueprintVersion>? versions;
  final PreviewModel? preview;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? rootChildrenCount;
  final int? allChildrenCount;

  const FileModel({
    this.id,
    this.name,
    this.extension,
    this.blueprint,
    this.isFolder,
    this.parentId,
    this.productId,
    this.projectId,
    this.path,
    this.versions,
    this.preview,
    this.createdAt,
    this.updatedAt,
    this.rootChildrenCount,
    this.allChildrenCount,
  });

  factory FileModel.fromJson(Map<String, dynamic> json) =>
      _$FileModelFromJson(json);
  Map<String, dynamic> toJson() => _$FileModelToJson(this);
}

@JsonSerializable(includeIfNull: false)
class BlueprintVersion {
  final String? name;
  final int? version;
  final String? path;
  final String? extension;
  final DateTime? createdAt;

  const BlueprintVersion({
    this.name,
    this.version,
    this.path,
    this.extension,
    this.createdAt,
  });

  factory BlueprintVersion.fromJson(Map<String, dynamic> json) =>
      _$BlueprintVersionFromJson(json);
  Map<String, dynamic> toJson() => _$BlueprintVersionToJson(this);
}

@JsonSerializable(includeIfNull: false)
class PreviewModel {
  final String? url;
  final String? previewUrl;
  final int? width;
  final int? height;
  final String? mimeType;

  const PreviewModel({
    this.url,
    this.previewUrl,
    this.width,
    this.height,
    this.mimeType,
  });

  factory PreviewModel.fromJson(Map<String, dynamic> json) =>
      _$PreviewModelFromJson(json);
  Map<String, dynamic> toJson() => _$PreviewModelToJson(this);
}
