// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FileModel _$FileModelFromJson(Map<String, dynamic> json) => FileModel(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      extension: json['extension'] as String?,
      blueprint: json['bluePrint'] as bool?,
      isFolder: json['isFolder'] as bool?,
      parentId: json['parentId'] as String?,
      productId: json['productId'] as String?,
      projectId: json['projectId'] as String?,
      path: json['path'] as String?,
      versions: (json['versions'] as List<dynamic>?)
          ?.map((e) => BlueprintVersion.fromJson(e as Map<String, dynamic>))
          .toList(),
      preview: json['preview'] == null
          ? null
          : PreviewModel.fromJson(json['preview'] as Map<String, dynamic>),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      rootChildrenCount: (json['rootChildrenCount'] as num?)?.toInt(),
      allChildrenCount: (json['allChildrenCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$FileModelToJson(FileModel instance) => <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.extension case final value?) 'extension': value,
      if (instance.blueprint case final value?) 'bluePrint': value,
      if (instance.isFolder case final value?) 'isFolder': value,
      if (instance.parentId case final value?) 'parentId': value,
      if (instance.productId case final value?) 'productId': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.path case final value?) 'path': value,
      if (instance.versions case final value?) 'versions': value,
      if (instance.preview case final value?) 'preview': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.rootChildrenCount case final value?)
        'rootChildrenCount': value,
      if (instance.allChildrenCount case final value?)
        'allChildrenCount': value,
    };

BlueprintVersion _$BlueprintVersionFromJson(Map<String, dynamic> json) =>
    BlueprintVersion(
      name: json['name'] as String?,
      version: (json['version'] as num?)?.toInt(),
      path: json['path'] as String?,
      extension: json['extension'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$BlueprintVersionToJson(BlueprintVersion instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (instance.version case final value?) 'version': value,
      if (instance.path case final value?) 'path': value,
      if (instance.extension case final value?) 'extension': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
    };

PreviewModel _$PreviewModelFromJson(Map<String, dynamic> json) => PreviewModel(
      url: json['url'] as String?,
      previewUrl: json['previewUrl'] as String?,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      mimeType: json['mimeType'] as String?,
    );

Map<String, dynamic> _$PreviewModelToJson(PreviewModel instance) =>
    <String, dynamic>{
      if (instance.url case final value?) 'url': value,
      if (instance.previewUrl case final value?) 'previewUrl': value,
      if (instance.width case final value?) 'width': value,
      if (instance.height case final value?) 'height': value,
      if (instance.mimeType case final value?) 'mimeType': value,
    };
