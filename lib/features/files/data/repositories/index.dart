import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:sphere/features/files/data/models/file.dart';
import 'package:sphere/features/files/data/models/index.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class FilesRepository {
  static String _getBaseURL() {
    if (API.mode == 'development') {
      return API.developmentURL;
    } else {
      return API.productionURL;
    }
  }

  static String getPath(String path) {
    return '${_getBaseURL()}/$path';
  }

  static Future<Response<FileModel>?> uploadBlueprint({
    String? projectId,
    String? productId,
    required File file,
  }) async {
    final body = FormData.fromMap({
      'file': await MultipartFile.fromFile(
        file.path,
        filename: file.path.split('/').last,
        // contentType: DioMediaType('application', 'pdf'),
      ),
    });

    final args = [
      if (projectId != null) 'projectId=$projectId',
      if (productId != null) 'productId=$productId',
    ];

    final request = await API.request<FileModel>(
      url: '/documents/uploadBlueprint?${args.join('&')}',
      body: body,
      method: 'POST',
      fromJson: FileModel.fromJson,
      options: Options(contentType: 'multipart/form-data'),
    );

    return request;
  }

  static Future<Response<FileModel>?> uploadDocument({
    String? folderParentId,
    String? projectId,
    String? productId,
    String? documentId,
    DocumentDepartment? department,
    required File file,
  }) async {
    final body = FormData.fromMap({
      'file': await MultipartFile.fromFile(
        file.path,
        filename: file.path.split('/').last,
      ),
    });

    final args = [
      if (folderParentId != null) 'folderParentId=$folderParentId',
      if (projectId != null) 'projectId=$projectId',
      if (productId != null) 'productId=$productId',
      if (documentId != null) 'documentId=$documentId',
      if (department != null) 'department=${department.toJson()}'
    ];

    final request = await API.request<FileModel>(
      url: '/documents/uploadDocument?${args.join('&')}',
      body: body,
      method: 'POST',
      fromJson: FileModel.fromJson,
    );

    return request;
  }

  static Future<Response<FileModel>?> createFolder({
    String? projectId,
    String? productId,
    String? folderParentId,
    DocumentDepartment? documentDepartment,
    required String name,
  }) async {
    final body = jsonEncode({
      if (projectId != null) 'projectId': projectId,
      if (productId != null) 'productId': productId,
      if (folderParentId != null) 'folderParentId': folderParentId,
      if (documentDepartment != null) 'documentDepartment': documentDepartment,
      'name': name,
    });

    final request = await API.request<FileModel>(
      url: '/documents/createFolder',
      body: body,
      method: 'POST',
      fromJson: FileModel.fromJson,
    );

    return request;
  }

  static Future<Response<void>?> deleteDocument(
    String documentId,
  ) async {
    final body = jsonEncode({
      'forceChildDocument': true,
    });

    final request = await API.request<void>(
      url: '/documents/deleteDocument?documentId=$documentId',
      body: body,
      method: 'POST',
      // fromJson: SearchFilesOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<SearchFilesOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<SearchFilesOutputModel>(
      url: '/documents/search',
      body: body,
      method: 'POST',
      fromJson: SearchFilesOutputModel.fromJson,
    );

    return request;
  }
}
