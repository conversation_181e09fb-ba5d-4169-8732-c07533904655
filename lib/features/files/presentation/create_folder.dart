import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class CreateFolderPopup extends StatelessWidget {
  const CreateFolderPopup({super.key, this.onSave});

  final Function(String name)? onSave;

  @override
  Widget build(BuildContext context) {
    final nameController = TextEditingController();
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return AlertDialog(
      backgroundColor:
          isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground,
      title: const Text(
        'Создание папки',
        style: Fonts.titleSmall,
      ),
      content: TextField(
        controller: nameController,
        decoration: const InputDecoration(
          hintText: 'Название папки',
        ),
        style: Fonts.labelSmall,
      ),
      actions: [
        TextButton(
          onPressed: () {
            context.router.popForced();
          },
          child: const Text(
            'Отмена',
            style: Fonts.labelSmall,
          ),
        ),
        TextButton(
          onPressed: () {
            if (nameController.text.isEmpty) return;
            onSave?.call(nameController.text);
            context.router.popForced();
          },
          child: const Text(
            'Сохранить',
            style: Fonts.labelSmall,
          ),
        ),
      ],
    );
  }
}
