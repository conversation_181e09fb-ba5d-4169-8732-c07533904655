import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/core/helpers/pick_file.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/files/data/models/file.dart';
import 'package:sphere/features/files/data/repositories/index.dart';
import 'package:sphere/features/files/presentation/create_folder.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/detailed.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

@RoutePage()
class FilesScreen extends StatefulWidget {
  FilesScreen({
    super.key,
    @PathParam('id') this.id, // project | product | folder
    @QueryParam('idType') String? idType,
    @QueryParam('isRoot') this.isRoot,
    @QueryParam('department') String? department,
    @QueryParam('projectName') this.projectName,
  })  : idType = idType != null
            ? FileScreenTypeId.fromString(idType)
            : FileScreenTypeId.fromProject,
        department = department != null
            ? DocumentDepartment.fromString(department)
            : null;

  final String? id;
  final FileScreenTypeId idType;
  final bool? isRoot;
  final DocumentDepartment? department;
  final String? projectName;

  @override
  State<FilesScreen> createState() => _FilesScreenState();
}

class _FilesScreenState extends State<FilesScreen> {
  bool _isLoading = false;
  List<FileModel> _files = [];

  Future<void> _getDocuments(
    String? id,
    bool? root, {
    String? query,
  }) async {
    SearchFiltersModel filters = SearchFiltersModel(projectId: id);
    switch (widget.idType) {
      case FileScreenTypeId.fromFolder:
        filters = SearchFiltersModel(
          query: query,
          parentFolderId: id,
          root: root,
          documentDepartment: widget.department,
        );
      case FileScreenTypeId.fromProject:
        filters = SearchFiltersModel(
          query: query,
          projectId: id,
          root: root,
          documentDepartment: widget.department,
        );
      case FileScreenTypeId.fromProduct:
        filters = SearchFiltersModel(
          query: query,
          productId: id,
          root: root,
          documentDepartment: widget.department,
        );
    }

    setState(() {
      _isLoading = true;
    });

    final result = await FilesRepository.search(SearchModel(filters: filters));

    setState(() {
      _isLoading = false;
      if (result?.data?.items != null) {
        _files = result!.data!.items!;
      }
    });
  }

  void _onTapFile(FileModel file) async {
    if (file.isFolder == true) {
      await context.router.push(
        FilesRoute(
          key: UniqueKey(),
          id: file.id,
          idType: FileScreenTypeId.fromFolder.toJson(),
          // isRoot: null,
          department: widget.department?.toJson(),
        ),
      );
    } else {
      if (file.path == null) {
        return;
      }
      // final path = FilesRepository.getPath(
      //   file.path!,
      // );
      // launchUrl(Uri.parse(path));
      context.router.push(PdfViewerRoute(pdfPath: file.path!));
    }
  }

  Future<void> _deleteFile(String id) async {
    CustomDropdownMenu.instance.hide();
    setState(() {
      _isLoading = true;
    });

    final result = await FilesRepository.deleteDocument(id);

    setState(() {
      _isLoading = false;
      if ((result?.statusCode ?? 500) <= 300) {
        _getDocuments(widget.id, widget.isRoot);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
          text: 'Ошибка при удалении! ${result?.statusMessage}',
          type: SnackBarType.error,
        ).toSnackBar(context));
      }
    });
  }

  Future<void> _uploadFile() async {
    CustomDropdownMenu.instance.hide();
    final file = await pickFile();
    if (file == null) return;

    setState(() {
      _isLoading = true;
    });

    final result = await FilesRepository.uploadDocument(
      folderParentId:
          widget.idType == FileScreenTypeId.fromFolder ? widget.id : null,
      projectId:
          widget.idType == FileScreenTypeId.fromProject ? widget.id : null,
      productId:
          widget.idType == FileScreenTypeId.fromProduct ? widget.id : null,
      department: widget.department,
      file: file,
    );

    setState(() {
      _isLoading = false;
      if ((result?.statusCode ?? 500) <= 300) {
        _getDocuments(widget.id, widget.isRoot);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
          text: 'Ошибка при добавлении файла! ${result?.statusMessage}',
          type: SnackBarType.error,
        ).toSnackBar(context));
      }
    });
  }

  Future<void> _createFolder(String name) async {
    setState(() {
      _isLoading = true;
    });

    final result = await FilesRepository.createFolder(
      folderParentId:
          widget.idType == FileScreenTypeId.fromFolder ? widget.id : null,
      projectId:
          widget.idType == FileScreenTypeId.fromProject ? widget.id : null,
      productId:
          widget.idType == FileScreenTypeId.fromProduct ? widget.id : null,
      name: name,
      documentDepartment: widget.department,
    );

    setState(() {
      _isLoading = false;
      if ((result?.statusCode ?? 500) <= 300) {
        _getDocuments(widget.id, widget.isRoot);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
          text: 'Ошибка при создании папки! ${result?.statusMessage}',
          type: SnackBarType.error,
        ).toSnackBar(context));
      }
    });
  }

  void _createFolderPopup() {
    CustomDropdownMenu.instance.hide();
    showDialog(
      context: context,
      builder: (context) {
        return CreateFolderPopup(onSave: _createFolder);
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _getDocuments(widget.id, widget.isRoot);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final columns = MediaQuery.of(context).size.width ~/ 300;
    // final isMobile = MediaQuery.of(context).size.width < 600;

    return Wrapper(
      appBar: CustomAppBar(
        title: widget.projectName,
        description: 'Элементов: ${_files.length}',
        isLoading: _isLoading,
        height: 48.0,
        rightPart: CustomAppBarFeatures.getPopupMenu(
          children: [
            if (widget.department != null)
              CustomDropdownMenuItem(
                icon: Assets.icons.createFolder,
                text: 'Создать папку',
                description: 'Создаст папку выбранному отделу',
                onTap: _createFolderPopup,
              ),
            CustomDropdownMenuItem(
              icon: Assets.icons.uploadFile,
              text: 'Загрузить файл',
              description:
                  'Создаст файл выбранному отделу. Это не заменит чертёж.',
              onTap: _uploadFile,
            ),
          ],
          context: context,
        ),
      ),
      body: Column(
        children: [
          // Фиксированный поиск
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: TextField(
              onChanged: (value) {
                if (value.isEmpty) {
                  _getDocuments(widget.id, widget.isRoot);
                  return;
                }
                _getDocuments(widget.id, null, query: value);
              },
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                hintText: 'Поиск',
                suffixIcon: Align(
                  widthFactor: 1.0,
                  alignment: Alignment.center,
                  child: SVG(
                    Assets.icons.search,
                    color: AppColors.medium,
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 12.0),
            child: Row(
              children: [
                const Text('Отдел:', style: Fonts.labelMedium),
                const SizedBox(width: 12.0),
                Expanded(
                  child: Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    clipBehavior: Clip.none,
                    children: List.generate(
                      DocumentDepartment.values.length,
                      (index) {
                        final department = DocumentDepartment.values[index];
                        final departmentName = department.getName();
                        final selected = widget.department == department;
                        return CustomChip(
                          selected: selected,
                          textStyle: Fonts.labelMedium,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12.0,
                            vertical: 4.0,
                          ),
                          onTap: () {
                            context.router.replace(FilesRoute(
                              key: UniqueKey(),
                              id: widget.id,
                              department: selected ? null : department.toJson(),
                              idType: widget.idType.toJson(),
                              isRoot: widget.isRoot,
                              projectName: widget.projectName,
                            ));
                          },
                          text: departmentName,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Прокручиваемая часть
          Flexible(
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12.0,
                    vertical: 12.0,
                  ),
                  sliver: SliverGrid(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: columns > 0 ? columns : 1,
                      mainAxisSpacing: 12.0,
                      crossAxisSpacing: 12.0,
                      childAspectRatio: 5 / 3,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final file = _files[index];
                        return DetailedCard(
                          onTap: () => _onTapFile(file),
                          onSecondaryTapDown: (details) {
                            CustomDropdownMenu.instance.show(
                                context: context,
                                position: details.globalPosition,
                                items: [
                                  CustomDropdownMenuItem(
                                    onTap: () {
                                      CustomDropdownMenu.instance.hide();
                                      showDialog(
                                        context: context,
                                        builder: (context) {
                                          return AlertDialog(
                                            title: Text(
                                              'Удаление ${file.isFolder == true ? 'папки' : 'файла'}',
                                              style: Fonts.titleSmall,
                                            ),
                                            content: file.isFolder == true
                                                ? Text(
                                                    'Вы уверены удалить папку "${file.name}", а также всё, что в ней содержится?',
                                                    style: Fonts.bodyMedium,
                                                  )
                                                : null,
                                            actionsAlignment:
                                                MainAxisAlignment.spaceAround,
                                            actions: [
                                              TextButton(
                                                onPressed: () {
                                                  context.router.popForced();
                                                },
                                                child: const Text(
                                                  'Отмена',
                                                  style: Fonts.labelMedium,
                                                ),
                                              ),
                                              TextButton(
                                                // color: isDarkTheme
                                                //     ? AppColors.darkError
                                                //     : AppColors.lightError,
                                                onPressed: () {
                                                  if (file.id == null) return;
                                                  context.router.popForced();
                                                  _deleteFile(file.id!);
                                                },
                                                child: Text(
                                                  'Да. удалить',
                                                  style:
                                                      Fonts.labelMedium.merge(
                                                    TextStyle(
                                                      color: isDarkTheme
                                                          ? AppColors.darkError
                                                          : AppColors
                                                              .lightError,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                    icon: Assets.icons.delete,
                                    text: 'Удалить',
                                  ),
                                ]);
                          },
                          title: file.name?.split('.').first,
                          description: file.updatedAt != null
                              ? 'Обновлено: ${getDateString(file.updatedAt!)}'
                              : null,
                          count: file.isFolder == true
                              ? file.allChildrenCount
                              : null,
                          icon: Assets.icons.copy,
                        );
                      },
                      childCount: _files.length,
                    ),
                  ),
                ),
                const SliverToBoxAdapter(child: SizedBox(height: 128.0)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

enum FileScreenTypeId {
  fromProject,
  fromProduct,
  fromFolder;

  static FileScreenTypeId fromString(String value) {
    return FileScreenTypeId.values.firstWhere(
      (e) => e.toString().split('.').last == value,
      orElse: () => FileScreenTypeId.fromProject,
    );
  }

  String toJson() => toString().split('.').last;
}
