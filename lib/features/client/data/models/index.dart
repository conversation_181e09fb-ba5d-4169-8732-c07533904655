import 'package:json_annotation/json_annotation.dart';
import 'package:sphere/features/client/data/models/client.dart';

part 'index.g.dart';

@JsonSerializable(includeIfNull: false)
class ClientSearchOutputModel {
  final List<ClientModel>? items;
  final int? totalItems;
  final String? message;

  const ClientSearchOutputModel({
    this.items,
    this.totalItems,
    this.message,
  });

  factory ClientSearchOutputModel.fromJson(Map<String, dynamic> json) =>
      _$ClientSearchOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$ClientSearchOutputModelToJson(this);
}
