// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'client.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ClientModel _$ClientModelFromJson(Map<String, dynamic> json) => ClientModel(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      clientType: $enumDecodeNullable(_$ClientTypeEnumMap, json['clientType']),
    );

Map<String, dynamic> _$ClientModelToJson(ClientModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (_$ClientTypeEnumMap[instance.clientType] case final value?)
        'clientType': value,
    };

const _$ClientTypeEnumMap = {
  ClientType.client: 'client',
  ClientType.supplier: 'supplier',
};
