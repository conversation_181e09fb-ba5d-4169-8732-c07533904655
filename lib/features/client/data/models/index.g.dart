// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ClientSearchOutputModel _$ClientSearchOutputModelFromJson(
        Map<String, dynamic> json) =>
    ClientSearchOutputModel(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ClientModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$ClientSearchOutputModelToJson(
        ClientSearchOutputModel instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
      if (instance.message case final value?) 'message': value,
    };
