import 'package:json_annotation/json_annotation.dart';

part 'client.g.dart';

@JsonSerializable(includeIfNull: false)
class ClientModel {
  @JsonKey(name: '_id')
  final String? id;
  final String? name;
  final ClientType? clientType;

  const ClientModel({
    this.id,
    this.name,
    this.clientType,
  });

  factory ClientModel.fromJson(Map<String, dynamic> json) =>
      _$ClientModelFromJson(json);
  Map<String, dynamic> toJson() => _$ClientModelToJson(this);
}

enum ClientType { client, supplier }
