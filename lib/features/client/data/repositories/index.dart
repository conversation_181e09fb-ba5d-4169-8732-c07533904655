import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sphere/features/client/data/models/index.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/data/models/search.dart';

class ClientRepository {
  static Future<Response<void>?> create(
    String name,
  ) async {
    final body = jsonEncode({'name': name});

    final request = await API.request<void>(
      url: '/clients/create',
      body: body,
      method: 'POST',
      // fromJson: ClientSearchOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<ClientSearchOutputModel>?> search(
    SearchModel data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ClientSearchOutputModel>(
      url: '/clients/search',
      body: body,
      method: 'POST',
      fromJson: ClientSearchOutputModel.fromJson,
    );

    return request;
  }
}
