import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/user/data/models/user.dart';

part 'bloc.freezed.dart';
part 'events.dart';
part 'state.dart';

class BlocAuth extends Bloc<BlocAuthEvents, BlocAuthState> {
  BlocAuth() : super(const BlocAuthState()) {
    // on<SetAppBarConfig>((event, emit) {
    //   emit(state.copyWith(config: event.config));
    // });
    on<SetUser>((event, emit) {
      emit(state.copyWith(user: event.user));
    });
  }
}
