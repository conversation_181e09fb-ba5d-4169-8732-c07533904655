// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BlocAuthState {
  UserModel get user => throw _privateConstructorUsedError;

  /// Create a copy of BlocAuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BlocAuthStateCopyWith<BlocAuthState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocAuthStateCopyWith<$Res> {
  factory $BlocAuthStateCopyWith(
          BlocAuthState value, $Res Function(BlocAuthState) then) =
      _$BlocAuthStateCopyWithImpl<$Res, BlocAuthState>;
  @useResult
  $Res call({UserModel user});
}

/// @nodoc
class _$BlocAuthStateCopyWithImpl<$Res, $Val extends BlocAuthState>
    implements $BlocAuthStateCopyWith<$Res> {
  _$BlocAuthStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocAuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_value.copyWith(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserModel,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlocAuthStateImplCopyWith<$Res>
    implements $BlocAuthStateCopyWith<$Res> {
  factory _$$BlocAuthStateImplCopyWith(
          _$BlocAuthStateImpl value, $Res Function(_$BlocAuthStateImpl) then) =
      __$$BlocAuthStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({UserModel user});
}

/// @nodoc
class __$$BlocAuthStateImplCopyWithImpl<$Res>
    extends _$BlocAuthStateCopyWithImpl<$Res, _$BlocAuthStateImpl>
    implements _$$BlocAuthStateImplCopyWith<$Res> {
  __$$BlocAuthStateImplCopyWithImpl(
      _$BlocAuthStateImpl _value, $Res Function(_$BlocAuthStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocAuthState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$BlocAuthStateImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserModel,
    ));
  }
}

/// @nodoc

class _$BlocAuthStateImpl extends _BlocAuthState {
  const _$BlocAuthStateImpl({this.user = const UserModel()}) : super._();

  @override
  @JsonKey()
  final UserModel user;

  @override
  String toString() {
    return 'BlocAuthState(user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlocAuthStateImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of BlocAuthState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BlocAuthStateImplCopyWith<_$BlocAuthStateImpl> get copyWith =>
      __$$BlocAuthStateImplCopyWithImpl<_$BlocAuthStateImpl>(this, _$identity);
}

abstract class _BlocAuthState extends BlocAuthState {
  const factory _BlocAuthState({final UserModel user}) = _$BlocAuthStateImpl;
  const _BlocAuthState._() : super._();

  @override
  UserModel get user;

  /// Create a copy of BlocAuthState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BlocAuthStateImplCopyWith<_$BlocAuthStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
