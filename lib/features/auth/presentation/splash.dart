import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/auth/data/repositories/index.dart';
import 'package:sphere/features/auth/presentation/bloc/bloc.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';

@RoutePage()
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final _storage = const FlutterSecureStorage();

  @override
  void initState() {
    super.initState();
    // Запускаем таймер для перехода через 1 секунды
    Timer(const Duration(seconds: 1), () {
      _enter();
    });
  }

  // Декодируем JWT токен чтобы получить userId
  String? _getUserIdFromToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final payloadMap = json.decode(decoded) as Map<String, dynamic>;

      return payloadMap['sub']?.toString() ??
          payloadMap['userId']?.toString() ??
          payloadMap['user_id']?.toString();
    } catch (e) {
      print('Ошибка декодирования токена: $e');
      return null;
    }
  }

  // обработка входа в приложение, если есть refresh token
  Future<void> _enter() async {
    try {
      final refreshToken = await _storage.read(key: 'refreshToken') ?? '';
      final response =
          await AuthRepository.refreshToken(refreshToken: refreshToken);

      if (response?.data?.accessToken != null) {
        final accessToken = response!.data!.accessToken!;
        final newRefreshToken = response.data!.refreshToken!;

        await _storage.write(key: 'accessToken', value: accessToken);
        await _storage.write(key: 'refreshToken', value: newRefreshToken);

        // Получаем userId из токена
        final userId = _getUserIdFromToken(accessToken) ??
            _getUserIdFromToken(newRefreshToken);

        if (userId != null &&
            !kIsWeb &&
            !Platform.isWindows &&
            !Platform.isMacOS) {
          await OneSignal.login(userId);
          print('OneSignal user ID установлен: $userId');
        }

        if (userId != null) {
          final user = await UserRepository.getById(userId);
          final preparedUser = user?.data ?? UserModel();

          context.read<BlocAuth>().add(SetUser(preparedUser));
        }

        context.router.replaceNamed('/home');
      } else {
        context.router.replaceNamed('/auth');
      }
    } catch (e) {
      print('Ошибка входа: $e');
      context.router.replaceNamed('/auth');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Wrapper(
      body: Center(
        child: Image.asset(
          Assets.images.startPng.path,
          width: 100,
          height: 100,
        ).animate(
          autoPlay: true,
          onPlay: (controller) => controller.repeat(reverse: true),
          effects: [
            const ScaleEffect(
              begin: Offset(0.9, 0.9),
              end: Offset(1.0, 1.0),
            ),
          ],
        ).fadeIn(begin: .5, duration: .4.seconds, curve: Curves.decelerate),
      ),
    );
  }
}
