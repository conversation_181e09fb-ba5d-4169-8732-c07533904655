import 'dart:convert';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/auth/data/repositories/index.dart';
import 'package:sphere/features/auth/presentation/bloc/bloc.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/shared/data/datasources/api.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:sphere/shared/widgets/utility/description.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

@RoutePage()
class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _passwordIsVisible = false;

  // Декодируем JWT токен чтобы получить userId
  String? _getUserIdFromToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final payloadMap = json.decode(decoded) as Map<String, dynamic>;

      return payloadMap['sub']?.toString() ??
          payloadMap['userId']?.toString() ??
          payloadMap['user_id']?.toString();
    } catch (e) {
      print('Ошибка декодирования токена: $e');
      return null;
    }
  }

  void _login() async {
    if (_loginController.text.isEmpty || _passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const CustomSnackbar(
          text: 'Вы должны ввести логин и пароль!',
          type: SnackBarType.error,
        ).toSnackBar(context),
      );
      return;
    }

    final response = await AuthRepository.auth(
      login: _loginController.text,
      password: _passwordController.text,
      apiKey: API.secretAPI,
    );

    if (response?.data?.accessToken != null) {
      final accessToken = response!.data!.accessToken!;
      final newRefreshToken = response.data!.refreshToken!;

      const prefs = FlutterSecureStorage();
      await prefs.write(
        key: 'accessToken',
        value: accessToken,
      );
      await prefs.write(
        key: 'refreshToken',
        value: newRefreshToken,
      );
      // Получаем userId из токена
      final userId = _getUserIdFromToken(accessToken) ??
          _getUserIdFromToken(newRefreshToken);

      if (userId != null &&
          !kIsWeb &&
          !Platform.isWindows &&
          !Platform.isMacOS) {
        await OneSignal.login(userId);
        print('OneSignal user ID установлен: $userId');
      }

      if (userId != null) {
        final user = await UserRepository.getById(userId);
        final preparedUser = user?.data ?? UserModel();

        context.read<BlocAuth>().add(SetUser(preparedUser));
      }

      context.router.replaceNamed('/home');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          type: SnackBarType.error,
          text:
              'Что-то пошло не так: STATUS_CODE: ${response?.statusCode.toString()}, STATUS_MESSAGE: ${response?.statusMessage}',
        ).toSnackBar(context),
      );
    }
  }

  void _togglePasswordVisible() {
    setState(() {
      _passwordIsVisible = !_passwordIsVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Wrapper(
      appBar: CustomAppBar(),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 500.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        Assets.images.startPng.path,
                        width: 50,
                        height: 50,
                      ),
                      const SizedBox(height: 12.0),
                      const Text(
                        'АО Сфера',
                        style: Fonts.displayMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 48.0),
                      TextField(
                        controller: _loginController,
                        decoration: const InputDecoration(hintText: 'Логин'),
                      ),
                      const SizedBox(height: 16.0),
                      TextField(
                        controller: _passwordController,
                        obscureText: !_passwordIsVisible,
                        decoration: InputDecoration(
                          hintText: 'Пароль',
                          suffixIcon: IconButton(
                            onPressed: _togglePasswordVisible,
                            icon: SVG(
                              _passwordIsVisible
                                  ? Assets.icons.visibilityOff
                                  : Assets.icons.visibility,
                              color: isDarkTheme
                                  ? AppColors.darkPrimary
                                  : AppColors.lightPrimary,
                            ),
                          ),
                        ),
                        keyboardType: TextInputType.visiblePassword,
                      ),
                      const SizedBox(height: 48.0),
                      Row(
                        children: [
                          Expanded(
                            child: CustomElevatedButton(
                              type: CustomElevatedButtonTypes.accent,
                              onPressed: _login,
                              text: 'Войти',
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16.0),
                      const DescriptionText(
                        text: 'Не можете войти? Обратитесь к администратору.',
                        textAlign: TextAlign.center,
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
