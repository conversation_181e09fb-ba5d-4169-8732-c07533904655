import 'package:json_annotation/json_annotation.dart';

part 'auth_output.g.dart';

@JsonSerializable()
class AuthOutputModel {
  final String? accessToken;
  final String? refreshToken;

  AuthOutputModel({
    this.accessToken,
    this.refreshToken,
  });

  factory AuthOutputModel.fromJson(Map<String, dynamic> json) =>
      _$AuthOutputModelFromJson(json);
  Map<String, dynamic> toJson() => _$AuthOutputModelToJson(this);
}
