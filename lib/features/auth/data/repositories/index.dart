import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sphere/features/auth/data/models/auth_output.dart';
import 'package:sphere/shared/data/datasources/api.dart';

class AuthRepository {
  static Future<Response<AuthOutputModel>?> auth({
    required String login,
    required String password,
    required String apiKey,
  }) async {
    final body = {
      'login': login,
      'password': password,
      'apiKey': apiKey,
    };

    final request = await API.request<AuthOutputModel>(
      url: '/auth/login',
      body: jsonEncode(body),
      method: 'POST',
      fromJson: AuthOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<AuthOutputModel>?> refreshToken({
    required String refreshToken,
  }) async {
    final body = {
      'refreshToken': refreshToken,
    };

    final request = await API.request<AuthOutputModel>(
      url: '/auth/refresh-token',
      body: json<PERSON>nco<PERSON>(body),
      method: 'POST',
      fromJson: AuthOutputModel.fromJson,
    );

    return request;
  }

  static Future<Response<void>?> logout({
    required String refreshToken,
  }) async {
    final body = {
      'refreshToken': refreshToken,
    };

    final request = await API.request<void>(
      url: '/auth/logout',
      body: jsonEncode(body),
      method: 'POST',
      // fromJson: void.fromJson,
    );

    return request;
  }
}
