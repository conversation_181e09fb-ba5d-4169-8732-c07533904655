import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/product/data/models/index.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/product/presentation/product_item/index.dart';
import 'package:sphere/features/structure/presentation/bloc/bloc.dart';
import 'package:sphere/features/structure/presentation/create_product.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class StructureScreen extends StatefulWidget {
  const StructureScreen({
    super.key,
    @PathParam('id') this.id,
    this.isProduct,
    this.projectId,
  });

  final String? id;
  final bool? isProduct;
  final String? projectId;

  @override
  State<StructureScreen> createState() => _StructureScreenState();
}

class _StructureScreenState extends State<StructureScreen>
    with TickerProviderStateMixin {
  SearchProductOutputModel data = SearchProductOutputModel();
  List<ProductModel> products = [];
  bool _isLoading = false;

  void _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final result = await ProductRepository.search(SearchModel(
      filters: SearchFiltersModel(
        projectId: widget.isProduct == true ? null : widget.id,
        parentProductId: widget.isProduct == true ? widget.id : null,
        root: widget.isProduct == true ? false : true,
      ),
    ));
    // await Future.delayed(const Duration(milliseconds: 20000));

    setState(() {
      data = result?.data ?? SearchProductOutputModel();
      if (data.items != null) {
        products = data.items!;
      }
      _isLoading = false;
    });
  }

  void _setProductData(String id, ProductModel? newData) {
    final productIndex = products.indexWhere((product) => product.id == id);
    if (productIndex < 0 || newData == null) return;
    setState(() {
      products[productIndex] = newData;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadData();
    context.read<BlocStructure>().add(ClearSelectionsInStructure());
  }

  @override
  Widget build(BuildContext context) {
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    // final isMobile = MediaQuery.of(context).size.width < 600;

    return BlocBuilder<BlocStructure, BlocStructureState>(
        builder: (context, state) {
      return Wrapper(
        appBar: CustomAppBar(
          description:
              'Структура ${widget.isProduct == true ? 'сборочной единицы' : 'проекта'}',
          // TODO: add name if this is a product
          title: data.project?.name ?? 'Title',
          height: 48.0,
          rightPart: Row(
            children: [
              if (state.selectedProgressTasks.isNotEmpty)
                TextButton(
                  child: Row(
                    children: [
                      SVG(Assets.icons.close, width: 18.0),
                      SizedBox(width: 8.0),
                      Text(
                        'Снять выделение задач (${state.selectedProgressTasks.length})',
                        style: Fonts.labelSmall.merge(
                          TextStyle(fontSize: 14.0),
                        ),
                      ),
                    ],
                  ),
                  onPressed: () {
                    CustomDropdownMenu.instance.hide();
                    context
                        .read<BlocStructure>()
                        .add(ClearProgressTasksSelectionsInStructure());
                  },
                ),
              if (state.selecting && state.selected.isNotEmpty)
                TextButton(
                  child: Row(
                    children: [
                      SVG(Assets.icons.close, width: 18.0),
                      SizedBox(width: 8.0),
                      Text(
                        'Снять выделение (${state.selected.length})',
                        style: Fonts.labelSmall.merge(
                          TextStyle(fontSize: 14.0),
                        ),
                      ),
                    ],
                  ),
                  onPressed: () {
                    CustomDropdownMenu.instance.hide();
                    context
                        .read<BlocStructure>()
                        .add(ClearSelectionsInStructure());
                  },
                ),
              if (state.copied.isNotEmpty)
                TextButton(
                  child: Row(
                    children: [
                      SVG(Assets.icons.close, width: 18.0),
                      SizedBox(width: 8.0),
                      Text(
                        'Отмена копирования (${state.copied.length})',
                        style: Fonts.labelSmall.merge(
                          TextStyle(fontSize: 14.0),
                        ),
                      ),
                    ],
                  ),
                  onPressed: () {
                    CustomDropdownMenu.instance.hide();
                    context.read<BlocStructure>().add(SetCopiedInStructure([]));
                  },
                ),
              SizedBox(width: 12.0),
              CustomAppBarFeatures.getPopupMenu(
                context: context,
                children: [
                  CustomDropdownMenuItem(
                    icon: Assets.icons.add,
                    text: 'Добавить...',
                    description: 'Сборочную единицу, Деталь и т.п.',
                    onTap: () {
                      CustomDropdownMenu.instance.hide();
                      CustomDrawer.instance.show(
                        context: context,
                        vsync: this,
                        // TODO: this is kludge
                        child: CreateProductDrawer(
                          projectId:
                              widget.isProduct == true ? null : widget.id,
                          parentId: widget.isProduct == true ? widget.id : null,
                          onSuccess: _loadData,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
        body: ListView.separated(
          itemCount: products.length,
          itemBuilder: (context, index) {
            return Column(children: [
              ProductItem(
                copied: state.copied,
                selectedProducts: state.selected,
                selecting: state.selecting,
                onSelect: (product) {
                  context.read<BlocStructure>().add(ToggleInStructure(product));
                },
                onTap: () {
                  context.router.pushNamed(
                    '/project/${products[index].projectId}/product/${products[index].id}',
                  );
                },
                setProductData: _setProductData,
                projectId: products[index].projectId,
                isLoading: _isLoading,
                data: products.isNotEmpty ? products[index] : ProductModel(),
                refresher: _loadData,
              ),
              if (products.length - 1 == index)
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.66,
                ),
            ]);
          },
          separatorBuilder: (BuildContext context, int index) {
            return const Divider(height: 1.0);
          },
        ),
      );
    });
  }
}
