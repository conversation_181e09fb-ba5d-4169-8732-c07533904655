import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/repositories/index.dart';
import 'package:sphere/features/product/data/models/index.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/dropdown_button.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class CreateProductDrawer extends StatefulWidget {
  const CreateProductDrawer({
    super.key,
    this.projectId,
    this.parentId,
    this.onSuccess,
  });

  final String? projectId;
  final String? parentId;
  final Function()? onSuccess;

  @override
  State<CreateProductDrawer> createState() => _CreateProductDrawerState();
}

class _CreateProductDrawerState extends State<CreateProductDrawer> {
  final ScrollController _scrollController = ScrollController();
  bool _showTopGradient = false;
  bool _showBottomGradient = true;
  final _nameController = TextEditingController(text: '');
  // final _designationController = TextEditingController(text: '');
  final _descriptionController = TextEditingController(text: '');
  final _templateNumberController = TextEditingController(text: '');
  final _quantityController = TextEditingController(text: '');
  ProductType _selectedType = ProductType.assembly;
  bool _isLoading = false;
  bool _dropdownIsOpened = false;
  bool _unitTypeDropdownIsOpened = false;
  Future<void> Function()? toggleDropdown;
  Future<void> Function()? toggleUnitTypeDropdown;
  UnitType _selectedUnitType = UnitType.kg;
  final _materialQuantityController = TextEditingController();
  NomenclatureModel? _selectedNomenclature;
  List<NomenclatureModel> _materialsFromSearch = [];

  bool _getReducedByType() {
    return _selectedType == ProductType.standard ||
        _selectedType == ProductType.materials;
  }

  Future<void> _searchMaterial() async {
    if (_nameController.text.characters.isEmpty) {
      _materialsFromSearch.clear();
      return;
    }
    setState(() {
      _isLoading = true;
    });

    NomenclatureType? getMaterialType() {
      if (_selectedType == ProductType.standard) {
        return NomenclatureType.standard;
      } else if (_selectedType == ProductType.other) {
        return NomenclatureType.other;
      } else if (_selectedType == ProductType.materials) {
        return NomenclatureType.materials;
      }
      return null;
    }

    print(getMaterialType());

    final result = await NomenclaturesRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _nameController.text,
        materialType: getMaterialType(),
      ),
    ));

    setState(() {
      if (result?.data?.items != null) {
        _materialsFromSearch = result!.data!.items!;
      }
      _isLoading = false;
    });
  }

  void _createProduct() async {
    setState(() {
      _isLoading = true;
    });
    final reducedByType = _getReducedByType();
    final quantity =
        double.tryParse(_quantityController.text.replaceAll(r',', '.'));
    final materialQuantity =
        double.tryParse(_materialQuantityController.text.replaceAll(r',', '.'));

    await ProductRepository.create(CreateProductInput(
      name: _nameController.text,
      number: !reducedByType ? _templateNumberController.text : null,
      description: _descriptionController.text,
      type: _selectedType,
      projectId: widget.projectId,
      parentId: widget.parentId,
      // quantity: quantity,
      // designation: _designationController.text,
      parameters: ParametersModel(
        unitType:
            _selectedType == ProductType.materials ? _selectedUnitType : null,
        quantity: _selectedType == ProductType.materials
            ? materialQuantity
            : quantity,
        // materialQuantity: _selectedType == ProductType.materials
        //     ? double.tryParse(
        //         _materialQuantityController.text.replaceAll(r',', '.'))
        //     : null,
        materialName: _selectedType != ProductType.part &&
                _selectedType != ProductType.assembly
            ? (_selectedNomenclature == null
                ? _nameController.text.isEmpty
                    ? null
                    : _nameController.text
                : null)
            : null,
        materialId: _selectedType != ProductType.part &&
                _selectedType != ProductType.assembly
            ? _selectedNomenclature?.id
            : null,
      ),
    ));

    setState(() {
      _isLoading = false;
    });
    widget.onSuccess?.call();
  }

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    setState(() {
      _showTopGradient = _scrollController.offset > 0;
      _showBottomGradient = _scrollController.position.pixels <
          _scrollController.position.maxScrollExtent;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final reducedByType = _getReducedByType();

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0),
          child: Text(
            'Добавление позиции',
            style: Fonts.titleMedium.merge(
              const TextStyle(height: 1.6),
            ),
          ),
        ),
        Expanded(
          child: ShaderMask(
            shaderCallback: (Rect rect) {
              return LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  if (_showTopGradient)
                    (isDarkTheme
                        ? AppColors.darkBackground
                        : AppColors.lightBackground)
                  else
                    Colors.transparent,
                  isDarkTheme
                      ? AppColors.darkBackground.withAlpha(0)
                      : AppColors.lightBackground.withAlpha(0),
                  isDarkTheme
                      ? AppColors.darkBackground.withAlpha(0)
                      : AppColors.lightBackground.withAlpha(0),
                  if (_showBottomGradient)
                    (isDarkTheme
                        ? AppColors.darkBackground
                        : AppColors.lightBackground)
                  else
                    Colors.transparent,
                ],
                stops: const [0.0, 0.05, 0.95, 1.0],
              ).createShader(rect);
            },
            blendMode: BlendMode.dstOut,
            child: ListView(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              children: [
                SizedBox(height: 10.0),
                Row(
                  children: [
                    Text(
                      'Тип позиции:',
                      style: Fonts.labelSmall.merge(
                        const TextStyle(height: 1.5),
                      ),
                    ),
                    const SizedBox(width: 10.0),
                    Row(
                      children: [
                        CustomDropDownButton(
                          isOpened: _dropdownIsOpened,
                          setIsOpened: (value) {
                            setState(() {
                              _dropdownIsOpened = value;
                            });
                          },
                          onToggleOpen: (toggleFunction) {
                            toggleDropdown = toggleFunction;
                          },
                          text: _selectedType.getName(),
                          children: ProductType.values.map((value) {
                            return CustomDropdownMenuItem(
                              text: value.getName(),
                              onTap: () {
                                toggleDropdown?.call();
                                setState(() {
                                  _selectedType = value;
                                });
                              },
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 24.0),
                Text(
                  'Название',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                TextField(
                  controller: _nameController,
                  onChanged: (_) {
                    if (_selectedType == ProductType.part ||
                        _selectedType == ProductType.assembly) {
                      return;
                    }
                    // if (_selectedType != ProductType.materials) return;
                    setState(() {
                      _selectedNomenclature = null;
                    });
                    _searchMaterial();
                  },
                  style: Fonts.labelSmall,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    hintText: 'Название',
                  ),
                ),
                if (_selectedType != ProductType.part &&
                    _selectedType != ProductType.assembly)
                  SizedBox(height: 10.0),
                if (_selectedType != ProductType.part &&
                    _selectedType != ProductType.assembly)
                  Wrap(
                    spacing: 4.0,
                    runSpacing: 4.0,
                    children: [
                      if (_selectedNomenclature != null)
                        CustomChip(
                          selected: true,
                          text: _selectedNomenclature?.name,
                        ),
                      ..._materialsFromSearch.map((material) {
                        return CustomChip(
                          text: material.name ?? 'NAME',
                          onTap: () {
                            _nameController.text = material.name ?? 'NAME';
                            setState(() {
                              _selectedNomenclature = material;
                              _selectedUnitType =
                                  material.baseUnit ?? UnitType.kg;
                              _materialsFromSearch.clear();
                            });
                          },
                        );
                      }),
                    ],
                  ),

                if (!reducedByType) const SizedBox(height: 24.0),
                if (!reducedByType)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Номер чертежа',
                        style: Fonts.labelSmall.merge(
                          const TextStyle(height: 1.5),
                        ),
                      ),
                      const SizedBox(height: 10.0),
                      TextField(
                        controller: _templateNumberController,
                        style: Fonts.labelSmall,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          hintText: 'Номер чертежа',
                        ),
                      ),
                    ],
                  ),
                const SizedBox(height: 24.0),
                Text(
                  'Количество',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                Row(children: [
                  if (_selectedType != ProductType.materials)
                    Expanded(
                      child: TextField(
                        controller: _quantityController,
                        style: Fonts.labelSmall,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          hintText: 'Количество',
                        ),
                      ),
                    ),
                  if (_selectedType != ProductType.materials)
                    const SizedBox(width: 12.0),
                  if (_selectedType != ProductType.materials)
                    Text(
                      'шт',
                      style: Fonts.bodyMedium.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        ),
                      ),
                    ),
                  if (_selectedType == ProductType.materials)
                    Expanded(
                      child: TextField(
                        controller: _materialQuantityController,
                        style: Fonts.labelSmall,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          hintText: 'Количество материала',
                        ),
                      ),
                    ),
                  if (_selectedType == ProductType.materials)
                    const SizedBox(width: 12.0),
                  if (_selectedType == ProductType.materials)
                    CustomDropDownButton(
                        isOpened: _unitTypeDropdownIsOpened,
                        setIsOpened: (value) {
                          setState(() {
                            _unitTypeDropdownIsOpened = value;
                          });
                        },
                        onToggleOpen: (toggleFunction) {
                          toggleUnitTypeDropdown = toggleFunction;
                        },
                        text: _selectedUnitType.getName(),
                        children: [
                          if (_selectedNomenclature?.baseUnit != null)
                            CustomDropdownMenuItem(
                              text: _selectedNomenclature!.baseUnit!.getName(),
                              onTap: () {
                                toggleUnitTypeDropdown?.call();
                                setState(() {
                                  _selectedUnitType =
                                      _selectedNomenclature!.baseUnit!;
                                });
                              },
                            ),
                          ...(_selectedNomenclature?.alternativeUnits
                                      ?.map((unit) => unit.unit)
                                      .whereType<UnitType>() ??
                                  UnitType.values)
                              .map((value) {
                            return CustomDropdownMenuItem(
                              text: value.getName(),
                              onTap: () {
                                toggleUnitTypeDropdown?.call();
                                setState(() {
                                  _selectedUnitType = value;
                                });
                              },
                            );
                          })
                        ]),
                ]),
                const SizedBox(height: 24.0),
                // Text(
                //   'Обозначение детали',
                //   style: Fonts.labelSmall.merge(
                //     const TextStyle(height: 1.5),
                //   ),
                // ),
                // const SizedBox(height: 10.0),
                // TextField(
                //   controller: _designationController,
                //   style: Fonts.labelSmall,
                //   keyboardType: TextInputType.number,
                //   decoration: const InputDecoration(
                //     hintText: 'Обозначение',
                //
                //   ),
                // ),
                // const SizedBox(height: 24.0),
                Text(
                  'Описание',
                  style: Fonts.labelSmall.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                TextField(
                  controller: _descriptionController,
                  style: Fonts.labelSmall,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    hintText: 'Описание',
                  ),
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          child: CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: _createProduct,
            text: 'Добавить позицию',
          ),
        )
      ])
    ]);
  }
}
