// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BlocStructureState {
  bool get selecting => throw _privateConstructorUsedError;
  List<ProductModel> get selected => throw _privateConstructorUsedError;
  List<ProductModel> get copied => throw _privateConstructorUsedError;
  List<TaskProgressModel> get selectedProgressTasks =>
      throw _privateConstructorUsedError;

  /// Create a copy of BlocStructureState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BlocStructureStateCopyWith<BlocStructureState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlocStructureStateCopyWith<$Res> {
  factory $BlocStructureStateCopyWith(
          BlocStructureState value, $Res Function(BlocStructureState) then) =
      _$BlocStructureStateCopyWithImpl<$Res, BlocStructureState>;
  @useResult
  $Res call(
      {bool selecting,
      List<ProductModel> selected,
      List<ProductModel> copied,
      List<TaskProgressModel> selectedProgressTasks});
}

/// @nodoc
class _$BlocStructureStateCopyWithImpl<$Res, $Val extends BlocStructureState>
    implements $BlocStructureStateCopyWith<$Res> {
  _$BlocStructureStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BlocStructureState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selecting = null,
    Object? selected = null,
    Object? copied = null,
    Object? selectedProgressTasks = null,
  }) {
    return _then(_value.copyWith(
      selecting: null == selecting
          ? _value.selecting
          : selecting // ignore: cast_nullable_to_non_nullable
              as bool,
      selected: null == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as List<ProductModel>,
      copied: null == copied
          ? _value.copied
          : copied // ignore: cast_nullable_to_non_nullable
              as List<ProductModel>,
      selectedProgressTasks: null == selectedProgressTasks
          ? _value.selectedProgressTasks
          : selectedProgressTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskProgressModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlocStructureStateImplCopyWith<$Res>
    implements $BlocStructureStateCopyWith<$Res> {
  factory _$$BlocStructureStateImplCopyWith(_$BlocStructureStateImpl value,
          $Res Function(_$BlocStructureStateImpl) then) =
      __$$BlocStructureStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool selecting,
      List<ProductModel> selected,
      List<ProductModel> copied,
      List<TaskProgressModel> selectedProgressTasks});
}

/// @nodoc
class __$$BlocStructureStateImplCopyWithImpl<$Res>
    extends _$BlocStructureStateCopyWithImpl<$Res, _$BlocStructureStateImpl>
    implements _$$BlocStructureStateImplCopyWith<$Res> {
  __$$BlocStructureStateImplCopyWithImpl(_$BlocStructureStateImpl _value,
      $Res Function(_$BlocStructureStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BlocStructureState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selecting = null,
    Object? selected = null,
    Object? copied = null,
    Object? selectedProgressTasks = null,
  }) {
    return _then(_$BlocStructureStateImpl(
      selecting: null == selecting
          ? _value.selecting
          : selecting // ignore: cast_nullable_to_non_nullable
              as bool,
      selected: null == selected
          ? _value._selected
          : selected // ignore: cast_nullable_to_non_nullable
              as List<ProductModel>,
      copied: null == copied
          ? _value._copied
          : copied // ignore: cast_nullable_to_non_nullable
              as List<ProductModel>,
      selectedProgressTasks: null == selectedProgressTasks
          ? _value._selectedProgressTasks
          : selectedProgressTasks // ignore: cast_nullable_to_non_nullable
              as List<TaskProgressModel>,
    ));
  }
}

/// @nodoc

class _$BlocStructureStateImpl extends _BlocStructureState {
  const _$BlocStructureStateImpl(
      {this.selecting = false,
      final List<ProductModel> selected = const [],
      final List<ProductModel> copied = const [],
      final List<TaskProgressModel> selectedProgressTasks = const []})
      : _selected = selected,
        _copied = copied,
        _selectedProgressTasks = selectedProgressTasks,
        super._();

  @override
  @JsonKey()
  final bool selecting;
  final List<ProductModel> _selected;
  @override
  @JsonKey()
  List<ProductModel> get selected {
    if (_selected is EqualUnmodifiableListView) return _selected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selected);
  }

  final List<ProductModel> _copied;
  @override
  @JsonKey()
  List<ProductModel> get copied {
    if (_copied is EqualUnmodifiableListView) return _copied;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_copied);
  }

  final List<TaskProgressModel> _selectedProgressTasks;
  @override
  @JsonKey()
  List<TaskProgressModel> get selectedProgressTasks {
    if (_selectedProgressTasks is EqualUnmodifiableListView)
      return _selectedProgressTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedProgressTasks);
  }

  @override
  String toString() {
    return 'BlocStructureState(selecting: $selecting, selected: $selected, copied: $copied, selectedProgressTasks: $selectedProgressTasks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlocStructureStateImpl &&
            (identical(other.selecting, selecting) ||
                other.selecting == selecting) &&
            const DeepCollectionEquality().equals(other._selected, _selected) &&
            const DeepCollectionEquality().equals(other._copied, _copied) &&
            const DeepCollectionEquality()
                .equals(other._selectedProgressTasks, _selectedProgressTasks));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selecting,
      const DeepCollectionEquality().hash(_selected),
      const DeepCollectionEquality().hash(_copied),
      const DeepCollectionEquality().hash(_selectedProgressTasks));

  /// Create a copy of BlocStructureState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BlocStructureStateImplCopyWith<_$BlocStructureStateImpl> get copyWith =>
      __$$BlocStructureStateImplCopyWithImpl<_$BlocStructureStateImpl>(
          this, _$identity);
}

abstract class _BlocStructureState extends BlocStructureState {
  const factory _BlocStructureState(
          {final bool selecting,
          final List<ProductModel> selected,
          final List<ProductModel> copied,
          final List<TaskProgressModel> selectedProgressTasks}) =
      _$BlocStructureStateImpl;
  const _BlocStructureState._() : super._();

  @override
  bool get selecting;
  @override
  List<ProductModel> get selected;
  @override
  List<ProductModel> get copied;
  @override
  List<TaskProgressModel> get selectedProgressTasks;

  /// Create a copy of BlocStructureState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BlocStructureStateImplCopyWith<_$BlocStructureStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
