part of 'bloc.dart';

sealed class BlocStructureEvents {}

final class ToggleSelectingInStructure extends BlocStructureEvents {
  ToggleSelectingInStructure();
}

final class ClearSelectionsInStructure extends BlocStructureEvents {
  ClearSelectionsInStructure();
}

final class ToggleInStructure extends BlocStructureEvents {
  final ProductModel item;
  ToggleInStructure(this.item);
}

final class SetCopiedInStructure extends BlocStructureEvents {
  final List<ProductModel> items;
  SetCopiedInStructure(this.items);
}

// final class SetSelectedProgressTasksInStructure extends BlocStructureEvents {
//   final List<TaskProgressModel> items;
//   SetSelectedProgressTasksInStructure(this.items);
// }

final class ToggleSelectedProgressTaskInStructure extends BlocStructureEvents {
  final TaskProgressModel item;
  ToggleSelectedProgressTaskInStructure(this.item);
}

final class ClearProgressTasksSelectionsInStructure
    extends BlocStructureEvents {
  ClearProgressTasksSelectionsInStructure();
}
