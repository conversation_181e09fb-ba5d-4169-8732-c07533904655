import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';

part 'bloc.freezed.dart';
part 'events.dart';
part 'state.dart';

class BlocStructure extends Bloc<BlocStructureEvents, BlocStructureState> {
  BlocStructure()
      : super(const BlocStructureState(
          selecting: true,
          selected: [],
        )) {
    on<ToggleSelectingInStructure>((event, emit) {
      state.selecting == false
          ? emit(state.copyWith(selecting: true))
          : emit(state.copyWith(selecting: false, selected: []));
    });
    on<ClearSelectionsInStructure>((event, emit) {
      emit(state.copyWith(selected: []));
    });
    on<ToggleInStructure>((event, emit) {
      final selectedProductIds = state.selected
          .map((product) => product.id)
          .where((id) => id != null)
          .toSet();

      if (selectedProductIds.contains(event.item.id)) {
        emit(
          state.copyWith(
            selected: List.of(state.selected)
              ..removeWhere((item) => item.id == event.item.id),
          ),
        );
        print('removed: ${event.item.id}');
      } else {
        print('added: ${event.item.id}');
        emit(
          state.copyWith(selected: List.of(state.selected)..add(event.item)),
        );
      }
    });
    on<SetCopiedInStructure>((event, emit) {
      emit(state.copyWith(copied: event.items));
    });
    on<ToggleSelectedProgressTaskInStructure>((event, emit) {
      final selectedProgressTaskIds = state.selectedProgressTasks
          .map((progressTask) => progressTask.id)
          .where((id) => id != null)
          .toSet();

      if (selectedProgressTaskIds.contains(event.item.id)) {
        emit(
          state.copyWith(
            selectedProgressTasks: List.of(state.selectedProgressTasks)
              ..removeWhere((item) => item.id == event.item.id),
          ),
        );
        print('removed: ${event.item.id}');
      } else {
        print('added: ${event.item.id}');
        emit(
          state.copyWith(
              selectedProgressTasks: List.of(state.selectedProgressTasks)
                ..add(event.item)),
        );
      }
    });
    on<ClearProgressTasksSelectionsInStructure>((event, emit) {
      emit(state.copyWith(selectedProgressTasks: []));
    });
  }
}
