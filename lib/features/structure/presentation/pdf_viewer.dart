import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:sphere/features/files/data/repositories/index.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class PdfViewerScreen extends StatelessWidget {
  PdfViewerScreen({
    super.key,
    @PathParam('pdfPath') required this.pdfPath,
  });

  final String pdfPath;
  final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final controller = PdfViewerController();
    final String pdfUrl = FilesRepository.getPath(pdfPath);
    print(pdfUrl);

    // _pdfViewerKey.currentState?.;

    return Scaffold(
      appBar: CustomAppBar(
        title: pdfPath,
        rightPart: TextButton(
          onPressed: () {
            launchUrl(Uri.parse(pdfUrl));
          },
          child: Text(
            'Открыть в браузере',
            style: Fonts.labelSmall,
          ),
        ),
      ),
      body: Row(
        children: [
          Column(children: [
            IconButton(
              onPressed: () {
                controller.zoomLevel += 1;
              },
              icon: Icon(Icons.zoom_in),
            ),
            IconButton(
              onPressed: () {
                controller.zoomLevel -= 1;
              },
              icon: Icon(Icons.zoom_out),
            ),
            IconButton(
              onPressed: () {
                controller.zoomLevel = 1;
              },
              icon: Icon(Icons.refresh),
            ),
            // IconButton(
            //   onPressed: () {
            //     // controller.previousPage();
            //     controller.jumpTo(
            //       xOffset: controller.scrollOffset.dx - 50, // Смещаем вправо
            //       yOffset: controller.scrollOffset.dy,
            //     );
            //   },
            //   icon: Icon(Icons.arrow_back_rounded),
            // ),
            // IconButton(
            //   onPressed: () {
            //     // controller.nextPage();

            //     controller.jumpTo(
            //       xOffset: controller.scrollOffset.dx + 50, // Смещаем вправо
            //       yOffset: controller.scrollOffset.dy,
            //     );
            //   },
            //   icon: Icon(Icons.arrow_forward_rounded),
            // ),
            IconButton(
              onPressed: () async {
                try {
                  // Get PDF bytes from the viewer
                  final List<int> docBytes = await controller.saveDocument();

                  // Open file picker to select save location
                  final String? filePath = await FilePicker.platform.saveFile(
                    fileName: pdfPath.split('/').last,
                    type: FileType.custom,
                    allowedExtensions: ['pdf'],
                    dialogTitle: 'Сохранить файл',
                  );

                  // If user cancels, exit function
                  if (filePath == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      CustomSnackbar(text: "Сохранение отменено")
                          .toSnackBar(context),
                    );
                    return;
                  }

                  // Save the file
                  final file = File(filePath);
                  await file.writeAsBytes(docBytes);

                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    CustomSnackbar(text: "Файл успешно сохранён!")
                        .toSnackBar(context),
                  );
                } catch (e) {
                  // Handle errors
                  ScaffoldMessenger.of(context).showSnackBar(
                    CustomSnackbar(text: "Ошибка сохранения: $e")
                        .toSnackBar(context),
                  );
                }
              },
              icon: Icon(Icons.download_rounded),
            ),
          ]),
          VerticalDivider(width: 1.0),
          Expanded(
            child: Stack(
              children: [
                SfPdfViewer.network(
                  pdfUrl,
                  controller: controller,
                  key: _pdfViewerKey,
                  maxZoomLevel: 10,
                  pageLayoutMode: PdfPageLayoutMode.single,
                  // onZoomLevelChanged: (details) {
                  //   controller.zoomLevel = ;
                  // },
                  onDocumentLoadFailed: (details) {
                    ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
                      text: "Ошибка загрузки: ${details.description}",
                    ).toSnackBar(context));
                  },
                  // onDocumentLoaded: (details) {
                  //   ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(
                  //     text: 'Документ успешно загружен',
                  //   ).toSnackBar(context));
                  // },
                ),
                // Positioned(
                //   top: 10,
                //   left: 10,
                //   child: Column(
                //     children: [
                //     ],
                //   ),
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
