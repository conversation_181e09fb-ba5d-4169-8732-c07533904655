import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sphere/core/navigation/index.dart';
import 'package:sphere/core/settings.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/auth/presentation/bloc/bloc.dart';
import 'package:sphere/features/deliveries/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/arrows/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/structure/presentation/bloc/bloc.dart';
import 'package:sphere/features/warehouses/presentation/bloc/bloc.dart';
import 'package:sphere/shared/styles/themes.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Optimize: Call settings once during build
    CoreSettings().getSystemUIOverlaySettings(true);

    // Optimize: Create router once and reuse
    final appRouter = AppRouter();

    return MultiBlocProvider(
      providers: [
        // Optimize: Only create essential BLoCs at startup
        // Others will be created lazily when needed
        BlocProvider<BlocInitial>(
          create: (_) => BlocInitial(),
          lazy: false, // Keep this one eager as it's used immediately
        ),
        BlocProvider<BlocAuth>(
          create: (_) => BlocAuth(),
          lazy: false, // Auth is needed early
        ),
        // Make other BLoCs lazy to improve startup performance
        BlocProvider<BlocPurchaseList>(
          create: (_) => BlocPurchaseList(),
          lazy: true,
        ),
        BlocProvider<BlocArrows>(
          create: (_) => BlocArrows(),
          lazy: true,
        ),
        BlocProvider<BlocStructure>(
          create: (_) => BlocStructure(),
          lazy: true,
        ),
        BlocProvider<BlocDeliveries>(
          create: (_) => BlocDeliveries(),
          lazy: true,
        ),
        BlocProvider<BlocWarehouses>(
          create: (_) => BlocWarehouses(),
          lazy: true,
        ),
      ],
      child: MaterialApp.router(
        restorationScopeId: 'app',
        scrollBehavior: const CupertinoScrollBehavior(),
        debugShowCheckedModeBanner: false,

        // Optimize: Use const for static configurations
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ru', ''), // Russian, no country code
        ],

        // Optimize: Use const themes
        theme: AppThemes.lightTheme,
        darkTheme: AppThemes.darkTheme,
        themeMode: ThemeMode.light,

        routerConfig: appRouter.config(
          navigatorObservers: () => [AutoRouteObserver()],
        ),
      ),
    );
  }
}
