import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class CustomDropDownButton extends StatefulWidget {
  const CustomDropDownButton({
    super.key,
    required this.children,
    required this.text,
    this.isOpened = false,
    this.setIsOpened,
    this.onToggleOpen,
    this.openable = true,
  });

  final List<Widget> children;
  final String text;
  final bool isOpened;
  final bool openable;
  final void Function(bool value)? setIsOpened;
  final void Function(Future<void> Function())? onToggleOpen;

  @override
  State<CustomDropDownButton> createState() => _CustomDropDownButtonState();
}

class _CustomDropDownButtonState extends State<CustomDropDownButton>
    with TickerProviderStateMixin {
  AnimationController? _openAnimationController;

  Future<void> toggleOpen() async {
    final RenderBox buttonRenderBox = context.findRenderObject() as RenderBox;
    final Size buttonSize = buttonRenderBox.size;
    final Offset buttonPosition = buttonRenderBox.localToGlobal(
      Offset(0, buttonSize.height + 8.0),
    );

    if (!widget.isOpened) {
      CustomDropdownMenu.instance.show(
        context: context,
        items: widget.children,
        position: buttonPosition,
        onExit: toggleOpen,
      );
      // await _openAnimationController!.forward();
    } else {
      CustomDropdownMenu.instance.hide();
    }
    widget.setIsOpened?.call(!widget.isOpened);
  }

  @override
  void initState() {
    super.initState();

    _openAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    widget.onToggleOpen?.call(toggleOpen);

    if (widget.isOpened) {
      _openAnimationController?.forward();
    }
  }

  @override
  void didUpdateWidget(covariant CustomDropDownButton oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isOpened != oldWidget.isOpened) {
      if (widget.isOpened) {
        _openAnimationController?.forward();
      } else {
        _openAnimationController?.reverse();
      }
    }
  }

  @override
  void dispose() {
    _openAnimationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final arrowUpAnimation = CurvedAnimation(
      parent: _openAnimationController!,
      curve: Curves.easeInOut,
    );

    return InkWell(
      borderRadius: BorderRadius.circular(4.0),
      onTap: widget.openable ? toggleOpen : null,
      child: Ink(
        padding: const EdgeInsets.all(4.0),
        child: Row(children: [
          Text(widget.text, style: Fonts.labelSmall),
          if (_openAnimationController != null && widget.openable)
            const SizedBox(width: 8.0),
          if (_openAnimationController != null && widget.openable)
            RotationTransition(
              turns: arrowUpAnimation.drive(Tween(begin: 0.5, end: 0)),
              child: SVG(Assets.icons.keyboardArrowUp),
            ),
        ]),
      ),
    );
  }
}
