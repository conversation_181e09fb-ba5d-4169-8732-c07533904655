import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';

class GhostButton extends StatelessWidget {
  const GhostButton({
    super.key,
    this.onTap,
    this.onSecondaryTapDown,
    this.color,
    this.borderRadius,
    this.border,
    this.backgroundOffset,
    required this.child,
  });

  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final Color? color;
  final double? borderRadius;
  final Border? border;
  final Offset? backgroundOffset;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final innerColor =
        isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary;

    return GestureDetector(
      onTap: onTap,
      onSecondaryTapDown: onSecondaryTapDown,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            left: backgroundOffset?.dx ?? -4,
            right: backgroundOffset?.dx ?? -4,
            top: backgroundOffset?.dy ?? -2,
            bottom: backgroundOffset?.dy ?? -2,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                splashFactory: InkSparkle.splashFactory,
                borderRadius: BorderRadius.circular(borderRadius ?? 6.0),
                splashColor: color?.withAlpha(40) ?? innerColor.withAlpha(40),
                highlightColor:
                    color?.withAlpha(30) ?? innerColor.withAlpha(30),
                hoverColor: color?.withAlpha(20) ?? innerColor.withAlpha(20),
                onTap: onTap,
                onSecondaryTapDown: onSecondaryTapDown,
                child: AnimatedOpacity(
                  opacity: 0,
                  duration: const Duration(milliseconds: 500),
                  child: Ink(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(borderRadius ?? 6.0),
                      border: border,
                    ),
                  ),
                ),
              ),
            ),
          ),
          IgnorePointer(child: child),
        ],
      ),
    );
  }
}
