import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class CustomDropdownMenu {
  static final CustomDropdownMenu instance = CustomDropdownMenu._internal();

  CustomDropdownMenu._internal();

  factory CustomDropdownMenu() => instance;

  OverlayEntry? _overlayEntry;

  void show({
    required BuildContext context,
    required List<Widget> items,
    required Offset position,
    bool alignRight = false,
    Offset? innerPosition,
    bool withBackground = true,
    Function()? onExit,
  }) {
    hide();

    _overlayEntry = _createOverlayEntry(
      context: context,
      items: items,
      position: innerPosition ?? position,
      withBackground: withBackground,
      onExit: onExit,
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  OverlayEntry _createOverlayEntry({
    required BuildContext context,
    required List<Widget> items,
    required Offset position,
    required bool withBackground,
    Function()? onExit,
  }) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;
    final menuWidth = screenSize.width > 500 ? 340.0 : 280.0;

    // Определяем стороны экрана
    final isRightHalf = position.dx > screenSize.width - menuWidth;
    final isBottomHalf = position.dy > screenSize.height / 2;

    // Рассчитываем позицию
    double left = isRightHalf ? position.dx - menuWidth : position.dx;

    // Проверка границ экрана
    // if (left + menuWidth > screenSize.width) {
    //   left = screenSize.width - menuWidth - 12;
    // }

    return OverlayEntry(
      builder: (context) {
        final child = Positioned(
          left: left,
          top: isBottomHalf ? null : position.dy,
          bottom: isBottomHalf ? screenSize.height - position.dy : null,
          child: Material(
            elevation: 6,
            clipBehavior: Clip.hardEdge,
            shape: RoundedRectangleBorder(
              side: BorderSide(
                color:
                    isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
              ),
              borderRadius: BorderRadius.circular(20.0),
            ),
            shadowColor: AppColors.lightPrimary.withValues(alpha: 0.75),
            color: isDarkTheme
                ? AppColors.darkBackground
                : AppColors.lightBackground,
            child: SizedBox(
              width: menuWidth,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: AnimateList(
                  interval: 100.ms,
                  effects: [FadeEffect(duration: 200.ms)],
                  children: items.asMap().entries.map((entry) {
                    final item = entry.value;
                    final index = entry.key;
                    return Column(children: [
                      item,
                      if (index != items.length - 1) const Divider(height: 1),
                    ]);
                  }).toList(),
                ),
              ),
            ),
          ),
        );

        if (!withBackground) {
          return child;
        }
        return Stack(
          fit: StackFit.expand,
          children: [
            GestureDetector(
              onTap: () {
                onExit?.call();
                hide();
              },
              onSecondaryTap: () {
                onExit?.call();
                hide();
              },
              onVerticalDragCancel: () {
                onExit?.call();
                hide();
              },
              onHorizontalDragCancel: () {
                onExit?.call();
                hide();
              },
              child: Container(
                color: Color(0x00000000),
                // child: ClipRRect(
                //   child: BackdropFilter(
                //     filter: ImageFilter.blur(
                //       sigmaX: 1.5,
                //       sigmaY: 1.5,
                //     ),
                //     child: SizedBox(),
                //   ),
                // ),
              ),
            ),
            child,
          ],
        );
      },
    );
  }
}

class CustomDropdownMenuItem extends StatelessWidget {
  final String? icon;
  final Widget? child;
  final VoidCallback? onTap;
  final String? text;
  final bool? disabled;
  final String? description;

  const CustomDropdownMenuItem({
    super.key,
    this.icon,
    this.child,
    this.text,
    this.onTap,
    this.disabled,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: disabled == true ? 0.5 : 1.0,
      child: InkWell(
        splashFactory: InkSparkle.splashFactory,
        onTap: disabled == true ? null : onTap,
        child: Ink(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              if (icon != null) SVG(icon!),
              if (icon != null) const SizedBox(width: 12.0),
              child ??
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(text ?? 'Menu item', style: Fonts.labelSmall),
                        if (description != null) const SizedBox(height: 4.0),
                        if (description != null)
                          Text(
                            description!,
                            style: Fonts.bodySmall.merge(
                              const TextStyle(
                                color: AppColors.medium,
                                height: 1.2,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
            ],
          ),
        ),
      ),
    );
  }
}
