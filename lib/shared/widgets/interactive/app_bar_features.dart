import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class CustomAppBarFeatures {
  static Widget getPopupMenu({
    required List<Widget> children,
    required BuildContext context,
  }) {
    return Builder(
      builder: (BuildContext builderContext) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            AspectRatio(
              aspectRatio: 1 / 1,
              child: Material(
                color: Color(0x00000000),
                child: InkWell(
                  splashFactory: InkSparkle.splashFactory,
                  hoverColor: Color(0x20808080),
                  onTap: () {
                    final RenderBox renderBox =
                        builderContext.findRenderObject() as RenderBox;
                    final position =
                        renderBox.localToGlobal(Offset.zero).translate(
                              renderBox.size.width,
                              62.0,
                            );

                    CustomDropdownMenu.instance.show(
                      context: context,
                      alignRight: true,
                      items: children,
                      position: position,
                    );
                  },
                  child: Center(
                    child: SVG(
                      Assets.icons.keyboardArrowDown,
                      width: 24.0,
                      height: 24.0,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
