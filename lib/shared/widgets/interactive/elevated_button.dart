import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

enum CustomElevatedButtonTypes {
  common,
  accent,
  inverse,
  attention,
  success,
}

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton({
    super.key,
    required this.onPressed,
    this.text,
    this.child,
    this.type = CustomElevatedButtonTypes.common,
    this.disabled = false,
    this.style,
  });

  final void Function() onPressed;
  final String? text;
  final Widget? child;
  final CustomElevatedButtonTypes type;
  final bool? disabled;
  final TextStyle? style;

  Color getBackgroundColor(
    CustomElevatedButtonTypes type,
    BuildContext context,
  ) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    switch (type) {
      case CustomElevatedButtonTypes.common:
        return isDarkTheme ? AppColors.darkSurface : AppColors.lightBackground;
      case CustomElevatedButtonTypes.inverse:
        return isDarkTheme ? AppColors.darkPrimary : AppColors.lightPrimary;
      case CustomElevatedButtonTypes.accent:
        return isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary;
      case CustomElevatedButtonTypes.attention:
        return isDarkTheme ? AppColors.darkError : AppColors.lightError;
      case CustomElevatedButtonTypes.success:
        return isDarkTheme ? AppColors.darkSuccess : AppColors.lightSuccess;
    }
  }

  Color getFontColor(
    CustomElevatedButtonTypes type,
    BuildContext context,
  ) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    if (disabled ?? false) {
      return AppColors.medium;
    }
    switch (type) {
      case CustomElevatedButtonTypes.common:
        return isDarkTheme ? AppColors.darkPrimary : AppColors.lightPrimary;
      case CustomElevatedButtonTypes.inverse:
        return isDarkTheme
            ? AppColors.darkBackground
            : AppColors.lightBackground;
      case CustomElevatedButtonTypes.accent:
        return AppColors.lightBackground;
      case CustomElevatedButtonTypes.attention:
        return AppColors.lightBackground;
      case CustomElevatedButtonTypes.success:
        return AppColors.lightPrimary;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        // boxShadow: [
        //   BoxShadow(
        //     offset: const Offset(0, 4),
        //     blurRadius: 12.0,
        //     color: AppColors.lightPrimary.withValues(alpha: 0.06),
        //   ),
        // ],
        border: Border.all(
          color: isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
        ),
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          disabledBackgroundColor: AppColors.medium.withValues(alpha: 0.2),
          backgroundColor: getBackgroundColor(type, context),
          minimumSize: Size(0, 48),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        onPressed: disabled ?? false ? null : onPressed,
        child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (child != null) child!,
              if (text != null)
                Flexible(
                  child: Text(
                    text!,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Fonts.labelLarge
                        .merge(
                          TextStyle(color: getFontColor(type, context)),
                        )
                        .merge(style),
                  ),
                ),
            ]),
      ),
    );
  }
}
