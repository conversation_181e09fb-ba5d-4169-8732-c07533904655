import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class CustomChip extends StatelessWidget {
  const CustomChip({
    super.key,
    this.text,
    this.textStyle,
    this.selected,
    this.onTap,
    this.onTapDown,
    this.padding,
    this.deleteIcon,
    this.child,
  });

  final String? text;
  final bool? selected;
  final void Function()? onTap;
  final void Function(TapDownDetails details)? onTapDown;
  final TextStyle? textStyle;
  final EdgeInsets? padding;
  final Widget? deleteIcon;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    Color getBackgroundColor() {
      if (isDarkTheme) {
        switch (selected) {
          case null:
            return AppColors.darkSurface;
          case true:
            return AppColors.darkSecondary;
          case false:
            return AppColors.darkSurface;
        }
      } else {
        switch (selected) {
          case null:
            return AppColors.lightSurface;
          case true:
            return AppColors.lightSecondary;
          case false:
            return AppColors.lightSurface;
        }
      }
    }

    Color getTextColor() {
      if (isDarkTheme) {
        switch (selected) {
          case null:
            return AppColors.darkPrimary;
          case true:
            return AppColors.darkPrimary;
          case false:
            return AppColors.darkPrimary;
        }
      } else {
        switch (selected) {
          case null:
            return AppColors.lightPrimary;
          case true:
            return AppColors.lightBackground;
          case false:
            return AppColors.lightPrimary;
        }
      }
    }

    return Material(
      color: Colors.transparent,
      // clipBehavior: Clip.hardEdge,
      child: InkWell(
        onTap: onTap,
        onTapDown: onTapDown,
        splashFactory: InkSparkle.splashFactory,
        borderRadius: BorderRadius.circular(8.0),
        child: Ink(
          decoration: BoxDecoration(
            color: getBackgroundColor(),
            borderRadius: BorderRadius.circular(8.0),
          ),
          padding: padding ?? const EdgeInsets.all(4.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (text != null)
                Text(
                  text ?? 'CHIP',
                  style: Fonts.labelSmall
                      .merge(TextStyle(
                        color: getTextColor(),
                      ))
                      .merge(textStyle),
                ),
              if (child != null) child!,
              if (deleteIcon != null) SizedBox(width: 4.0),
              if (deleteIcon != null) deleteIcon!,
            ],
          ),
        ),
      ),
    );
  }
}
