import 'dart:io';

import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class GlobalWrapper extends StatelessWidget {
  const GlobalWrapper({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      return Column(children: [
        Row(children: [
          Text('Сфера', style: Fonts.labelLarge),
          Spacer(),
          IconButton(
            onPressed: () {
              appWindow.hide();
            },
            icon: SVG(Assets.icons.underline),
          ),
          SizedBox(width: 8.0),
          IconButton(
            onPressed: () {
              appWindow.hide();
              // appWindow.close();
            },
            icon: SVG(Assets.icons.close),
          ),
        ]),
        child,
      ]);
    } else {
      return child;
    }
  }
}
