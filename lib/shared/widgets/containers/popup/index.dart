import 'dart:math';

import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';

showBaseDialog(
  BuildContext context, {
  required Widget Function(BuildContext) builder,
  EdgeInsets? padding,
  double maxWidth = 600.0,
  double? maxHeight,
}) {
  final width = MediaQuery.of(context).size.width;
  final height = MediaQuery.of(context).size.height;

  showDialog(
    context: context,
    builder: (context) {
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

      return Dialog(
        backgroundColor:
            isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground,
        child: SizedBox(
          width: width < 400 ? width : min(width - 100, maxWidth),
          height: (maxHeight ?? 0) < height - 60 ? maxHeight : height - 60.0,
          child: Padding(
            padding: padding ?? EdgeInsets.all(width < 400 ? 12.0 : 18.0),
            child: builder(context),
          ),
        ),
      );
    },
  );
}
