import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/widgets/utility/skeleton.dart';

class CustomCard extends StatelessWidget {
  const CustomCard({
    super.key,
    this.padding,
    this.onTap,
    this.child,
    this.onTapDown,
    this.onSecondaryTapDown,
    this.isLoading,
    this.border,
    this.color,
    this.constraints,
    this.height,
    this.margin,
  });

  final EdgeInsets? padding;
  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final void Function(TapDownDetails)? onTapDown;
  final Widget? child;
  final bool? isLoading;
  final BoxBorder? border;
  final Color? color;
  final BoxConstraints? constraints;
  final double? height;
  final EdgeInsets? margin;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final innerColor = color ??
        (isDarkTheme ? AppColors.darkSurface : AppColors.lightBackground);

    // Optimize: Extract decoration to avoid recreating on every build
    final decoration = BoxDecoration(
      color: innerColor,
      borderRadius: BorderRadius.circular(20.0),
      border: border ??
          Border.all(
            color: isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
          ),
      boxShadow: const [
        // Optimize: Removed unused box shadow for better performance
      ],
    );

    return Container(
      height: height,
      constraints: constraints,
      margin: margin,
      decoration: decoration,
      child: Skeleton(
        isEnabled: isLoading ?? false,
        borderRadius: 20.0,
        child: Material(
          color: Colors.transparent,
          child: GestureDetector(
            onLongPressStart: (details) {
              onSecondaryTapDown?.call(TapDownDetails(
                globalPosition: details.globalPosition,
              ));
            },
            child: InkWell(
              borderRadius: BorderRadius.circular(20.0),
              onTap: onTap,
              onTapDown: onTapDown,
              onSecondaryTapDown: onSecondaryTapDown,
              splashFactory: InkSparkle.splashFactory,
              child: Ink(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.0),
                ),
                padding: padding ?? const EdgeInsets.all(12.0),
                child: child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
