import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';

class TechnologicalMapCard extends StatelessWidget {
  const TechnologicalMapCard({
    super.key,
    required this.operations,
    this.onTap,
    this.isLoading,
  });

  final void Function()? onTap;
  final List<String> operations;
  final bool? isLoading;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      isLoading: isLoading,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: const Text(
                  'Технологическая карта',
                  style: Fonts.titleSmall,
                ),
              ),
              Text(
                operations.length.toString(),
                style: Fonts.labelSmall.merge(
                  TextStyle(
                    color: isDarkTheme
                        ? AppColors.darkDescription
                        : AppColors.lightDescription,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.0),
          if (operations.isEmpty)
            Text(
              'Технологическая карта пуста',
              style: Fonts.labelSmall.merge(
                TextStyle(
                  color: isDarkTheme
                      ? AppColors.darkDescription
                      : AppColors.lightDescription,
                ),
              ),
            ),
          if (operations.isNotEmpty)
            SizedBox(
              height: 20,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: operations.length,
                itemBuilder: (context, index) {
                  return Row(
                    children: [
                      if (index != 0) const Text('→ ', style: Fonts.labelSmall),
                      Text('${index + 1}. ${operations[index]}',
                          style: Fonts.labelSmall),
                      if (index != operations.length - 1)
                        const Text(' ', style: Fonts.labelSmall),
                    ],
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}
