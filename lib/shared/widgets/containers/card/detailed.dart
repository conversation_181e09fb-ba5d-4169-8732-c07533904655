import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class DetailedCard extends StatelessWidget {
  const DetailedCard({
    super.key,
    this.onTap,
    this.title,
    this.description,
    this.icon,
    this.count,
    this.onSecondaryTapDown,
    this.isLoading,
    this.height,
  });

  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final String? title;
  final String? description;
  final String? icon;
  final int? count;
  final bool? isLoading;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      isLoading: isLoading,
      height: height,
      onTap: onTap,
      onSecondaryTapDown: onSecondaryTapDown,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(title ?? 'TITLE', style: Fonts.titleMedium),
        if (description != null) const SizedBox(height: 10.0),
        if (description != null)
          Text(
            description!,
            style: Fonts.bodySmall,
          ),
        const Spacer(),
        if (icon != null && count != null)
          Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            SVG(icon!),
            const SizedBox(width: 10),
            Text(
              '$count',
              style: Fonts.labelLarge,
            ),
          ]),
      ]),
    );
  }
}
