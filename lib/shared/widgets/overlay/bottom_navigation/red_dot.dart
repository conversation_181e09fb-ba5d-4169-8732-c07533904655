import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';

class RedDot extends StatelessWidget {
  const RedDot({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: 10.0,
      height: 10.0,
      decoration: BoxDecoration(
        color: isDarkTheme ? AppColors.darkError : AppColors.lightError,
        borderRadius: BorderRadius.circular(1000.0),
      ),
    );
  }
}
