import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/widgets/overlay/bottom_navigation/navigation_button.dart';

class CustomBottomNavigationBar extends StatelessWidget {
  const CustomBottomNavigationBar({
    super.key,
    required this.children,
    required this.currentIndex,
    required this.onTap,
  });

  final List<CustomNavigationButton> children;
  final int currentIndex;
  final void Function(int) onTap;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground;
    final strokeColor =
        isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: BorderDirectional(
          top: BorderSide(color: strokeColor),
        ),
      ),
      child: Safe<PERSON>rea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: children.asMap().entries.map((entry) {
            final button = entry.value;
            final index = entry.key;

            return HeroMode(
              enabled: true,
              child: CustomNavigationButton(
                key: ValueKey(index),
                icon: button.icon,
                label: button.label,
                withNotification: button.withNotification,
                isActive: index == currentIndex,
                onTap: () {
                  onTap(index);
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
