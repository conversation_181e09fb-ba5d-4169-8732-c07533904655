import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/overlay/bottom_navigation/red_dot.dart';

class CustomNavigationButton extends StatelessWidget {
  const CustomNavigationButton({
    super.key,
    required this.icon,
    this.isActive,
    this.label,
    this.withNotification,
    this.onTap,
  });

  final Widget icon;
  final bool? isActive;
  final String? label;
  final bool? withNotification;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      clipBehavior: Clip.none,
      child: InkWell(
        splashFactory: InkSparkle.splashFactory,
        borderRadius: BorderRadius.circular(8.0),
        onTap: onTap,
        child: Ink(
          child: Stack(children: [
            if (isActive == true)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: isDarkTheme
                        ? AppColors.darkStroke
                        : AppColors.lightStroke,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(6.0),
              child: Row(children: [
                Stack(children: [
                  icon,
                  if (withNotification == true)
                    const Positioned(
                      top: 0,
                      right: 0,
                      child: RedDot(),
                    ),
                ]),
                if (label != null) const SizedBox(width: 10.0),
                if (label != null)
                  Expanded(
                    child: Text(
                      label!,
                      style: Fonts.labelSmall,
                      softWrap: false,
                      overflow: TextOverflow.fade,
                    ),
                  )
              ]),
            ),
          ]),
        ),
      ),
    );
  }
}
