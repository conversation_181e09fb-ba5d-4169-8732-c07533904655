// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CustomAppBarConfig {
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get leftIcon => throw _privateConstructorUsedError;
  double get height => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  Widget? get rightPart => throw _privateConstructorUsedError;

  /// Create a copy of CustomAppBarConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomAppBarConfigCopyWith<CustomAppBarConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomAppBarConfigCopyWith<$Res> {
  factory $CustomAppBarConfigCopyWith(
          CustomAppBarConfig value, $Res Function(CustomAppBarConfig) then) =
      _$CustomAppBarConfigCopyWithImpl<$Res, CustomAppBarConfig>;
  @useResult
  $Res call(
      {String title,
      String? description,
      String? leftIcon,
      double height,
      bool isLoading,
      Widget? rightPart});
}

/// @nodoc
class _$CustomAppBarConfigCopyWithImpl<$Res, $Val extends CustomAppBarConfig>
    implements $CustomAppBarConfigCopyWith<$Res> {
  _$CustomAppBarConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomAppBarConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = freezed,
    Object? leftIcon = freezed,
    Object? height = null,
    Object? isLoading = null,
    Object? rightPart = freezed,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      leftIcon: freezed == leftIcon
          ? _value.leftIcon
          : leftIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      rightPart: freezed == rightPart
          ? _value.rightPart
          : rightPart // ignore: cast_nullable_to_non_nullable
              as Widget?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomAppBarConfigImplCopyWith<$Res>
    implements $CustomAppBarConfigCopyWith<$Res> {
  factory _$$CustomAppBarConfigImplCopyWith(_$CustomAppBarConfigImpl value,
          $Res Function(_$CustomAppBarConfigImpl) then) =
      __$$CustomAppBarConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      String? description,
      String? leftIcon,
      double height,
      bool isLoading,
      Widget? rightPart});
}

/// @nodoc
class __$$CustomAppBarConfigImplCopyWithImpl<$Res>
    extends _$CustomAppBarConfigCopyWithImpl<$Res, _$CustomAppBarConfigImpl>
    implements _$$CustomAppBarConfigImplCopyWith<$Res> {
  __$$CustomAppBarConfigImplCopyWithImpl(_$CustomAppBarConfigImpl _value,
      $Res Function(_$CustomAppBarConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomAppBarConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = freezed,
    Object? leftIcon = freezed,
    Object? height = null,
    Object? isLoading = null,
    Object? rightPart = freezed,
  }) {
    return _then(_$CustomAppBarConfigImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      leftIcon: freezed == leftIcon
          ? _value.leftIcon
          : leftIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      height: null == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      rightPart: freezed == rightPart
          ? _value.rightPart
          : rightPart // ignore: cast_nullable_to_non_nullable
              as Widget?,
    ));
  }
}

/// @nodoc

class _$CustomAppBarConfigImpl extends _CustomAppBarConfig {
  const _$CustomAppBarConfigImpl(
      {required this.title,
      this.description,
      this.leftIcon,
      this.height = 48.0,
      this.isLoading = false,
      this.rightPart})
      : super._();

  @override
  final String title;
  @override
  final String? description;
  @override
  final String? leftIcon;
  @override
  @JsonKey()
  final double height;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final Widget? rightPart;

  @override
  String toString() {
    return 'CustomAppBarConfig(title: $title, description: $description, leftIcon: $leftIcon, height: $height, isLoading: $isLoading, rightPart: $rightPart)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomAppBarConfigImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.leftIcon, leftIcon) ||
                other.leftIcon == leftIcon) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.rightPart, rightPart) ||
                other.rightPart == rightPart));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, title, description, leftIcon, height, isLoading, rightPart);

  /// Create a copy of CustomAppBarConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomAppBarConfigImplCopyWith<_$CustomAppBarConfigImpl> get copyWith =>
      __$$CustomAppBarConfigImplCopyWithImpl<_$CustomAppBarConfigImpl>(
          this, _$identity);
}

abstract class _CustomAppBarConfig extends CustomAppBarConfig {
  const factory _CustomAppBarConfig(
      {required final String title,
      final String? description,
      final String? leftIcon,
      final double height,
      final bool isLoading,
      final Widget? rightPart}) = _$CustomAppBarConfigImpl;
  const _CustomAppBarConfig._() : super._();

  @override
  String get title;
  @override
  String? get description;
  @override
  String? get leftIcon;
  @override
  double get height;
  @override
  bool get isLoading;
  @override
  Widget? get rightPart;

  /// Create a copy of CustomAppBarConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomAppBarConfigImplCopyWith<_$CustomAppBarConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
