import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'config.freezed.dart';

@freezed
class CustomAppBarConfig with _$CustomAppBarConfig {
  const CustomAppBarConfig._();
  const factory CustomAppBarConfig({
    required String title,
    String? description,
    String? leftIcon,
    @Default(48.0) double height,
    @Default(false) bool isLoading,
    Widget? rightPart,
  }) = _CustomAppBarConfig;
}
