import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  const CustomAppBar({
    super.key,
    // this.rightButtonOnPress,
    this.rightPart,
    this.leftIcon,
    // this.rightIcon,
    this.height = 48.0,
    this.isLoading = false,
    this.title,
    this.description,
  });

  // buttons
  // final void Function()? rightButtonOnPress;
  final Widget? rightPart;
  final String? leftIcon;
  // final String? rightIcon;
  final String? title;
  final String? description;

  // utility
  final double height;
  final bool? isLoading;

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(height);
}

class _CustomAppBarState extends State<CustomAppBar> {
  Color? svgColor;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    svgColor =
        isDarkTheme ? AppColors.darkDescription : AppColors.lightDescription;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: SafeArea(
        child: SizedBox(
          height: widget.height,
          child: Column(children: [
            if (!kIsWeb &&
                (Platform.isWindows || Platform.isLinux || Platform.isMacOS))
              Expanded(
                child: Row(children: [
                  if (context.router.canPop())
                    SizedBox(
                      height: widget.height,
                      width: widget.height,
                      child: Material(
                        color: Color(0x00000000),
                        child: InkWell(
                          splashFactory: InkSparkle.splashFactory,
                          hoverColor: Color(0x20808080),
                          onTap: () => context.router.back(),
                          child: Center(
                            child: SVG(
                              widget.leftIcon ?? Assets.icons.arrowBack,
                              color: isDarkTheme
                                  ? AppColors.darkPrimary
                                  : AppColors.lightPrimary,
                              width: 24.0,
                              height: 24.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                  Expanded(
                    child: SizedBox(
                      child: MoveWindow(
                        child: Center(
                          child: Wrap(
                            alignment: WrapAlignment.center,
                            children: [
                              if (widget.title != null ||
                                  widget.isLoading == true)
                                Text(
                                  widget.isLoading!
                                      ? 'Обновление...'
                                      : widget.title!,
                                  style: Fonts.labelSmall,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              if (widget.description != null &&
                                  widget.isLoading != true)
                                SizedBox(width: 10.0),
                              if (widget.description != null &&
                                  widget.isLoading != true)
                                Text(
                                  widget.description!,
                                  style: Fonts.bodySmall.merge(
                                    TextStyle(
                                      color: isDarkTheme
                                          ? AppColors.darkDescription
                                          : AppColors.lightDescription,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  // if (widget.rightPart != null && widget.isLoading != true)
                  //   VerticalDivider(width: 24),
                  if (widget.rightPart != null && widget.isLoading != true)
                    widget.rightPart!,
                  if (widget.rightPart != null && widget.isLoading != true)
                    VerticalDivider(width: 24),
                  SizedBox(
                    height: widget.height,
                    width: widget.height,
                    child: Material(
                      color: Color(0x00000000),
                      child: InkWell(
                        splashFactory: InkSparkle.splashFactory,
                        hoverColor: Color(0x20808080),
                        onTap: () {
                          appWindow.minimize();
                        },
                        child: Center(
                          child: SVG(
                            Assets.icons.underline,
                            color: isDarkTheme
                                ? AppColors.darkDescription
                                : AppColors.lightDescription,
                            width: 18.0,
                            height: 18.0,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: widget.height,
                    width: widget.height,
                    child: Material(
                      color: Color(0x00000000),
                      child: InkWell(
                        splashFactory: InkSparkle.splashFactory,
                        hoverColor: Color(0x20808080),
                        onTap: () {
                          appWindow.maximizeOrRestore();
                        },
                        child: Center(
                          child: SVG(
                            Assets.icons.square,
                            color: isDarkTheme
                                ? AppColors.darkDescription
                                : AppColors.lightDescription,
                            width: 18.0,
                            height: 18.0,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: widget.height,
                    width: widget.height,
                    child: Material(
                      color: Color(0x00000000),
                      child: MouseRegion(
                        onEnter: (_) {
                          setState(() {
                            svgColor = AppColors.lightBackground;
                          });
                        },
                        onExit: (_) {
                          setState(() {
                            svgColor = isDarkTheme
                                ? AppColors.darkDescription
                                : AppColors.lightDescription;
                          });
                        },
                        child: InkWell(
                          splashFactory: InkSparkle.splashFactory,
                          hoverColor: Color(0xffff0000),
                          onTap: () {
                            appWindow.hide();
                            // appWindow.close();
                          },
                          child: Center(
                            child: SVG(
                              Assets.icons.close,
                              color: svgColor,
                              width: 18.0,
                              height: 18.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ]),
              ),
            if (!(!kIsWeb &&
                (Platform.isWindows || Platform.isLinux || Platform.isMacOS)))
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // const SizedBox(width: 12.0),
                    if (context.router.canPop() && !kIsWeb)
                      SizedBox(
                        height: widget.height,
                        width: widget.height,
                        child: Material(
                          color: Color(0x00000000),
                          child: InkWell(
                            splashFactory: InkSparkle.splashFactory,
                            hoverColor: Color(0x20808080),
                            onTap: () => context.router.back(),
                            child: Center(
                              child: SVG(
                                widget.leftIcon ?? Assets.icons.arrowBack,
                                color: isDarkTheme
                                    ? AppColors.darkPrimary
                                    : AppColors.lightPrimary,
                                width: 24.0,
                                height: 24.0,
                              ),
                            ),
                          ),
                        ),
                      ),
                    Expanded(
                      child: SizedBox(
                        child: MoveWindow(
                          child: Center(
                            child: Wrap(
                              alignment: WrapAlignment.center,
                              children: [
                                if (widget.title != null ||
                                    widget.isLoading == true)
                                  Text(
                                    widget.isLoading!
                                        ? 'Обновление...'
                                        : widget.title!,
                                    style: Fonts.labelSmall,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                if (widget.description != null &&
                                    widget.isLoading != true)
                                  SizedBox(width: 10.0),
                                if (widget.description != null &&
                                    widget.isLoading != true)
                                  Text(
                                    widget.description!,
                                    style: Fonts.bodySmall.merge(
                                      TextStyle(
                                        color: isDarkTheme
                                            ? AppColors.darkDescription
                                            : AppColors.lightDescription,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    if (widget.rightPart != null && widget.isLoading != true)
                      widget.rightPart!,
                  ],
                ),
              ),
            const Divider(
              height: 1.0,
            ),
          ]),
        ),
      ),
    );
  }
}
