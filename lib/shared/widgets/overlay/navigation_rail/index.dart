import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/widgets/overlay/bottom_navigation/navigation_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class CustomNavigationRail extends StatelessWidget {
  const CustomNavigationRail({
    super.key,
    required this.children,
    required this.currentIndex,
    required this.onTap,
    this.isExtended = false,
    this.toggleExtend,
  });

  final List<CustomNavigationButton> children;
  final int currentIndex;
  final void Function(int) onTap;
  final bool isExtended;
  final void Function()? toggleExtend;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;
    final backgroundColor =
        isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground;
    final strokeColor =
        isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke;

    return AnimatedContainer(
      width: isExtended ? 220 : 80,
      decoration: BoxDecoration(
        color: backgroundColor,
        border: BorderDirectional(end: BorderSide(color: strokeColor)),
      ),
      padding: const EdgeInsets.all(19.0),
      duration: const Duration(milliseconds: 200),
      curve: Curves.decelerate,
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildToggleExtendButton(),
            const SizedBox(height: 12.0),
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: _buildNavigationButtons(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleExtendButton() {
    return IconButton(
      onPressed: toggleExtend,
      icon: SVG(
        isExtended ? Assets.icons.tabHide : Assets.icons.tabShow,
        color: AppColors.medium,
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Column(
      key: ValueKey(isExtended),
      children: [
        for (var i = 0; i < children.length; i++) ...[
          CustomNavigationButton(
            key: ValueKey(i),
            icon: children[i].icon,
            label: isExtended ? children[i].label : null,
            withNotification: children[i].withNotification,
            isActive: i == currentIndex,
            onTap: () => onTap(i),
          ),
          if (i != children.length - 1) const SizedBox(height: 12.0),
        ],
      ],
    );
  }
}
