import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sphere/shared/styles/colors.dart';

class SVG extends StatelessWidget {
  const SVG(
    this.asset, {
    super.key,
    this.width,
    this.height,
    this.color,
    this.fit,
  });

  final String asset;
  final double? height;
  final double? width;
  final Color? color;
  final BoxFit? fit;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return SvgPicture.asset(
      asset,
      height: height,
      width: width,
      colorFilter: ColorFilter.mode(
          color ??
              (isDarkTheme ? AppColors.darkPrimary : AppColors.lightPrimary),
          BlendMode.srcIn),
      fit: fit ?? BoxFit.contain,
    );
  }
}
