import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/utility/skeleton.dart';

class InfoListItem extends StatelessWidget {
  const InfoListItem({
    super.key,
    required this.name,
    required this.value,
    this.isLoading,
  });

  final String name;
  final String value;
  final bool? isLoading;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final isLarge = value.length > 20;

    if (value.isEmpty) {
      return SizedBox();
    }

    if (isLarge) {
      return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Skeleton(
          isEnabled: isLoading ?? false,
          withStroke: false,
          child: Text(
            name,
            style: Fonts.labelSmall.merge(
              TextStyle(
                fontFamily: Fonts.mono,
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
                fontVariations: [FontVariation.weight(450)],
              ),
            ),
          ),
        ),
        const SizedBox(height: 2.0),
        Skeleton(
          isEnabled: isLoading ?? false,
          withStroke: false,
          child: Text(
            value,
            style: Fonts.labelSmall.merge(
              const TextStyle(
                fontVariations: [FontVariation.weight(450)],
              ),
            ),
          ),
        ),
        SizedBox(height: 4.0),
      ]);
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Baseline(
              baseline: 16.0,
              baselineType: TextBaseline.alphabetic,
              child: Skeleton(
                isEnabled: isLoading ?? false,
                withStroke: false,
                child: Text(
                  name,
                  style: Fonts.labelSmall.merge(
                    TextStyle(
                      fontFamily: Fonts.mono,
                      color: isDarkTheme
                          ? AppColors.darkDescription
                          : AppColors.lightDescription,
                      fontVariations: [FontVariation.weight(450)],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 6.0),
            Expanded(
              child: Baseline(
                baseline: 16.0,
                baselineType: TextBaseline.alphabetic,
                child: DottedLine(
                  dashRadius: 10.0,
                  dashColor: isDarkTheme
                      ? AppColors.darkStroke
                      : AppColors.lightStroke,
                ),
              ),
            ),
            const SizedBox(width: 6.0),
            Baseline(
              baseline: 16.0,
              baselineType: TextBaseline.alphabetic,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: constraints.maxWidth * 0.6,
                ),
                child: Skeleton(
                  isEnabled: isLoading ?? false,
                  withStroke: false,
                  child: Text(
                    value,
                    softWrap: true,
                    // maxLines: 3,
                    textAlign: TextAlign.right,
                    style: Fonts.labelSmall.merge(
                      const TextStyle(
                        fontVariations: [FontVariation.weight(450)],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
