import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:sphere/shared/styles/colors.dart';

class Skeleton extends StatelessWidget {
  const Skeleton({
    super.key,
    this.isEnabled = true,
    required this.child,
    this.baseColor,
    this.highlightColor,
    this.borderRadius,
    this.padding = const EdgeInsets.all(2.0),
    this.withStroke = false,
  });

  final bool isEnabled;
  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;
  final double? borderRadius;
  final EdgeInsetsGeometry padding;
  final bool? withStroke;

  @override
  Widget build(BuildContext context) {
    if (!isEnabled) return child;

    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final innerBaseColor =
        isDarkTheme ? AppColors.darkSurface : AppColors.lightSurface;
    final innerHighlightColor =
        isDarkTheme ? AppColors.darkBackground : AppColors.lightBackground;

    return Stack(
      children: [
        // Optimize: Use conditional widget instead of conditional padding
        isEnabled ? Padding(padding: padding, child: child) : child,
        if (isEnabled)
          Positioned.fill(
            child: Container(
              clipBehavior: Clip.none,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(borderRadius ?? 8.0),
                border: withStroke == true
                    ? Border.all(
                        color: isDarkTheme
                            ? AppColors.darkStroke.withValues(alpha: 0.5)
                            : AppColors.lightStroke.withValues(alpha: 0.5),
                      )
                    : Border.all(width: 0, color: Colors.transparent),
              ),
              child: Shimmer.fromColors(
                period: const Duration(milliseconds: 600),
                baseColor: baseColor ?? innerBaseColor,
                highlightColor: highlightColor ?? innerHighlightColor,
                child: Container(
                  clipBehavior: Clip.none,
                  decoration: BoxDecoration(
                    color: baseColor ?? innerBaseColor,
                    borderRadius: BorderRadius.circular(borderRadius ?? 8.0),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
