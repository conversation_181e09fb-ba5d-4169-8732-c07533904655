import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/fonts.dart';

class TooltipSpan extends WidgetSpan {
  TooltipSpan({
    required String message,
    required InlineSpan inlineSpan,
  }) : super(
          child: Tooltip(
            message: message,
            textStyle: Fonts.labelSmall,
            child: Text.rich(
              inlineSpan,
            ),
          ),
        );
}
