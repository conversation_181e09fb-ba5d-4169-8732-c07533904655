import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class InfoBlock extends StatelessWidget {
  const InfoBlock({
    super.key,
    required this.icon,
    required this.text,
  });

  final String icon;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      SVG(icon),
      Text(
        text,
        style: Fonts.bodyMedium,
      )
    ]);
  }
}
