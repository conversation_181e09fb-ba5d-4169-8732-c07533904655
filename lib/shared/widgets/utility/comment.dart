import 'package:flutter/material.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/tasks/data/models/comment/comment.dart';
import 'package:sphere/features/user/presentation/user_card_body.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';

class Comment extends StatelessWidget {
  const Comment({super.key, required this.data});

  final CommentModel data;

  @override
  Widget build(BuildContext context) {
    // print(data);

    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return CustomCard(
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        SelectableText(
          data.comment ?? '*Пустой комментарий*',
          style: Fonts.labelSmall,
        ),
        // SelectableText(
        //   data.userId ?? '*User ID*',
        //   style: Fonts.bodySmall,
        // ),
        SizedBox(height: 12.0),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (data.user != null)
              UserCardBody(
                minified: true,
                user: data.user,
              ),
            SizedBox(width: 12.0),
            Text(
              getDateString(data.updatedAt ?? DateTime(2000)),
              style: Fonts.bodySmall.merge(TextStyle(
                color: isDarkTheme
                    ? AppColors.darkDescription
                    : AppColors.lightDescription,
              )),
            ),
          ],
        ),
      ]),
    );
  }
}
