import 'package:flutter/material.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/shared/widgets/utility/info_list_item.dart';

class InfoList extends StatelessWidget {
  const InfoList({
    super.key,
    this.project,
    this.product,
    this.isLoading,
  });

  final ProjectModel? project;
  final ProductModel? product;
  final bool? isLoading;

  List<Map<String, String>> _generateInfoList() {
    if (isLoading == true) {
      return [
        {'name': 'Загрузка...', 'value': 'value'},
        {'name': 'Подождите', 'value': 'value123'},
      ];
    }

    if (project != null) {
      return [
        if (project!.createdAt != null)
          {
            'name': 'Дата создания',
            'value': getDateString(project!.createdAt!),
          },
        if (project!.releaseDate != null)
          {
            'name': 'Дата реализации проекта',
            'value': getDateString(project!.releaseDate!),
          },
        if (project!.client?.name != null)
          {'name': 'Заказчик', 'value': project!.client!.name!},
        // if (project!.parameters?.quantity != null)
        //   {'name': 'Количество', 'value': '${project!.parameters!.quantity}'},
        if (project!.parameters?.length != null)
          {'name': 'Длина', 'value': '${project!.parameters!.length} мм'},
        if (project!.parameters?.width != null)
          {'name': 'Ширина', 'value': '${project!.parameters!.width} мм'},
        if (project!.parameters?.width != null)
          {'name': 'Высота', 'value': '${project!.parameters!.height} мм'},
        if (project!.parameters?.mass != null)
          {'name': 'Масса (1шт)', 'value': '${project!.parameters!.mass!} кг'},
        if (project!.parameters?.mass != null &&
            project!.parameters?.quantity != null)
          {
            'name': 'Масса (общая)',
            'value':
                '${(project!.parameters!.mass!) * (project!.parameters!.quantity!)} кг',
          },
        // if (project!.parameters?.featureType != null)
        //   {
        //     'name': 'Признак',
        //     'value': project!.parameters!.featureType!.getName(),
        //   },
        if (project!.parameters?.itemRequirements != null)
          {
            'name': 'Требования к проекту',
            'value': project!.parameters!.itemRequirements!,
          },
        if (project!.parameters?.material?.name != null)
          {
            'name': 'Материал',
            'value': project!.parameters!.material!.name!,
          },
        if (project!.parameters?.materialRequirements != null)
          {
            'name': 'Требования к материалу',
            'value': project!.parameters!.materialRequirements!,
          },
      ];
    }

    if (product?.parameters != null) {
      return [
        if (product!.createdAt != null)
          {
            'name': 'Дата создания',
            'value': getDateString(product!.createdAt!)
          },
        if (product!.parameters?.releaseDate != null)
          {
            'name': 'Плановая дата выпуска ГП',
            'value': getDateString(product!.parameters!.releaseDate!),
          },
        if (product!.parameters?.quantity != null)
          {
            'name': 'Количество',
            'value':
                '${product!.parameters!.quantity} ${(product?.parameters?.unitType ?? UnitType.pcs).getName()}'
          },
        if (product!.parameters?.length != null)
          {'name': 'Длина', 'value': '${product!.parameters!.length} мм'},
        if (product!.parameters?.width != null)
          {'name': 'Ширина', 'value': '${product!.parameters!.width} мм'},
        if (product!.parameters?.height != null)
          {'name': 'Высота', 'value': '${product!.parameters!.height} мм'},
        if (product!.parameters?.mass != null)
          {'name': 'Масса (1шт)', 'value': '${product!.parameters!.mass!} кг'},
        if (product!.parameters?.mass != null &&
            product!.parameters?.quantity != null)
          {
            'name': 'Масса (общая)',
            'value':
                '${(product!.parameters!.mass!) * (product!.parameters!.quantity!)} кг',
          },
        if (product!.parameters?.pureMass != null)
          {
            'name': 'Чистая масса',
            'value': '${product!.parameters!.pureMass!} кг',
          },
        if (product!.parameters?.featureTypes != null)
          {
            'name': 'Признаки',
            'value': product!.parameters!.featureTypes!
                .map((e) => e.getName())
                .join(', '),
          },
        if (product!.parameters?.itemRequirements != null)
          {
            'name': 'Требования к заготовке',
            'value': product!.parameters!.itemRequirements!,
          },
        if (product!.parameters?.surfaceCoating != null)
          {
            'name': 'Покрытие',
            'value': product!.parameters!.surfaceCoating!,
          },
        if (product!.parameters?.material?.name != null)
          {
            'name': 'Материал',
            'value': product!.parameters!.material!.name!,
          },
        if (product!.parameters?.materialRequirements != null)
          {
            'name': 'Требования к материалу',
            'value': product!.parameters!.materialRequirements!,
          },
        if (product!.parameters?.note != null)
          {
            'name': 'Примечание',
            'value': product!.parameters!.note!,
          },
        // if (product!.parameters?.materialQuantity != null)
        //   {
        //     'name': 'Количество',
        //     'value':
        //         '${product!.parameters!.materialQuantity} ${product?.parameters?.unitType?.getName()}'
        //   }
      ];
    }

    return [];
  }

  @override
  Widget build(BuildContext context) {
    final infoList = _generateInfoList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: infoList.asMap().entries.map((entry) {
        final item = entry.value;
        final index = entry.key;

        return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          InfoListItem(
            isLoading: isLoading,
            name: item['name']!,
            value: item['value']!,
          ),
          if (index != infoList.length - 1) const SizedBox(height: 4.0),
        ]);
      }).toList(),
    );
  }
}
