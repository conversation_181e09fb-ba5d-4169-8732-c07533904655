import 'package:flutter/material.dart';

class CustomElevatedButtonThemeData {
  static final lightTheme = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
        elevation: 0.0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        minimumSize: const Size.fromHeight(50)
        // padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
        // alignment: Alignment.center,
        ),
  );
}
