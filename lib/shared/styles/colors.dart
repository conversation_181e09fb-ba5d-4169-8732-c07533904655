import 'package:flutter/material.dart';

class AppColors {
  // --- Light colors ---
  static const lightPrimary = Color(0xFF000000);
  static const lightSecondary = Color(0xFF0045FD);
  static const lightError = Color(0xFFFF0000);
  static const lightWarning = Color(0xFFFF8800);
  static const lightSuccess = Color(0xFF00F245);
  static const lightPurple = Color(0xFFAA44FF);
  static const lightBackground = Color(0xFFFFFFFF);
  static const lightHover = Color(0x33FFFFFF);
  static const lightSurface = Color(0xFFF8F8F8);
  static const lightStroke = Color(0xFFEEEEEE);
  static const lightAccentStroke = Color(0xFFCCCCCC);
  static const lightDescription = Color(0x54000000);

  // --- Dark colors ---
  static const darkPrimary = Color(0xFFFFFFFF);
  static const darkSecondary = Color(0xFF0045FD);
  static const darkError = Color(0xFFFF0000);
  static const darkWarning = Color(0xFFFF8800);
  static const darkSuccess = Color(0xFF00F245);
  static const darkPurple = Color(0xFFAA44FF);
  static const darkBackground = Color(0xFF000000);
  static const darkHover = Color(0x33111111);
  static const darkSurface = Color(0xFF111111);
  static const darkStroke = Color(0xFF080808);
  static const darkAccentStroke = Color(0xFF222222);
  static const darkDescription = Color(0x54FFFFFF);

  // --- Universal ---
  static const medium = Color(0xFF808080);
}

class AppColorsSchemes {
  static const lightColorSheme = ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.lightPrimary,
    onPrimary: AppColors.lightBackground,
    secondary: AppColors.lightSecondary,
    onSecondary: AppColors.lightBackground,
    error: AppColors.lightError,
    onError: AppColors.lightBackground,
    surface: AppColors.lightSurface,
    onSurface: AppColors.lightPrimary,
  );

  static const darkColorSheme = ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.darkPrimary,
    onPrimary: AppColors.darkBackground,
    secondary: AppColors.darkSecondary,
    onSecondary: AppColors.darkPrimary,
    error: AppColors.darkError,
    onError: AppColors.darkPrimary,
    surface: AppColors.darkSurface,
    onSurface: AppColors.darkPrimary,
  );
}
