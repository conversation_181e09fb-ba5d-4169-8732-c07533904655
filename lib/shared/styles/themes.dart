import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/styles/widgets_themes/elevated_button.dart';
import 'package:sphere/shared/styles/widgets_themes/popup_menu.dart';
import 'package:sphere/shared/styles/widgets_themes/text_field.dart';

class AppThemes {
  static final lightTheme = ThemeData(
    scaffoldBackgroundColor: AppColors.lightBackground,
    colorScheme: AppColorsSchemes.lightColorSheme,
    hoverColor: AppColors.medium.withValues(alpha: 0.075),
    splashColor: AppColors.medium.withValues(alpha: 0.125),
    highlightColor: AppColors.medium.withValues(alpha: 0.125),

    // widgets
    inputDecorationTheme: CustomTextFieldTheme.lightTheme,
    elevatedButtonTheme: CustomElevatedButtonThemeData.lightTheme,
    popupMenuTheme: CustomPopupMenuThemeData.lightTheme,
    dividerTheme: const DividerThemeData(
      color: AppColors.lightStroke,
    ),
    tooltipTheme: TooltipThemeData(
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      textStyle: Fonts.bodySmall.merge(TextStyle(
        letterSpacing: -0.5,
        color: AppColors.lightPrimary.withValues(alpha: 0.8),
      )),
      decoration: BoxDecoration(
        color: AppColors.lightBackground.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: AppColors.lightStroke,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.lightDescription.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      showDuration: const Duration(milliseconds: 200),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.lightBackground;
        } else if (states.contains(WidgetState.disabled)) {
          return AppColors.medium;
        } else if (states.contains(WidgetState.selected)) {
          return AppColors.lightBackground;
        }
        return AppColors.lightPrimary;
      }),
    ),
  );

  static final darkTheme = ThemeData(
    scaffoldBackgroundColor: AppColors.darkBackground,
    colorScheme: AppColorsSchemes.darkColorSheme,
    hoverColor: AppColors.medium.withValues(alpha: 0.05),
    splashColor: AppColors.medium.withValues(alpha: 0.10),
    highlightColor: AppColors.medium.withValues(alpha: 0.10),

    // widgets
    inputDecorationTheme: CustomTextFieldTheme.lightTheme,
    elevatedButtonTheme: CustomElevatedButtonThemeData.lightTheme,
    popupMenuTheme: CustomPopupMenuThemeData.darkTheme,
    dividerTheme: const DividerThemeData(
      color: AppColors.darkStroke,
    ),
    tooltipTheme: TooltipThemeData(
      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
      textStyle: Fonts.bodySmall.merge(TextStyle(
        letterSpacing: -0.5,
        color: AppColors.darkDescription.withValues(alpha: 0.8),
      )),
      decoration: BoxDecoration(
        color: AppColors.darkBackground.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: AppColors.darkStroke,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.darkDescription.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      showDuration: const Duration(milliseconds: 200),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.darkBackground;
        } else if (states.contains(WidgetState.disabled)) {
          return AppColors.medium;
        } else if (states.contains(WidgetState.selected)) {
          return AppColors.darkBackground;
        }
        return AppColors.darkPrimary;
      }),
    ),
  );
}
