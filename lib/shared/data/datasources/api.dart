import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';
import 'package:sphere/core/helpers/context_service.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

class API {
  static final dio = Dio()
    ..options.validateStatus = (status) {
      // Allow Dio to handle statuses from 200 to <500
      return status != null && status >= 200 && status < 500;
    };

  // development / production
  static final mode = dotenv.get('MODE');

  static final developmentURL = dotenv.get('API_URL_DEVELOPMENT');
  static final productionURL = dotenv.get('API_URL_PRODUCTION');

  static final postfix = dotenv.get('API_POSTFIX');

  static final secretAPI = dotenv.get('SECRET_API_KEY');

  static String getBaseURL() {
    return mode == 'development'
        ? '$developmentURL$postfix'
        : '$productionURL$postfix';
  }

  static Future<Response<T>> request<T>({
    String url = '/',
    Object body = const {},
    String method = 'GET',
    String authorization = '',
    T Function(Map<String, dynamic>)? fromJson,
    Options? options,
    BuildContext? context,
  }) async {
    final logger = Logger(printer: PrettyPrinter());
    try {
      const prefs = FlutterSecureStorage();
      final accessToken = await prefs.read(key: 'accessToken') ?? authorization;

      // Log request details
      logger.i('--- INPUT ---');
      logger.d(
          'URL: ${getBaseURL() + url} \nSECRET_API: $secretAPI\nBODY: ${body.toString()}\nACCESS_TOKEN: $accessToken');

      // Perform the request
      final result = await dio.request(
        '${getBaseURL()}$url',
        data: body,
        options: Options(
          method: options?.method ?? method,
          contentType: options?.contentType ?? 'application/json',
          headers: options?.headers ??
              {
                'Authorization': 'Bearer $accessToken',
                'SECRET_API': secretAPI,
              },
          responseType: options?.responseType ?? ResponseType.json,
        ),
      );

      // Log response details
      logger.i('--- OUTPUT ---');
      logger.d('Status: ${result.statusCode}');
      logger.d('Headers: ${result.headers}');
      if (options?.responseType == ResponseType.bytes) {
        logger.d(
            'Binary data received, length: ${(result.data as Uint8List).length} bytes');
      } else {
        logger.d(result.data);
      }

      // Handle JSON responses only
      final globalContext = NavigationService.navigatorKey.currentContext;
      if (options?.responseType != ResponseType.bytes &&
          result.data is Map<String, dynamic>) {
        if (result.data['message'] != null) {
          ScaffoldMessenger.of(context ?? globalContext!).showSnackBar(
            CustomSnackbar(
              text: '${result.statusCode}: ${result.data['message']}',
              type: (result.statusCode ?? 0) >= 400
                  ? SnackBarType.error
                  : SnackBarType.common,
            ).toSnackBar(context ?? globalContext!),
          );
        }
      }

      // Return response
      return Response(
        data: options?.responseType == ResponseType.bytes
            ? result.data as T // Return Uint8List for binary responses
            : fromJson?.call(result.data) ?? result.data as T,
        requestOptions: result.requestOptions,
        statusCode: result.statusCode,
        statusMessage: result.statusMessage,
      );
    } catch (e) {
      logger.e(e);
      return Response(
        requestOptions: RequestOptions(),
        statusCode: 500,
        statusMessage: e.toString(),
      );
    }
  }
}
