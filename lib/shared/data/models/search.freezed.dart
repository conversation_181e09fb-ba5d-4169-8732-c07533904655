// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'search.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SearchModel _$SearchModelFromJson(Map<String, dynamic> json) {
  return _SearchModel.fromJson(json);
}

/// @nodoc
mixin _$SearchModel {
  SearchFiltersModel? get filters => throw _privateConstructorUsedError;
  SearchPaginationModel? get pagination => throw _privateConstructorUsedError;
  SearchSortModel? get sort => throw _privateConstructorUsedError;

  /// Serializes this SearchModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SearchModelCopyWith<SearchModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchModelCopyWith<$Res> {
  factory $SearchModelCopyWith(
          SearchModel value, $Res Function(SearchModel) then) =
      _$SearchModelCopyWithImpl<$Res, SearchModel>;
  @useResult
  $Res call(
      {SearchFiltersModel? filters,
      SearchPaginationModel? pagination,
      SearchSortModel? sort});

  $SearchFiltersModelCopyWith<$Res>? get filters;
  $SearchPaginationModelCopyWith<$Res>? get pagination;
  $SearchSortModelCopyWith<$Res>? get sort;
}

/// @nodoc
class _$SearchModelCopyWithImpl<$Res, $Val extends SearchModel>
    implements $SearchModelCopyWith<$Res> {
  _$SearchModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = freezed,
    Object? pagination = freezed,
    Object? sort = freezed,
  }) {
    return _then(_value.copyWith(
      filters: freezed == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as SearchFiltersModel?,
      pagination: freezed == pagination
          ? _value.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as SearchPaginationModel?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as SearchSortModel?,
    ) as $Val);
  }

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SearchFiltersModelCopyWith<$Res>? get filters {
    if (_value.filters == null) {
      return null;
    }

    return $SearchFiltersModelCopyWith<$Res>(_value.filters!, (value) {
      return _then(_value.copyWith(filters: value) as $Val);
    });
  }

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SearchPaginationModelCopyWith<$Res>? get pagination {
    if (_value.pagination == null) {
      return null;
    }

    return $SearchPaginationModelCopyWith<$Res>(_value.pagination!, (value) {
      return _then(_value.copyWith(pagination: value) as $Val);
    });
  }

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SearchSortModelCopyWith<$Res>? get sort {
    if (_value.sort == null) {
      return null;
    }

    return $SearchSortModelCopyWith<$Res>(_value.sort!, (value) {
      return _then(_value.copyWith(sort: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SearchModelImplCopyWith<$Res>
    implements $SearchModelCopyWith<$Res> {
  factory _$$SearchModelImplCopyWith(
          _$SearchModelImpl value, $Res Function(_$SearchModelImpl) then) =
      __$$SearchModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SearchFiltersModel? filters,
      SearchPaginationModel? pagination,
      SearchSortModel? sort});

  @override
  $SearchFiltersModelCopyWith<$Res>? get filters;
  @override
  $SearchPaginationModelCopyWith<$Res>? get pagination;
  @override
  $SearchSortModelCopyWith<$Res>? get sort;
}

/// @nodoc
class __$$SearchModelImplCopyWithImpl<$Res>
    extends _$SearchModelCopyWithImpl<$Res, _$SearchModelImpl>
    implements _$$SearchModelImplCopyWith<$Res> {
  __$$SearchModelImplCopyWithImpl(
      _$SearchModelImpl _value, $Res Function(_$SearchModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = freezed,
    Object? pagination = freezed,
    Object? sort = freezed,
  }) {
    return _then(_$SearchModelImpl(
      filters: freezed == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as SearchFiltersModel?,
      pagination: freezed == pagination
          ? _value.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as SearchPaginationModel?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as SearchSortModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SearchModelImpl implements _SearchModel {
  const _$SearchModelImpl({this.filters, this.pagination, this.sort});

  factory _$SearchModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchModelImplFromJson(json);

  @override
  final SearchFiltersModel? filters;
  @override
  final SearchPaginationModel? pagination;
  @override
  final SearchSortModel? sort;

  @override
  String toString() {
    return 'SearchModel(filters: $filters, pagination: $pagination, sort: $sort)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchModelImpl &&
            (identical(other.filters, filters) || other.filters == filters) &&
            (identical(other.pagination, pagination) ||
                other.pagination == pagination) &&
            (identical(other.sort, sort) || other.sort == sort));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, filters, pagination, sort);

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchModelImplCopyWith<_$SearchModelImpl> get copyWith =>
      __$$SearchModelImplCopyWithImpl<_$SearchModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchModelImplToJson(
      this,
    );
  }
}

abstract class _SearchModel implements SearchModel {
  const factory _SearchModel(
      {final SearchFiltersModel? filters,
      final SearchPaginationModel? pagination,
      final SearchSortModel? sort}) = _$SearchModelImpl;

  factory _SearchModel.fromJson(Map<String, dynamic> json) =
      _$SearchModelImpl.fromJson;

  @override
  SearchFiltersModel? get filters;
  @override
  SearchPaginationModel? get pagination;
  @override
  SearchSortModel? get sort;

  /// Create a copy of SearchModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchModelImplCopyWith<_$SearchModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SearchFiltersModel _$SearchFiltersModelFromJson(Map<String, dynamic> json) {
  return _SearchFiltersModel.fromJson(json);
}

/// @nodoc
mixin _$SearchFiltersModel {
  String? get query => throw _privateConstructorUsedError;
  String? get branchId => throw _privateConstructorUsedError;
  Department? get department => throw _privateConstructorUsedError;
  DocumentDepartment? get documentDepartment =>
      throw _privateConstructorUsedError; // project
  List<String>? get projectIds => throw _privateConstructorUsedError;
  ProjectStatus? get status => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError; // product
  List<String>? get productIds => throw _privateConstructorUsedError;
  bool? get root => throw _privateConstructorUsedError;
  ProductType? get type => throw _privateConstructorUsedError;
  String? get parentProductId => throw _privateConstructorUsedError; // user
  List<UserRole>? get roles => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError; // documents
  bool? get bluePrint => throw _privateConstructorUsedError;
  bool? get isFolder => throw _privateConstructorUsedError;
  String? get extension => throw _privateConstructorUsedError;
  String? get parentFolderId => throw _privateConstructorUsedError;
  int? get version =>
      throw _privateConstructorUsedError; // nomenclatures / storage
  String? get materialId => throw _privateConstructorUsedError;
  StorageType? get storageType => throw _privateConstructorUsedError;
  NomenclatureType? get materialType => throw _privateConstructorUsedError;
  UnitType? get baseUnit => throw _privateConstructorUsedError;
  bool? get visible => throw _privateConstructorUsedError;
  bool? get remainingMaterials => throw _privateConstructorUsedError; // tasks
  String? get workerId => throw _privateConstructorUsedError;
  TaskStatus? get taskStatus => throw _privateConstructorUsedError;
  DateTime? get fromDate => throw _privateConstructorUsedError;
  DateTime? get toDate => throw _privateConstructorUsedError; // clients
  ClientType? get clientType =>
      throw _privateConstructorUsedError; // deliveries
  String? get provisionId => throw _privateConstructorUsedError;
  List<DeliveryStatus>? get deliveryStatus =>
      throw _privateConstructorUsedError; // provisions
  ProvisionsFilter? get filterType =>
      throw _privateConstructorUsedError; // Fields from JSON
  @JsonKey(name: 'dateFrom')
  DateTime? get dateFrom => throw _privateConstructorUsedError;
  @JsonKey(name: 'dateTo')
  DateTime? get dateTo => throw _privateConstructorUsedError;
  String? get drawingNumber => throw _privateConstructorUsedError;
  List<String>? get drawingNumbers => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  List<String>? get names => throw _privateConstructorUsedError;
  String? get material => throw _privateConstructorUsedError;
  List<String>? get materials => throw _privateConstructorUsedError;
  ParametersFeatureType? get feature => throw _privateConstructorUsedError;
  List<ParametersFeatureType>? get features =>
      throw _privateConstructorUsedError;
  double? get massFrom => throw _privateConstructorUsedError;
  double? get massTo => throw _privateConstructorUsedError;
  int? get quantityFrom => throw _privateConstructorUsedError;
  int? get quantityTo => throw _privateConstructorUsedError;
  double? get totalMassFrom => throw _privateConstructorUsedError;
  double? get totalMassTo => throw _privateConstructorUsedError;
  String? get requirement => throw _privateConstructorUsedError;
  List<String>? get requirements => throw _privateConstructorUsedError;
  String? get materialRequirement => throw _privateConstructorUsedError;
  List<String>? get materialRequirements => throw _privateConstructorUsedError;
  int? get priority => throw _privateConstructorUsedError;
  List<int>? get priorities => throw _privateConstructorUsedError;
  String? get responsiblePerson => throw _privateConstructorUsedError;
  List<String>? get responsiblePersons => throw _privateConstructorUsedError;
  @JsonKey(name: 'taskDateFrom')
  DateTime? get taskDateFrom => throw _privateConstructorUsedError;
  @JsonKey(name: 'taskDateTo')
  DateTime? get taskDateTo => throw _privateConstructorUsedError;
  List<SupplyStatus>? get supplyStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'plannedContractDateFrom')
  DateTime? get plannedContractDateFrom => throw _privateConstructorUsedError;
  @JsonKey(name: 'plannedContractDateTo')
  DateTime? get plannedContractDateTo => throw _privateConstructorUsedError;
  List<int>? get lotNumbers =>
      throw _privateConstructorUsedError; // Changed to List<int> to match JSON
  List<String>? get lotNames => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  List<String>? get contractNumbers => throw _privateConstructorUsedError;
  String? get supplier => throw _privateConstructorUsedError;
  List<String>? get suppliers => throw _privateConstructorUsedError;
  @JsonKey(name: 'deliveryDateFrom')
  DateTime? get deliveryDateFrom => throw _privateConstructorUsedError;
  @JsonKey(name: 'deliveryDateTo')
  DateTime? get deliveryDateTo => throw _privateConstructorUsedError;
  double? get costFrom => throw _privateConstructorUsedError;
  double? get costTo => throw _privateConstructorUsedError;
  List<String>? get lotIds => throw _privateConstructorUsedError;
  List<String>? get contractIds => throw _privateConstructorUsedError;
  String? get parentName => throw _privateConstructorUsedError;
  List<String>? get parentNames => throw _privateConstructorUsedError;

  /// Serializes this SearchFiltersModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SearchFiltersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SearchFiltersModelCopyWith<SearchFiltersModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchFiltersModelCopyWith<$Res> {
  factory $SearchFiltersModelCopyWith(
          SearchFiltersModel value, $Res Function(SearchFiltersModel) then) =
      _$SearchFiltersModelCopyWithImpl<$Res, SearchFiltersModel>;
  @useResult
  $Res call(
      {String? query,
      String? branchId,
      Department? department,
      DocumentDepartment? documentDepartment,
      List<String>? projectIds,
      ProjectStatus? status,
      String? userId,
      List<String>? productIds,
      bool? root,
      ProductType? type,
      String? parentProductId,
      List<UserRole>? roles,
      String? projectId,
      String? productId,
      bool? bluePrint,
      bool? isFolder,
      String? extension,
      String? parentFolderId,
      int? version,
      String? materialId,
      StorageType? storageType,
      NomenclatureType? materialType,
      UnitType? baseUnit,
      bool? visible,
      bool? remainingMaterials,
      String? workerId,
      TaskStatus? taskStatus,
      DateTime? fromDate,
      DateTime? toDate,
      ClientType? clientType,
      String? provisionId,
      List<DeliveryStatus>? deliveryStatus,
      ProvisionsFilter? filterType,
      @JsonKey(name: 'dateFrom') DateTime? dateFrom,
      @JsonKey(name: 'dateTo') DateTime? dateTo,
      String? drawingNumber,
      List<String>? drawingNumbers,
      String? name,
      List<String>? names,
      String? material,
      List<String>? materials,
      ParametersFeatureType? feature,
      List<ParametersFeatureType>? features,
      double? massFrom,
      double? massTo,
      int? quantityFrom,
      int? quantityTo,
      double? totalMassFrom,
      double? totalMassTo,
      String? requirement,
      List<String>? requirements,
      String? materialRequirement,
      List<String>? materialRequirements,
      int? priority,
      List<int>? priorities,
      String? responsiblePerson,
      List<String>? responsiblePersons,
      @JsonKey(name: 'taskDateFrom') DateTime? taskDateFrom,
      @JsonKey(name: 'taskDateTo') DateTime? taskDateTo,
      List<SupplyStatus>? supplyStatus,
      @JsonKey(name: 'plannedContractDateFrom')
      DateTime? plannedContractDateFrom,
      @JsonKey(name: 'plannedContractDateTo') DateTime? plannedContractDateTo,
      List<int>? lotNumbers,
      List<String>? lotNames,
      String? contractNumber,
      List<String>? contractNumbers,
      String? supplier,
      List<String>? suppliers,
      @JsonKey(name: 'deliveryDateFrom') DateTime? deliveryDateFrom,
      @JsonKey(name: 'deliveryDateTo') DateTime? deliveryDateTo,
      double? costFrom,
      double? costTo,
      List<String>? lotIds,
      List<String>? contractIds,
      String? parentName,
      List<String>? parentNames});
}

/// @nodoc
class _$SearchFiltersModelCopyWithImpl<$Res, $Val extends SearchFiltersModel>
    implements $SearchFiltersModelCopyWith<$Res> {
  _$SearchFiltersModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchFiltersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = freezed,
    Object? branchId = freezed,
    Object? department = freezed,
    Object? documentDepartment = freezed,
    Object? projectIds = freezed,
    Object? status = freezed,
    Object? userId = freezed,
    Object? productIds = freezed,
    Object? root = freezed,
    Object? type = freezed,
    Object? parentProductId = freezed,
    Object? roles = freezed,
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? bluePrint = freezed,
    Object? isFolder = freezed,
    Object? extension = freezed,
    Object? parentFolderId = freezed,
    Object? version = freezed,
    Object? materialId = freezed,
    Object? storageType = freezed,
    Object? materialType = freezed,
    Object? baseUnit = freezed,
    Object? visible = freezed,
    Object? remainingMaterials = freezed,
    Object? workerId = freezed,
    Object? taskStatus = freezed,
    Object? fromDate = freezed,
    Object? toDate = freezed,
    Object? clientType = freezed,
    Object? provisionId = freezed,
    Object? deliveryStatus = freezed,
    Object? filterType = freezed,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? drawingNumber = freezed,
    Object? drawingNumbers = freezed,
    Object? name = freezed,
    Object? names = freezed,
    Object? material = freezed,
    Object? materials = freezed,
    Object? feature = freezed,
    Object? features = freezed,
    Object? massFrom = freezed,
    Object? massTo = freezed,
    Object? quantityFrom = freezed,
    Object? quantityTo = freezed,
    Object? totalMassFrom = freezed,
    Object? totalMassTo = freezed,
    Object? requirement = freezed,
    Object? requirements = freezed,
    Object? materialRequirement = freezed,
    Object? materialRequirements = freezed,
    Object? priority = freezed,
    Object? priorities = freezed,
    Object? responsiblePerson = freezed,
    Object? responsiblePersons = freezed,
    Object? taskDateFrom = freezed,
    Object? taskDateTo = freezed,
    Object? supplyStatus = freezed,
    Object? plannedContractDateFrom = freezed,
    Object? plannedContractDateTo = freezed,
    Object? lotNumbers = freezed,
    Object? lotNames = freezed,
    Object? contractNumber = freezed,
    Object? contractNumbers = freezed,
    Object? supplier = freezed,
    Object? suppliers = freezed,
    Object? deliveryDateFrom = freezed,
    Object? deliveryDateTo = freezed,
    Object? costFrom = freezed,
    Object? costTo = freezed,
    Object? lotIds = freezed,
    Object? contractIds = freezed,
    Object? parentName = freezed,
    Object? parentNames = freezed,
  }) {
    return _then(_value.copyWith(
      query: freezed == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String?,
      branchId: freezed == branchId
          ? _value.branchId
          : branchId // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      documentDepartment: freezed == documentDepartment
          ? _value.documentDepartment
          : documentDepartment // ignore: cast_nullable_to_non_nullable
              as DocumentDepartment?,
      projectIds: freezed == projectIds
          ? _value.projectIds
          : projectIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProjectStatus?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      productIds: freezed == productIds
          ? _value.productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      root: freezed == root
          ? _value.root
          : root // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ProductType?,
      parentProductId: freezed == parentProductId
          ? _value.parentProductId
          : parentProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      roles: freezed == roles
          ? _value.roles
          : roles // ignore: cast_nullable_to_non_nullable
              as List<UserRole>?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      bluePrint: freezed == bluePrint
          ? _value.bluePrint
          : bluePrint // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFolder: freezed == isFolder
          ? _value.isFolder
          : isFolder // ignore: cast_nullable_to_non_nullable
              as bool?,
      extension: freezed == extension
          ? _value.extension
          : extension // ignore: cast_nullable_to_non_nullable
              as String?,
      parentFolderId: freezed == parentFolderId
          ? _value.parentFolderId
          : parentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      storageType: freezed == storageType
          ? _value.storageType
          : storageType // ignore: cast_nullable_to_non_nullable
              as StorageType?,
      materialType: freezed == materialType
          ? _value.materialType
          : materialType // ignore: cast_nullable_to_non_nullable
              as NomenclatureType?,
      baseUnit: freezed == baseUnit
          ? _value.baseUnit
          : baseUnit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      visible: freezed == visible
          ? _value.visible
          : visible // ignore: cast_nullable_to_non_nullable
              as bool?,
      remainingMaterials: freezed == remainingMaterials
          ? _value.remainingMaterials
          : remainingMaterials // ignore: cast_nullable_to_non_nullable
              as bool?,
      workerId: freezed == workerId
          ? _value.workerId
          : workerId // ignore: cast_nullable_to_non_nullable
              as String?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as TaskStatus?,
      fromDate: freezed == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toDate: freezed == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      clientType: freezed == clientType
          ? _value.clientType
          : clientType // ignore: cast_nullable_to_non_nullable
              as ClientType?,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryStatus: freezed == deliveryStatus
          ? _value.deliveryStatus
          : deliveryStatus // ignore: cast_nullable_to_non_nullable
              as List<DeliveryStatus>?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      drawingNumber: freezed == drawingNumber
          ? _value.drawingNumber
          : drawingNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      drawingNumbers: freezed == drawingNumbers
          ? _value.drawingNumbers
          : drawingNumbers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      names: freezed == names
          ? _value.names
          : names // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as String?,
      materials: freezed == materials
          ? _value.materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      feature: freezed == feature
          ? _value.feature
          : feature // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      features: freezed == features
          ? _value.features
          : features // ignore: cast_nullable_to_non_nullable
              as List<ParametersFeatureType>?,
      massFrom: freezed == massFrom
          ? _value.massFrom
          : massFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      massTo: freezed == massTo
          ? _value.massTo
          : massTo // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityFrom: freezed == quantityFrom
          ? _value.quantityFrom
          : quantityFrom // ignore: cast_nullable_to_non_nullable
              as int?,
      quantityTo: freezed == quantityTo
          ? _value.quantityTo
          : quantityTo // ignore: cast_nullable_to_non_nullable
              as int?,
      totalMassFrom: freezed == totalMassFrom
          ? _value.totalMassFrom
          : totalMassFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      totalMassTo: freezed == totalMassTo
          ? _value.totalMassTo
          : totalMassTo // ignore: cast_nullable_to_non_nullable
              as double?,
      requirement: freezed == requirement
          ? _value.requirement
          : requirement // ignore: cast_nullable_to_non_nullable
              as String?,
      requirements: freezed == requirements
          ? _value.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      materialRequirement: freezed == materialRequirement
          ? _value.materialRequirement
          : materialRequirement // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      priorities: freezed == priorities
          ? _value.priorities
          : priorities // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      responsiblePerson: freezed == responsiblePerson
          ? _value.responsiblePerson
          : responsiblePerson // ignore: cast_nullable_to_non_nullable
              as String?,
      responsiblePersons: freezed == responsiblePersons
          ? _value.responsiblePersons
          : responsiblePersons // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      taskDateFrom: freezed == taskDateFrom
          ? _value.taskDateFrom
          : taskDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      taskDateTo: freezed == taskDateTo
          ? _value.taskDateTo
          : taskDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      supplyStatus: freezed == supplyStatus
          ? _value.supplyStatus
          : supplyStatus // ignore: cast_nullable_to_non_nullable
              as List<SupplyStatus>?,
      plannedContractDateFrom: freezed == plannedContractDateFrom
          ? _value.plannedContractDateFrom
          : plannedContractDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      plannedContractDateTo: freezed == plannedContractDateTo
          ? _value.plannedContractDateTo
          : plannedContractDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lotNumbers: freezed == lotNumbers
          ? _value.lotNumbers
          : lotNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      lotNames: freezed == lotNames
          ? _value.lotNames
          : lotNames // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractNumbers: freezed == contractNumbers
          ? _value.contractNumbers
          : contractNumbers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as String?,
      suppliers: freezed == suppliers
          ? _value.suppliers
          : suppliers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      deliveryDateFrom: freezed == deliveryDateFrom
          ? _value.deliveryDateFrom
          : deliveryDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateTo: freezed == deliveryDateTo
          ? _value.deliveryDateTo
          : deliveryDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      costFrom: freezed == costFrom
          ? _value.costFrom
          : costFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      costTo: freezed == costTo
          ? _value.costTo
          : costTo // ignore: cast_nullable_to_non_nullable
              as double?,
      lotIds: freezed == lotIds
          ? _value.lotIds
          : lotIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractIds: freezed == contractIds
          ? _value.contractIds
          : contractIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      parentName: freezed == parentName
          ? _value.parentName
          : parentName // ignore: cast_nullable_to_non_nullable
              as String?,
      parentNames: freezed == parentNames
          ? _value.parentNames
          : parentNames // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchFiltersModelImplCopyWith<$Res>
    implements $SearchFiltersModelCopyWith<$Res> {
  factory _$$SearchFiltersModelImplCopyWith(_$SearchFiltersModelImpl value,
          $Res Function(_$SearchFiltersModelImpl) then) =
      __$$SearchFiltersModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? query,
      String? branchId,
      Department? department,
      DocumentDepartment? documentDepartment,
      List<String>? projectIds,
      ProjectStatus? status,
      String? userId,
      List<String>? productIds,
      bool? root,
      ProductType? type,
      String? parentProductId,
      List<UserRole>? roles,
      String? projectId,
      String? productId,
      bool? bluePrint,
      bool? isFolder,
      String? extension,
      String? parentFolderId,
      int? version,
      String? materialId,
      StorageType? storageType,
      NomenclatureType? materialType,
      UnitType? baseUnit,
      bool? visible,
      bool? remainingMaterials,
      String? workerId,
      TaskStatus? taskStatus,
      DateTime? fromDate,
      DateTime? toDate,
      ClientType? clientType,
      String? provisionId,
      List<DeliveryStatus>? deliveryStatus,
      ProvisionsFilter? filterType,
      @JsonKey(name: 'dateFrom') DateTime? dateFrom,
      @JsonKey(name: 'dateTo') DateTime? dateTo,
      String? drawingNumber,
      List<String>? drawingNumbers,
      String? name,
      List<String>? names,
      String? material,
      List<String>? materials,
      ParametersFeatureType? feature,
      List<ParametersFeatureType>? features,
      double? massFrom,
      double? massTo,
      int? quantityFrom,
      int? quantityTo,
      double? totalMassFrom,
      double? totalMassTo,
      String? requirement,
      List<String>? requirements,
      String? materialRequirement,
      List<String>? materialRequirements,
      int? priority,
      List<int>? priorities,
      String? responsiblePerson,
      List<String>? responsiblePersons,
      @JsonKey(name: 'taskDateFrom') DateTime? taskDateFrom,
      @JsonKey(name: 'taskDateTo') DateTime? taskDateTo,
      List<SupplyStatus>? supplyStatus,
      @JsonKey(name: 'plannedContractDateFrom')
      DateTime? plannedContractDateFrom,
      @JsonKey(name: 'plannedContractDateTo') DateTime? plannedContractDateTo,
      List<int>? lotNumbers,
      List<String>? lotNames,
      String? contractNumber,
      List<String>? contractNumbers,
      String? supplier,
      List<String>? suppliers,
      @JsonKey(name: 'deliveryDateFrom') DateTime? deliveryDateFrom,
      @JsonKey(name: 'deliveryDateTo') DateTime? deliveryDateTo,
      double? costFrom,
      double? costTo,
      List<String>? lotIds,
      List<String>? contractIds,
      String? parentName,
      List<String>? parentNames});
}

/// @nodoc
class __$$SearchFiltersModelImplCopyWithImpl<$Res>
    extends _$SearchFiltersModelCopyWithImpl<$Res, _$SearchFiltersModelImpl>
    implements _$$SearchFiltersModelImplCopyWith<$Res> {
  __$$SearchFiltersModelImplCopyWithImpl(_$SearchFiltersModelImpl _value,
      $Res Function(_$SearchFiltersModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchFiltersModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = freezed,
    Object? branchId = freezed,
    Object? department = freezed,
    Object? documentDepartment = freezed,
    Object? projectIds = freezed,
    Object? status = freezed,
    Object? userId = freezed,
    Object? productIds = freezed,
    Object? root = freezed,
    Object? type = freezed,
    Object? parentProductId = freezed,
    Object? roles = freezed,
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? bluePrint = freezed,
    Object? isFolder = freezed,
    Object? extension = freezed,
    Object? parentFolderId = freezed,
    Object? version = freezed,
    Object? materialId = freezed,
    Object? storageType = freezed,
    Object? materialType = freezed,
    Object? baseUnit = freezed,
    Object? visible = freezed,
    Object? remainingMaterials = freezed,
    Object? workerId = freezed,
    Object? taskStatus = freezed,
    Object? fromDate = freezed,
    Object? toDate = freezed,
    Object? clientType = freezed,
    Object? provisionId = freezed,
    Object? deliveryStatus = freezed,
    Object? filterType = freezed,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? drawingNumber = freezed,
    Object? drawingNumbers = freezed,
    Object? name = freezed,
    Object? names = freezed,
    Object? material = freezed,
    Object? materials = freezed,
    Object? feature = freezed,
    Object? features = freezed,
    Object? massFrom = freezed,
    Object? massTo = freezed,
    Object? quantityFrom = freezed,
    Object? quantityTo = freezed,
    Object? totalMassFrom = freezed,
    Object? totalMassTo = freezed,
    Object? requirement = freezed,
    Object? requirements = freezed,
    Object? materialRequirement = freezed,
    Object? materialRequirements = freezed,
    Object? priority = freezed,
    Object? priorities = freezed,
    Object? responsiblePerson = freezed,
    Object? responsiblePersons = freezed,
    Object? taskDateFrom = freezed,
    Object? taskDateTo = freezed,
    Object? supplyStatus = freezed,
    Object? plannedContractDateFrom = freezed,
    Object? plannedContractDateTo = freezed,
    Object? lotNumbers = freezed,
    Object? lotNames = freezed,
    Object? contractNumber = freezed,
    Object? contractNumbers = freezed,
    Object? supplier = freezed,
    Object? suppliers = freezed,
    Object? deliveryDateFrom = freezed,
    Object? deliveryDateTo = freezed,
    Object? costFrom = freezed,
    Object? costTo = freezed,
    Object? lotIds = freezed,
    Object? contractIds = freezed,
    Object? parentName = freezed,
    Object? parentNames = freezed,
  }) {
    return _then(_$SearchFiltersModelImpl(
      query: freezed == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String?,
      branchId: freezed == branchId
          ? _value.branchId
          : branchId // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      documentDepartment: freezed == documentDepartment
          ? _value.documentDepartment
          : documentDepartment // ignore: cast_nullable_to_non_nullable
              as DocumentDepartment?,
      projectIds: freezed == projectIds
          ? _value._projectIds
          : projectIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProjectStatus?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      productIds: freezed == productIds
          ? _value._productIds
          : productIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      root: freezed == root
          ? _value.root
          : root // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ProductType?,
      parentProductId: freezed == parentProductId
          ? _value.parentProductId
          : parentProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      roles: freezed == roles
          ? _value._roles
          : roles // ignore: cast_nullable_to_non_nullable
              as List<UserRole>?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      bluePrint: freezed == bluePrint
          ? _value.bluePrint
          : bluePrint // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFolder: freezed == isFolder
          ? _value.isFolder
          : isFolder // ignore: cast_nullable_to_non_nullable
              as bool?,
      extension: freezed == extension
          ? _value.extension
          : extension // ignore: cast_nullable_to_non_nullable
              as String?,
      parentFolderId: freezed == parentFolderId
          ? _value.parentFolderId
          : parentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int?,
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      storageType: freezed == storageType
          ? _value.storageType
          : storageType // ignore: cast_nullable_to_non_nullable
              as StorageType?,
      materialType: freezed == materialType
          ? _value.materialType
          : materialType // ignore: cast_nullable_to_non_nullable
              as NomenclatureType?,
      baseUnit: freezed == baseUnit
          ? _value.baseUnit
          : baseUnit // ignore: cast_nullable_to_non_nullable
              as UnitType?,
      visible: freezed == visible
          ? _value.visible
          : visible // ignore: cast_nullable_to_non_nullable
              as bool?,
      remainingMaterials: freezed == remainingMaterials
          ? _value.remainingMaterials
          : remainingMaterials // ignore: cast_nullable_to_non_nullable
              as bool?,
      workerId: freezed == workerId
          ? _value.workerId
          : workerId // ignore: cast_nullable_to_non_nullable
              as String?,
      taskStatus: freezed == taskStatus
          ? _value.taskStatus
          : taskStatus // ignore: cast_nullable_to_non_nullable
              as TaskStatus?,
      fromDate: freezed == fromDate
          ? _value.fromDate
          : fromDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toDate: freezed == toDate
          ? _value.toDate
          : toDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      clientType: freezed == clientType
          ? _value.clientType
          : clientType // ignore: cast_nullable_to_non_nullable
              as ClientType?,
      provisionId: freezed == provisionId
          ? _value.provisionId
          : provisionId // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryStatus: freezed == deliveryStatus
          ? _value._deliveryStatus
          : deliveryStatus // ignore: cast_nullable_to_non_nullable
              as List<DeliveryStatus>?,
      filterType: freezed == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as ProvisionsFilter?,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      drawingNumber: freezed == drawingNumber
          ? _value.drawingNumber
          : drawingNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      drawingNumbers: freezed == drawingNumbers
          ? _value._drawingNumbers
          : drawingNumbers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      names: freezed == names
          ? _value._names
          : names // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as String?,
      materials: freezed == materials
          ? _value._materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      feature: freezed == feature
          ? _value.feature
          : feature // ignore: cast_nullable_to_non_nullable
              as ParametersFeatureType?,
      features: freezed == features
          ? _value._features
          : features // ignore: cast_nullable_to_non_nullable
              as List<ParametersFeatureType>?,
      massFrom: freezed == massFrom
          ? _value.massFrom
          : massFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      massTo: freezed == massTo
          ? _value.massTo
          : massTo // ignore: cast_nullable_to_non_nullable
              as double?,
      quantityFrom: freezed == quantityFrom
          ? _value.quantityFrom
          : quantityFrom // ignore: cast_nullable_to_non_nullable
              as int?,
      quantityTo: freezed == quantityTo
          ? _value.quantityTo
          : quantityTo // ignore: cast_nullable_to_non_nullable
              as int?,
      totalMassFrom: freezed == totalMassFrom
          ? _value.totalMassFrom
          : totalMassFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      totalMassTo: freezed == totalMassTo
          ? _value.totalMassTo
          : totalMassTo // ignore: cast_nullable_to_non_nullable
              as double?,
      requirement: freezed == requirement
          ? _value.requirement
          : requirement // ignore: cast_nullable_to_non_nullable
              as String?,
      requirements: freezed == requirements
          ? _value._requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      materialRequirement: freezed == materialRequirement
          ? _value.materialRequirement
          : materialRequirement // ignore: cast_nullable_to_non_nullable
              as String?,
      materialRequirements: freezed == materialRequirements
          ? _value._materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int?,
      priorities: freezed == priorities
          ? _value._priorities
          : priorities // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      responsiblePerson: freezed == responsiblePerson
          ? _value.responsiblePerson
          : responsiblePerson // ignore: cast_nullable_to_non_nullable
              as String?,
      responsiblePersons: freezed == responsiblePersons
          ? _value._responsiblePersons
          : responsiblePersons // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      taskDateFrom: freezed == taskDateFrom
          ? _value.taskDateFrom
          : taskDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      taskDateTo: freezed == taskDateTo
          ? _value.taskDateTo
          : taskDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      supplyStatus: freezed == supplyStatus
          ? _value._supplyStatus
          : supplyStatus // ignore: cast_nullable_to_non_nullable
              as List<SupplyStatus>?,
      plannedContractDateFrom: freezed == plannedContractDateFrom
          ? _value.plannedContractDateFrom
          : plannedContractDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      plannedContractDateTo: freezed == plannedContractDateTo
          ? _value.plannedContractDateTo
          : plannedContractDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lotNumbers: freezed == lotNumbers
          ? _value._lotNumbers
          : lotNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      lotNames: freezed == lotNames
          ? _value._lotNames
          : lotNames // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contractNumbers: freezed == contractNumbers
          ? _value._contractNumbers
          : contractNumbers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      supplier: freezed == supplier
          ? _value.supplier
          : supplier // ignore: cast_nullable_to_non_nullable
              as String?,
      suppliers: freezed == suppliers
          ? _value._suppliers
          : suppliers // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      deliveryDateFrom: freezed == deliveryDateFrom
          ? _value.deliveryDateFrom
          : deliveryDateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      deliveryDateTo: freezed == deliveryDateTo
          ? _value.deliveryDateTo
          : deliveryDateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      costFrom: freezed == costFrom
          ? _value.costFrom
          : costFrom // ignore: cast_nullable_to_non_nullable
              as double?,
      costTo: freezed == costTo
          ? _value.costTo
          : costTo // ignore: cast_nullable_to_non_nullable
              as double?,
      lotIds: freezed == lotIds
          ? _value._lotIds
          : lotIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      contractIds: freezed == contractIds
          ? _value._contractIds
          : contractIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      parentName: freezed == parentName
          ? _value.parentName
          : parentName // ignore: cast_nullable_to_non_nullable
              as String?,
      parentNames: freezed == parentNames
          ? _value._parentNames
          : parentNames // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SearchFiltersModelImpl implements _SearchFiltersModel {
  const _$SearchFiltersModelImpl(
      {this.query,
      this.branchId,
      this.department,
      this.documentDepartment,
      final List<String>? projectIds,
      this.status,
      this.userId,
      final List<String>? productIds,
      this.root,
      this.type,
      this.parentProductId,
      final List<UserRole>? roles,
      this.projectId,
      this.productId,
      this.bluePrint,
      this.isFolder,
      this.extension,
      this.parentFolderId,
      this.version,
      this.materialId,
      this.storageType,
      this.materialType,
      this.baseUnit,
      this.visible,
      this.remainingMaterials,
      this.workerId,
      this.taskStatus,
      this.fromDate,
      this.toDate,
      this.clientType,
      this.provisionId,
      final List<DeliveryStatus>? deliveryStatus,
      this.filterType,
      @JsonKey(name: 'dateFrom') this.dateFrom,
      @JsonKey(name: 'dateTo') this.dateTo,
      this.drawingNumber,
      final List<String>? drawingNumbers,
      this.name,
      final List<String>? names,
      this.material,
      final List<String>? materials,
      this.feature,
      final List<ParametersFeatureType>? features,
      this.massFrom,
      this.massTo,
      this.quantityFrom,
      this.quantityTo,
      this.totalMassFrom,
      this.totalMassTo,
      this.requirement,
      final List<String>? requirements,
      this.materialRequirement,
      final List<String>? materialRequirements,
      this.priority,
      final List<int>? priorities,
      this.responsiblePerson,
      final List<String>? responsiblePersons,
      @JsonKey(name: 'taskDateFrom') this.taskDateFrom,
      @JsonKey(name: 'taskDateTo') this.taskDateTo,
      final List<SupplyStatus>? supplyStatus,
      @JsonKey(name: 'plannedContractDateFrom') this.plannedContractDateFrom,
      @JsonKey(name: 'plannedContractDateTo') this.plannedContractDateTo,
      final List<int>? lotNumbers,
      final List<String>? lotNames,
      this.contractNumber,
      final List<String>? contractNumbers,
      this.supplier,
      final List<String>? suppliers,
      @JsonKey(name: 'deliveryDateFrom') this.deliveryDateFrom,
      @JsonKey(name: 'deliveryDateTo') this.deliveryDateTo,
      this.costFrom,
      this.costTo,
      final List<String>? lotIds,
      final List<String>? contractIds,
      this.parentName,
      final List<String>? parentNames})
      : _projectIds = projectIds,
        _productIds = productIds,
        _roles = roles,
        _deliveryStatus = deliveryStatus,
        _drawingNumbers = drawingNumbers,
        _names = names,
        _materials = materials,
        _features = features,
        _requirements = requirements,
        _materialRequirements = materialRequirements,
        _priorities = priorities,
        _responsiblePersons = responsiblePersons,
        _supplyStatus = supplyStatus,
        _lotNumbers = lotNumbers,
        _lotNames = lotNames,
        _contractNumbers = contractNumbers,
        _suppliers = suppliers,
        _lotIds = lotIds,
        _contractIds = contractIds,
        _parentNames = parentNames;

  factory _$SearchFiltersModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchFiltersModelImplFromJson(json);

  @override
  final String? query;
  @override
  final String? branchId;
  @override
  final Department? department;
  @override
  final DocumentDepartment? documentDepartment;
// project
  final List<String>? _projectIds;
// project
  @override
  List<String>? get projectIds {
    final value = _projectIds;
    if (value == null) return null;
    if (_projectIds is EqualUnmodifiableListView) return _projectIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ProjectStatus? status;
  @override
  final String? userId;
// product
  final List<String>? _productIds;
// product
  @override
  List<String>? get productIds {
    final value = _productIds;
    if (value == null) return null;
    if (_productIds is EqualUnmodifiableListView) return _productIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? root;
  @override
  final ProductType? type;
  @override
  final String? parentProductId;
// user
  final List<UserRole>? _roles;
// user
  @override
  List<UserRole>? get roles {
    final value = _roles;
    if (value == null) return null;
    if (_roles is EqualUnmodifiableListView) return _roles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? projectId;
  @override
  final String? productId;
// documents
  @override
  final bool? bluePrint;
  @override
  final bool? isFolder;
  @override
  final String? extension;
  @override
  final String? parentFolderId;
  @override
  final int? version;
// nomenclatures / storage
  @override
  final String? materialId;
  @override
  final StorageType? storageType;
  @override
  final NomenclatureType? materialType;
  @override
  final UnitType? baseUnit;
  @override
  final bool? visible;
  @override
  final bool? remainingMaterials;
// tasks
  @override
  final String? workerId;
  @override
  final TaskStatus? taskStatus;
  @override
  final DateTime? fromDate;
  @override
  final DateTime? toDate;
// clients
  @override
  final ClientType? clientType;
// deliveries
  @override
  final String? provisionId;
  final List<DeliveryStatus>? _deliveryStatus;
  @override
  List<DeliveryStatus>? get deliveryStatus {
    final value = _deliveryStatus;
    if (value == null) return null;
    if (_deliveryStatus is EqualUnmodifiableListView) return _deliveryStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// provisions
  @override
  final ProvisionsFilter? filterType;
// Fields from JSON
  @override
  @JsonKey(name: 'dateFrom')
  final DateTime? dateFrom;
  @override
  @JsonKey(name: 'dateTo')
  final DateTime? dateTo;
  @override
  final String? drawingNumber;
  final List<String>? _drawingNumbers;
  @override
  List<String>? get drawingNumbers {
    final value = _drawingNumbers;
    if (value == null) return null;
    if (_drawingNumbers is EqualUnmodifiableListView) return _drawingNumbers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? name;
  final List<String>? _names;
  @override
  List<String>? get names {
    final value = _names;
    if (value == null) return null;
    if (_names is EqualUnmodifiableListView) return _names;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? material;
  final List<String>? _materials;
  @override
  List<String>? get materials {
    final value = _materials;
    if (value == null) return null;
    if (_materials is EqualUnmodifiableListView) return _materials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ParametersFeatureType? feature;
  final List<ParametersFeatureType>? _features;
  @override
  List<ParametersFeatureType>? get features {
    final value = _features;
    if (value == null) return null;
    if (_features is EqualUnmodifiableListView) return _features;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final double? massFrom;
  @override
  final double? massTo;
  @override
  final int? quantityFrom;
  @override
  final int? quantityTo;
  @override
  final double? totalMassFrom;
  @override
  final double? totalMassTo;
  @override
  final String? requirement;
  final List<String>? _requirements;
  @override
  List<String>? get requirements {
    final value = _requirements;
    if (value == null) return null;
    if (_requirements is EqualUnmodifiableListView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? materialRequirement;
  final List<String>? _materialRequirements;
  @override
  List<String>? get materialRequirements {
    final value = _materialRequirements;
    if (value == null) return null;
    if (_materialRequirements is EqualUnmodifiableListView)
      return _materialRequirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? priority;
  final List<int>? _priorities;
  @override
  List<int>? get priorities {
    final value = _priorities;
    if (value == null) return null;
    if (_priorities is EqualUnmodifiableListView) return _priorities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? responsiblePerson;
  final List<String>? _responsiblePersons;
  @override
  List<String>? get responsiblePersons {
    final value = _responsiblePersons;
    if (value == null) return null;
    if (_responsiblePersons is EqualUnmodifiableListView)
      return _responsiblePersons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'taskDateFrom')
  final DateTime? taskDateFrom;
  @override
  @JsonKey(name: 'taskDateTo')
  final DateTime? taskDateTo;
  final List<SupplyStatus>? _supplyStatus;
  @override
  List<SupplyStatus>? get supplyStatus {
    final value = _supplyStatus;
    if (value == null) return null;
    if (_supplyStatus is EqualUnmodifiableListView) return _supplyStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'plannedContractDateFrom')
  final DateTime? plannedContractDateFrom;
  @override
  @JsonKey(name: 'plannedContractDateTo')
  final DateTime? plannedContractDateTo;
  final List<int>? _lotNumbers;
  @override
  List<int>? get lotNumbers {
    final value = _lotNumbers;
    if (value == null) return null;
    if (_lotNumbers is EqualUnmodifiableListView) return _lotNumbers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Changed to List<int> to match JSON
  final List<String>? _lotNames;
// Changed to List<int> to match JSON
  @override
  List<String>? get lotNames {
    final value = _lotNames;
    if (value == null) return null;
    if (_lotNames is EqualUnmodifiableListView) return _lotNames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? contractNumber;
  final List<String>? _contractNumbers;
  @override
  List<String>? get contractNumbers {
    final value = _contractNumbers;
    if (value == null) return null;
    if (_contractNumbers is EqualUnmodifiableListView) return _contractNumbers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? supplier;
  final List<String>? _suppliers;
  @override
  List<String>? get suppliers {
    final value = _suppliers;
    if (value == null) return null;
    if (_suppliers is EqualUnmodifiableListView) return _suppliers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'deliveryDateFrom')
  final DateTime? deliveryDateFrom;
  @override
  @JsonKey(name: 'deliveryDateTo')
  final DateTime? deliveryDateTo;
  @override
  final double? costFrom;
  @override
  final double? costTo;
  final List<String>? _lotIds;
  @override
  List<String>? get lotIds {
    final value = _lotIds;
    if (value == null) return null;
    if (_lotIds is EqualUnmodifiableListView) return _lotIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _contractIds;
  @override
  List<String>? get contractIds {
    final value = _contractIds;
    if (value == null) return null;
    if (_contractIds is EqualUnmodifiableListView) return _contractIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? parentName;
  final List<String>? _parentNames;
  @override
  List<String>? get parentNames {
    final value = _parentNames;
    if (value == null) return null;
    if (_parentNames is EqualUnmodifiableListView) return _parentNames;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SearchFiltersModel(query: $query, branchId: $branchId, department: $department, documentDepartment: $documentDepartment, projectIds: $projectIds, status: $status, userId: $userId, productIds: $productIds, root: $root, type: $type, parentProductId: $parentProductId, roles: $roles, projectId: $projectId, productId: $productId, bluePrint: $bluePrint, isFolder: $isFolder, extension: $extension, parentFolderId: $parentFolderId, version: $version, materialId: $materialId, storageType: $storageType, materialType: $materialType, baseUnit: $baseUnit, visible: $visible, remainingMaterials: $remainingMaterials, workerId: $workerId, taskStatus: $taskStatus, fromDate: $fromDate, toDate: $toDate, clientType: $clientType, provisionId: $provisionId, deliveryStatus: $deliveryStatus, filterType: $filterType, dateFrom: $dateFrom, dateTo: $dateTo, drawingNumber: $drawingNumber, drawingNumbers: $drawingNumbers, name: $name, names: $names, material: $material, materials: $materials, feature: $feature, features: $features, massFrom: $massFrom, massTo: $massTo, quantityFrom: $quantityFrom, quantityTo: $quantityTo, totalMassFrom: $totalMassFrom, totalMassTo: $totalMassTo, requirement: $requirement, requirements: $requirements, materialRequirement: $materialRequirement, materialRequirements: $materialRequirements, priority: $priority, priorities: $priorities, responsiblePerson: $responsiblePerson, responsiblePersons: $responsiblePersons, taskDateFrom: $taskDateFrom, taskDateTo: $taskDateTo, supplyStatus: $supplyStatus, plannedContractDateFrom: $plannedContractDateFrom, plannedContractDateTo: $plannedContractDateTo, lotNumbers: $lotNumbers, lotNames: $lotNames, contractNumber: $contractNumber, contractNumbers: $contractNumbers, supplier: $supplier, suppliers: $suppliers, deliveryDateFrom: $deliveryDateFrom, deliveryDateTo: $deliveryDateTo, costFrom: $costFrom, costTo: $costTo, lotIds: $lotIds, contractIds: $contractIds, parentName: $parentName, parentNames: $parentNames)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchFiltersModelImpl &&
            (identical(other.query, query) || other.query == query) &&
            (identical(other.branchId, branchId) ||
                other.branchId == branchId) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.documentDepartment, documentDepartment) ||
                other.documentDepartment == documentDepartment) &&
            const DeepCollectionEquality()
                .equals(other._projectIds, _projectIds) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality()
                .equals(other._productIds, _productIds) &&
            (identical(other.root, root) || other.root == root) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.parentProductId, parentProductId) ||
                other.parentProductId == parentProductId) &&
            const DeepCollectionEquality().equals(other._roles, _roles) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.bluePrint, bluePrint) ||
                other.bluePrint == bluePrint) &&
            (identical(other.isFolder, isFolder) ||
                other.isFolder == isFolder) &&
            (identical(other.extension, extension) ||
                other.extension == extension) &&
            (identical(other.parentFolderId, parentFolderId) ||
                other.parentFolderId == parentFolderId) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.storageType, storageType) ||
                other.storageType == storageType) &&
            (identical(other.materialType, materialType) ||
                other.materialType == materialType) &&
            (identical(other.baseUnit, baseUnit) ||
                other.baseUnit == baseUnit) &&
            (identical(other.visible, visible) || other.visible == visible) &&
            (identical(other.remainingMaterials, remainingMaterials) ||
                other.remainingMaterials == remainingMaterials) &&
            (identical(other.workerId, workerId) ||
                other.workerId == workerId) &&
            (identical(other.taskStatus, taskStatus) ||
                other.taskStatus == taskStatus) &&
            (identical(other.fromDate, fromDate) ||
                other.fromDate == fromDate) &&
            (identical(other.toDate, toDate) || other.toDate == toDate) &&
            (identical(other.clientType, clientType) ||
                other.clientType == clientType) &&
            (identical(other.provisionId, provisionId) ||
                other.provisionId == provisionId) &&
            const DeepCollectionEquality()
                .equals(other._deliveryStatus, _deliveryStatus) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo) &&
            (identical(other.drawingNumber, drawingNumber) ||
                other.drawingNumber == drawingNumber) &&
            const DeepCollectionEquality()
                .equals(other._drawingNumbers, _drawingNumbers) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._names, _names) &&
            (identical(other.material, material) ||
                other.material == material) &&
            const DeepCollectionEquality()
                .equals(other._materials, _materials) &&
            (identical(other.feature, feature) || other.feature == feature) &&
            const DeepCollectionEquality().equals(other._features, _features) &&
            (identical(other.massFrom, massFrom) ||
                other.massFrom == massFrom) &&
            (identical(other.massTo, massTo) || other.massTo == massTo) &&
            (identical(other.quantityFrom, quantityFrom) ||
                other.quantityFrom == quantityFrom) &&
            (identical(other.quantityTo, quantityTo) ||
                other.quantityTo == quantityTo) &&
            (identical(other.totalMassFrom, totalMassFrom) ||
                other.totalMassFrom == totalMassFrom) &&
            (identical(other.totalMassTo, totalMassTo) ||
                other.totalMassTo == totalMassTo) &&
            (identical(other.requirement, requirement) ||
                other.requirement == requirement) &&
            const DeepCollectionEquality()
                .equals(other._requirements, _requirements) &&
            (identical(other.materialRequirement, materialRequirement) ||
                other.materialRequirement == materialRequirement) &&
            const DeepCollectionEquality()
                .equals(other._materialRequirements, _materialRequirements) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            const DeepCollectionEquality()
                .equals(other._priorities, _priorities) &&
            (identical(other.responsiblePerson, responsiblePerson) ||
                other.responsiblePerson == responsiblePerson) &&
            const DeepCollectionEquality()
                .equals(other._responsiblePersons, _responsiblePersons) &&
            (identical(other.taskDateFrom, taskDateFrom) ||
                other.taskDateFrom == taskDateFrom) &&
            (identical(other.taskDateTo, taskDateTo) || other.taskDateTo == taskDateTo) &&
            const DeepCollectionEquality().equals(other._supplyStatus, _supplyStatus) &&
            (identical(other.plannedContractDateFrom, plannedContractDateFrom) || other.plannedContractDateFrom == plannedContractDateFrom) &&
            (identical(other.plannedContractDateTo, plannedContractDateTo) || other.plannedContractDateTo == plannedContractDateTo) &&
            const DeepCollectionEquality().equals(other._lotNumbers, _lotNumbers) &&
            const DeepCollectionEquality().equals(other._lotNames, _lotNames) &&
            (identical(other.contractNumber, contractNumber) || other.contractNumber == contractNumber) &&
            const DeepCollectionEquality().equals(other._contractNumbers, _contractNumbers) &&
            (identical(other.supplier, supplier) || other.supplier == supplier) &&
            const DeepCollectionEquality().equals(other._suppliers, _suppliers) &&
            (identical(other.deliveryDateFrom, deliveryDateFrom) || other.deliveryDateFrom == deliveryDateFrom) &&
            (identical(other.deliveryDateTo, deliveryDateTo) || other.deliveryDateTo == deliveryDateTo) &&
            (identical(other.costFrom, costFrom) || other.costFrom == costFrom) &&
            (identical(other.costTo, costTo) || other.costTo == costTo) &&
            const DeepCollectionEquality().equals(other._lotIds, _lotIds) &&
            const DeepCollectionEquality().equals(other._contractIds, _contractIds) &&
            (identical(other.parentName, parentName) || other.parentName == parentName) &&
            const DeepCollectionEquality().equals(other._parentNames, _parentNames));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        query,
        branchId,
        department,
        documentDepartment,
        const DeepCollectionEquality().hash(_projectIds),
        status,
        userId,
        const DeepCollectionEquality().hash(_productIds),
        root,
        type,
        parentProductId,
        const DeepCollectionEquality().hash(_roles),
        projectId,
        productId,
        bluePrint,
        isFolder,
        extension,
        parentFolderId,
        version,
        materialId,
        storageType,
        materialType,
        baseUnit,
        visible,
        remainingMaterials,
        workerId,
        taskStatus,
        fromDate,
        toDate,
        clientType,
        provisionId,
        const DeepCollectionEquality().hash(_deliveryStatus),
        filterType,
        dateFrom,
        dateTo,
        drawingNumber,
        const DeepCollectionEquality().hash(_drawingNumbers),
        name,
        const DeepCollectionEquality().hash(_names),
        material,
        const DeepCollectionEquality().hash(_materials),
        feature,
        const DeepCollectionEquality().hash(_features),
        massFrom,
        massTo,
        quantityFrom,
        quantityTo,
        totalMassFrom,
        totalMassTo,
        requirement,
        const DeepCollectionEquality().hash(_requirements),
        materialRequirement,
        const DeepCollectionEquality().hash(_materialRequirements),
        priority,
        const DeepCollectionEquality().hash(_priorities),
        responsiblePerson,
        const DeepCollectionEquality().hash(_responsiblePersons),
        taskDateFrom,
        taskDateTo,
        const DeepCollectionEquality().hash(_supplyStatus),
        plannedContractDateFrom,
        plannedContractDateTo,
        const DeepCollectionEquality().hash(_lotNumbers),
        const DeepCollectionEquality().hash(_lotNames),
        contractNumber,
        const DeepCollectionEquality().hash(_contractNumbers),
        supplier,
        const DeepCollectionEquality().hash(_suppliers),
        deliveryDateFrom,
        deliveryDateTo,
        costFrom,
        costTo,
        const DeepCollectionEquality().hash(_lotIds),
        const DeepCollectionEquality().hash(_contractIds),
        parentName,
        const DeepCollectionEquality().hash(_parentNames)
      ]);

  /// Create a copy of SearchFiltersModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchFiltersModelImplCopyWith<_$SearchFiltersModelImpl> get copyWith =>
      __$$SearchFiltersModelImplCopyWithImpl<_$SearchFiltersModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchFiltersModelImplToJson(
      this,
    );
  }
}

abstract class _SearchFiltersModel implements SearchFiltersModel {
  const factory _SearchFiltersModel(
      {final String? query,
      final String? branchId,
      final Department? department,
      final DocumentDepartment? documentDepartment,
      final List<String>? projectIds,
      final ProjectStatus? status,
      final String? userId,
      final List<String>? productIds,
      final bool? root,
      final ProductType? type,
      final String? parentProductId,
      final List<UserRole>? roles,
      final String? projectId,
      final String? productId,
      final bool? bluePrint,
      final bool? isFolder,
      final String? extension,
      final String? parentFolderId,
      final int? version,
      final String? materialId,
      final StorageType? storageType,
      final NomenclatureType? materialType,
      final UnitType? baseUnit,
      final bool? visible,
      final bool? remainingMaterials,
      final String? workerId,
      final TaskStatus? taskStatus,
      final DateTime? fromDate,
      final DateTime? toDate,
      final ClientType? clientType,
      final String? provisionId,
      final List<DeliveryStatus>? deliveryStatus,
      final ProvisionsFilter? filterType,
      @JsonKey(name: 'dateFrom') final DateTime? dateFrom,
      @JsonKey(name: 'dateTo') final DateTime? dateTo,
      final String? drawingNumber,
      final List<String>? drawingNumbers,
      final String? name,
      final List<String>? names,
      final String? material,
      final List<String>? materials,
      final ParametersFeatureType? feature,
      final List<ParametersFeatureType>? features,
      final double? massFrom,
      final double? massTo,
      final int? quantityFrom,
      final int? quantityTo,
      final double? totalMassFrom,
      final double? totalMassTo,
      final String? requirement,
      final List<String>? requirements,
      final String? materialRequirement,
      final List<String>? materialRequirements,
      final int? priority,
      final List<int>? priorities,
      final String? responsiblePerson,
      final List<String>? responsiblePersons,
      @JsonKey(name: 'taskDateFrom') final DateTime? taskDateFrom,
      @JsonKey(name: 'taskDateTo') final DateTime? taskDateTo,
      final List<SupplyStatus>? supplyStatus,
      @JsonKey(name: 'plannedContractDateFrom')
      final DateTime? plannedContractDateFrom,
      @JsonKey(name: 'plannedContractDateTo')
      final DateTime? plannedContractDateTo,
      final List<int>? lotNumbers,
      final List<String>? lotNames,
      final String? contractNumber,
      final List<String>? contractNumbers,
      final String? supplier,
      final List<String>? suppliers,
      @JsonKey(name: 'deliveryDateFrom') final DateTime? deliveryDateFrom,
      @JsonKey(name: 'deliveryDateTo') final DateTime? deliveryDateTo,
      final double? costFrom,
      final double? costTo,
      final List<String>? lotIds,
      final List<String>? contractIds,
      final String? parentName,
      final List<String>? parentNames}) = _$SearchFiltersModelImpl;

  factory _SearchFiltersModel.fromJson(Map<String, dynamic> json) =
      _$SearchFiltersModelImpl.fromJson;

  @override
  String? get query;
  @override
  String? get branchId;
  @override
  Department? get department;
  @override
  DocumentDepartment? get documentDepartment; // project
  @override
  List<String>? get projectIds;
  @override
  ProjectStatus? get status;
  @override
  String? get userId; // product
  @override
  List<String>? get productIds;
  @override
  bool? get root;
  @override
  ProductType? get type;
  @override
  String? get parentProductId; // user
  @override
  List<UserRole>? get roles;
  @override
  String? get projectId;
  @override
  String? get productId; // documents
  @override
  bool? get bluePrint;
  @override
  bool? get isFolder;
  @override
  String? get extension;
  @override
  String? get parentFolderId;
  @override
  int? get version; // nomenclatures / storage
  @override
  String? get materialId;
  @override
  StorageType? get storageType;
  @override
  NomenclatureType? get materialType;
  @override
  UnitType? get baseUnit;
  @override
  bool? get visible;
  @override
  bool? get remainingMaterials; // tasks
  @override
  String? get workerId;
  @override
  TaskStatus? get taskStatus;
  @override
  DateTime? get fromDate;
  @override
  DateTime? get toDate; // clients
  @override
  ClientType? get clientType; // deliveries
  @override
  String? get provisionId;
  @override
  List<DeliveryStatus>? get deliveryStatus; // provisions
  @override
  ProvisionsFilter? get filterType; // Fields from JSON
  @override
  @JsonKey(name: 'dateFrom')
  DateTime? get dateFrom;
  @override
  @JsonKey(name: 'dateTo')
  DateTime? get dateTo;
  @override
  String? get drawingNumber;
  @override
  List<String>? get drawingNumbers;
  @override
  String? get name;
  @override
  List<String>? get names;
  @override
  String? get material;
  @override
  List<String>? get materials;
  @override
  ParametersFeatureType? get feature;
  @override
  List<ParametersFeatureType>? get features;
  @override
  double? get massFrom;
  @override
  double? get massTo;
  @override
  int? get quantityFrom;
  @override
  int? get quantityTo;
  @override
  double? get totalMassFrom;
  @override
  double? get totalMassTo;
  @override
  String? get requirement;
  @override
  List<String>? get requirements;
  @override
  String? get materialRequirement;
  @override
  List<String>? get materialRequirements;
  @override
  int? get priority;
  @override
  List<int>? get priorities;
  @override
  String? get responsiblePerson;
  @override
  List<String>? get responsiblePersons;
  @override
  @JsonKey(name: 'taskDateFrom')
  DateTime? get taskDateFrom;
  @override
  @JsonKey(name: 'taskDateTo')
  DateTime? get taskDateTo;
  @override
  List<SupplyStatus>? get supplyStatus;
  @override
  @JsonKey(name: 'plannedContractDateFrom')
  DateTime? get plannedContractDateFrom;
  @override
  @JsonKey(name: 'plannedContractDateTo')
  DateTime? get plannedContractDateTo;
  @override
  List<int>? get lotNumbers; // Changed to List<int> to match JSON
  @override
  List<String>? get lotNames;
  @override
  String? get contractNumber;
  @override
  List<String>? get contractNumbers;
  @override
  String? get supplier;
  @override
  List<String>? get suppliers;
  @override
  @JsonKey(name: 'deliveryDateFrom')
  DateTime? get deliveryDateFrom;
  @override
  @JsonKey(name: 'deliveryDateTo')
  DateTime? get deliveryDateTo;
  @override
  double? get costFrom;
  @override
  double? get costTo;
  @override
  List<String>? get lotIds;
  @override
  List<String>? get contractIds;
  @override
  String? get parentName;
  @override
  List<String>? get parentNames;

  /// Create a copy of SearchFiltersModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchFiltersModelImplCopyWith<_$SearchFiltersModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SearchPaginationModel _$SearchPaginationModelFromJson(
    Map<String, dynamic> json) {
  return _SearchPaginationModel.fromJson(json);
}

/// @nodoc
mixin _$SearchPaginationModel {
  int? get offset => throw _privateConstructorUsedError;
  int? get limit => throw _privateConstructorUsedError;

  /// Serializes this SearchPaginationModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SearchPaginationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SearchPaginationModelCopyWith<SearchPaginationModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchPaginationModelCopyWith<$Res> {
  factory $SearchPaginationModelCopyWith(SearchPaginationModel value,
          $Res Function(SearchPaginationModel) then) =
      _$SearchPaginationModelCopyWithImpl<$Res, SearchPaginationModel>;
  @useResult
  $Res call({int? offset, int? limit});
}

/// @nodoc
class _$SearchPaginationModelCopyWithImpl<$Res,
        $Val extends SearchPaginationModel>
    implements $SearchPaginationModelCopyWith<$Res> {
  _$SearchPaginationModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchPaginationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = freezed,
    Object? limit = freezed,
  }) {
    return _then(_value.copyWith(
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchPaginationModelImplCopyWith<$Res>
    implements $SearchPaginationModelCopyWith<$Res> {
  factory _$$SearchPaginationModelImplCopyWith(
          _$SearchPaginationModelImpl value,
          $Res Function(_$SearchPaginationModelImpl) then) =
      __$$SearchPaginationModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? offset, int? limit});
}

/// @nodoc
class __$$SearchPaginationModelImplCopyWithImpl<$Res>
    extends _$SearchPaginationModelCopyWithImpl<$Res,
        _$SearchPaginationModelImpl>
    implements _$$SearchPaginationModelImplCopyWith<$Res> {
  __$$SearchPaginationModelImplCopyWithImpl(_$SearchPaginationModelImpl _value,
      $Res Function(_$SearchPaginationModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchPaginationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offset = freezed,
    Object? limit = freezed,
  }) {
    return _then(_$SearchPaginationModelImpl(
      offset: freezed == offset
          ? _value.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SearchPaginationModelImpl implements _SearchPaginationModel {
  const _$SearchPaginationModelImpl({this.offset, this.limit});

  factory _$SearchPaginationModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchPaginationModelImplFromJson(json);

  @override
  final int? offset;
  @override
  final int? limit;

  @override
  String toString() {
    return 'SearchPaginationModel(offset: $offset, limit: $limit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchPaginationModelImpl &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.limit, limit) || other.limit == limit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, offset, limit);

  /// Create a copy of SearchPaginationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchPaginationModelImplCopyWith<_$SearchPaginationModelImpl>
      get copyWith => __$$SearchPaginationModelImplCopyWithImpl<
          _$SearchPaginationModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchPaginationModelImplToJson(
      this,
    );
  }
}

abstract class _SearchPaginationModel implements SearchPaginationModel {
  const factory _SearchPaginationModel({final int? offset, final int? limit}) =
      _$SearchPaginationModelImpl;

  factory _SearchPaginationModel.fromJson(Map<String, dynamic> json) =
      _$SearchPaginationModelImpl.fromJson;

  @override
  int? get offset;
  @override
  int? get limit;

  /// Create a copy of SearchPaginationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchPaginationModelImplCopyWith<_$SearchPaginationModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SearchSortModel _$SearchSortModelFromJson(Map<String, dynamic> json) {
  return _SearchSortModel.fromJson(json);
}

/// @nodoc
mixin _$SearchSortModel {
  String? get field => throw _privateConstructorUsedError;
  String? get order => throw _privateConstructorUsedError;

  /// Serializes this SearchSortModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SearchSortModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SearchSortModelCopyWith<SearchSortModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SearchSortModelCopyWith<$Res> {
  factory $SearchSortModelCopyWith(
          SearchSortModel value, $Res Function(SearchSortModel) then) =
      _$SearchSortModelCopyWithImpl<$Res, SearchSortModel>;
  @useResult
  $Res call({String? field, String? order});
}

/// @nodoc
class _$SearchSortModelCopyWithImpl<$Res, $Val extends SearchSortModel>
    implements $SearchSortModelCopyWith<$Res> {
  _$SearchSortModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SearchSortModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? field = freezed,
    Object? order = freezed,
  }) {
    return _then(_value.copyWith(
      field: freezed == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SearchSortModelImplCopyWith<$Res>
    implements $SearchSortModelCopyWith<$Res> {
  factory _$$SearchSortModelImplCopyWith(_$SearchSortModelImpl value,
          $Res Function(_$SearchSortModelImpl) then) =
      __$$SearchSortModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? field, String? order});
}

/// @nodoc
class __$$SearchSortModelImplCopyWithImpl<$Res>
    extends _$SearchSortModelCopyWithImpl<$Res, _$SearchSortModelImpl>
    implements _$$SearchSortModelImplCopyWith<$Res> {
  __$$SearchSortModelImplCopyWithImpl(
      _$SearchSortModelImpl _value, $Res Function(_$SearchSortModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SearchSortModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? field = freezed,
    Object? order = freezed,
  }) {
    return _then(_$SearchSortModelImpl(
      field: freezed == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$SearchSortModelImpl implements _SearchSortModel {
  const _$SearchSortModelImpl({this.field, this.order});

  factory _$SearchSortModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SearchSortModelImplFromJson(json);

  @override
  final String? field;
  @override
  final String? order;

  @override
  String toString() {
    return 'SearchSortModel(field: $field, order: $order)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchSortModelImpl &&
            (identical(other.field, field) || other.field == field) &&
            (identical(other.order, order) || other.order == order));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, field, order);

  /// Create a copy of SearchSortModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchSortModelImplCopyWith<_$SearchSortModelImpl> get copyWith =>
      __$$SearchSortModelImplCopyWithImpl<_$SearchSortModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SearchSortModelImplToJson(
      this,
    );
  }
}

abstract class _SearchSortModel implements SearchSortModel {
  const factory _SearchSortModel({final String? field, final String? order}) =
      _$SearchSortModelImpl;

  factory _SearchSortModel.fromJson(Map<String, dynamic> json) =
      _$SearchSortModelImpl.fromJson;

  @override
  String? get field;
  @override
  String? get order;

  /// Create a copy of SearchSortModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchSortModelImplCopyWith<_$SearchSortModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
