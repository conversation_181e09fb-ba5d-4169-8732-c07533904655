import 'dart:io';

import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:local_notifier/local_notifier.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:sphere/core/helpers/http_overrides.dart';

import 'app.dart';

void main() async {
  // Optimize: Ensure Flutter binding is initialized first
  WidgetsFlutterBinding.ensureInitialized();

  // Optimize: Load environment variables asynchronously
  await dotenv.load(fileName: '.env');

  // Optimize: Set HTTP overrides
  HttpOverrides.global = MyHttpOverrides();

  // Optimize: Initialize OneSignal asynchronously to avoid blocking startup
  _initializeOneSignal();

  // Optimize: Run app immediately, don't wait for OneSignal
  runApp(const MyApp());

  // Optimize: Initialize desktop-specific features asynchronously
  if (!kIsWeb && (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
    _initializeDesktopFeatures();
  }
}

// Optimize: Extract OneSignal initialization to avoid blocking startup
Future<void> _initializeOneSignal() async {
  try {
    OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
    OneSignal.initialize("************************************");
    await OneSignal.Notifications.requestPermission(true);
  } catch (e) {
    // Log error but don't crash the app
    debugPrint('OneSignal initialization failed: $e');
  }
}

// Optimize: Extract desktop features initialization
Future<void> _initializeDesktopFeatures() async {
  try {
    await localNotifier.setup(
      appName: 'Сфера — Система управления проектами',
      shortcutPolicy: ShortcutPolicy.requireCreate,
    );

    doWhenWindowReady(() {
      const initialSize = Size(900, 600);
      appWindow.minSize = initialSize;
      appWindow.size = initialSize;
      appWindow.alignment = Alignment.center;
      appWindow.title = 'Сфера';
      appWindow.show();
      appWindow.maximize();
    });
  } catch (e) {
    // Log error but don't crash the app
    debugPrint('Desktop features initialization failed: $e');
  }
}
